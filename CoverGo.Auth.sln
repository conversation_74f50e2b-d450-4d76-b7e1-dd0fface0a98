﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33213.308
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{4934D5DE-7D24-4998-8195-8FC0C0A21421}"
	ProjectSection(SolutionItems) = preProject
		.dockerignore = .dockerignore
		.editorconfig = .editorconfig
		.gflows\workflow-configuration\build-publish\build-publish.settings.yml = .gflows\workflow-configuration\build-publish\build-publish.settings.yml
		.github\workflows\build-publish.yml = .github\workflows\build-publish.yml
		.gflows\config.yml = .gflows\config.yml
		DBS.Dockerfile = DBS.Dockerfile
		docker-compose.yml = docker-compose.yml
		Dockerfile = Dockerfile
		Mongo.Dockerfile = Mongo.Dockerfile
		nuget.config = nuget.config
		README.md = README.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Auth.Domain", "src\CoverGo.Auth.Domain\CoverGo.Auth.Domain.csproj", "{49095286-02A6-4823-B59A-48B46B78685A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Auth.Infrastructure", "src\CoverGo.Auth.Infrastructure\CoverGo.Auth.Infrastructure.csproj", "{EA96B437-9B75-41A4-AF61-A00F78F6CFAE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Auth.Application", "src\CoverGo.Auth.Application\CoverGo.Auth.Application.csproj", "{6DAA879A-66EE-4D56-A3EA-E7DC4A0FABCC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Auth.Client", "src\CoverGo.Auth.Client\CoverGo.Auth.Client.csproj", "{1FC6EC9B-8748-474E-BAEA-6E6E4BDC6EC5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Auth.Tests.Integration", "src\CoverGo.Auth.Tests.Integration\CoverGo.Auth.Tests.Integration.csproj", "{0DF4DC7F-EBD4-493A-A062-EC10BDB20244}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Auth.Tests.Unit", "src\CoverGo.Auth.Tests.Unit\CoverGo.Auth.Tests.Unit.csproj", "{00CCCA05-780D-463C-BB28-A1283E7AE4CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Auth.LDAP", "src\CoverGo.Auth.LDAP\CoverGo.Auth.LDAP.csproj", "{602AD3A3-A53F-4029-BE99-183DD6199572}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Auth.Tests.LDAPIntegration", "src\CoverGo.Auth.Tests.LDAPIntegration\CoverGo.Auth.Tests.LDAPIntegration.csproj", "{E578CC1F-A7FA-4FB2-9140-1A4BE1F11253}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{49095286-02A6-4823-B59A-48B46B78685A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{49095286-02A6-4823-B59A-48B46B78685A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{49095286-02A6-4823-B59A-48B46B78685A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{49095286-02A6-4823-B59A-48B46B78685A}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA96B437-9B75-41A4-AF61-A00F78F6CFAE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA96B437-9B75-41A4-AF61-A00F78F6CFAE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA96B437-9B75-41A4-AF61-A00F78F6CFAE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA96B437-9B75-41A4-AF61-A00F78F6CFAE}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DAA879A-66EE-4D56-A3EA-E7DC4A0FABCC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DAA879A-66EE-4D56-A3EA-E7DC4A0FABCC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DAA879A-66EE-4D56-A3EA-E7DC4A0FABCC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DAA879A-66EE-4D56-A3EA-E7DC4A0FABCC}.Release|Any CPU.Build.0 = Release|Any CPU
		{1FC6EC9B-8748-474E-BAEA-6E6E4BDC6EC5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1FC6EC9B-8748-474E-BAEA-6E6E4BDC6EC5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1FC6EC9B-8748-474E-BAEA-6E6E4BDC6EC5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1FC6EC9B-8748-474E-BAEA-6E6E4BDC6EC5}.Release|Any CPU.Build.0 = Release|Any CPU
		{0DF4DC7F-EBD4-493A-A062-EC10BDB20244}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0DF4DC7F-EBD4-493A-A062-EC10BDB20244}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0DF4DC7F-EBD4-493A-A062-EC10BDB20244}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0DF4DC7F-EBD4-493A-A062-EC10BDB20244}.Release|Any CPU.Build.0 = Release|Any CPU
		{00CCCA05-780D-463C-BB28-A1283E7AE4CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00CCCA05-780D-463C-BB28-A1283E7AE4CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00CCCA05-780D-463C-BB28-A1283E7AE4CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00CCCA05-780D-463C-BB28-A1283E7AE4CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{602AD3A3-A53F-4029-BE99-183DD6199572}.Debug|Any CPU.ActiveCfg = Release|Any CPU
		{602AD3A3-A53F-4029-BE99-183DD6199572}.Debug|Any CPU.Build.0 = Release|Any CPU
		{602AD3A3-A53F-4029-BE99-183DD6199572}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{602AD3A3-A53F-4029-BE99-183DD6199572}.Release|Any CPU.Build.0 = Release|Any CPU
		{E578CC1F-A7FA-4FB2-9140-1A4BE1F11253}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E578CC1F-A7FA-4FB2-9140-1A4BE1F11253}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E578CC1F-A7FA-4FB2-9140-1A4BE1F11253}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E578CC1F-A7FA-4FB2-9140-1A4BE1F11253}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {83E7AC9C-128D-4A14-88F3-B75D3180EA0D}
	EndGlobalSection
EndGlobal
