
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
COPY --from=mcr.microsoft.com/dotnet/sdk:7.0-alpine /usr/share/dotnet /usr/share/dotnet
ARG BUILDCONFIG=Debug
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app
COPY . .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

RUN dotnet restore
ARG APP_VERSION=1.0.0

RUN dotnet build -c $BUILDCONFIG /p:Version=$APP_VERSION --no-restore

RUN dotnet publish ./src/CoverGo.Auth.Application/CoverGo.Auth.Application.csproj -c $BUILDCONFIG -o ./out /p:Version=$APP_VERSION --no-restore

FROM mcr.microsoft.com/dotnet/aspnet:7.0-alpine AS runtime

ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
RUN apk add --no-cache icu-libs
ADD https://github.com/ufoscout/docker-compose-wait/releases/download/2.9.0/wait /wait
RUN chmod +x /wait

WORKDIR /app
COPY --from=build /app/out ./

RUN adduser -D buildadmin
RUN chown buildadmin:buildadmin /app /app/*
USER buildadmin

ENV ASPNETCORE_URLS http://*:8080
ARG COMMIT_SHA
ENV SENTRY_RELEASE=${COMMIT_SHA} REVISION=${COMMIT_SHA}
EXPOSE 8080
ENTRYPOINT ["/bin/sh", "-c" , "/wait && dotnet CoverGo.Auth.Application.dll"]

FROM build AS nuget
ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
RUN dotnet restore ./src/CoverGo.Auth.Client/CoverGo.Auth.Client.csproj
RUN dotnet build ./src/CoverGo.Auth.Client/CoverGo.Auth.Client.csproj -c $BUILDCONFIG --no-restore -v Minimal -p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"
RUN dotnet pack  ./src/CoverGo.Auth.Client/CoverGo.Auth.Client.csproj -c $BUILDCONFIG -o nuget --no-build -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM build AS tests
ENV AUTH_INTEGRATION_TEST-AuthUrl="http://covergo-auth:60000"
ENTRYPOINT dotnet test --collect:"XPlat Code Coverage" --no-build --verbosity normal --settings coverlet.runsettings\
  --logger:"junit;LogFileName=TestResults.{assembly}.{framework}.xml;verbosity=normal"\
  --logger:"console;verbosity=normal"

FROM mongo AS mongo-auth
COPY mongo-auth-init.js /docker-entrypoint-initdb.d/
EXPOSE 27017
