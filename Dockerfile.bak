
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build-service
COPY --from=mcr.microsoft.com/dotnet/sdk:7.0-alpine /usr/share/dotnet /usr/share/dotnet
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app

COPY ./nuget.config .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

COPY Directory.Build.props .

#copy csproj and restore as distinct layers
# optimisation from https://docs.microsoft.com/en-us/aspnet/core/host-and-deploy/docker/building-net-docker-images?view=aspnetcore-7.0
COPY ./*.sln .
COPY ./src/CoverGo.Auth.Application/*.csproj ./src/CoverGo.Auth.Application/
COPY ./src/CoverGo.Auth.Domain/*.csproj ./src/CoverGo.Auth.Domain/
COPY ./src/CoverGo.Auth.Infrastructure/*.csproj ./src/CoverGo.Auth.Infrastructure/
COPY ./src/CoverGo.Auth.LDAP/*.csproj ./src/CoverGo.Auth.LDAP/

RUN dotnet restore ./src/CoverGo.Auth.Application/CoverGo.Auth.Application.csproj
RUN dotnet restore ./src/CoverGo.Auth.Domain/CoverGo.Auth.Domain.csproj
RUN dotnet restore ./src/CoverGo.Auth.Infrastructure/CoverGo.Auth.Infrastructure.csproj
RUN dotnet restore ./src/CoverGo.Auth.LDAP/CoverGo.Auth.LDAP.csproj

COPY ./src/CoverGo.Auth.Domain ./src/CoverGo.Auth.Domain
COPY ./src/CoverGo.Auth.LDAP ./src/CoverGo.Auth.LDAP
COPY ./src/CoverGo.Auth.Infrastructure ./src/CoverGo.Auth.Infrastructure
COPY ./src/CoverGo.Auth.Application ./src/CoverGo.Auth.Application

ARG BUILDCONFIG=Release
ARG VERSION=1.0.0
ARG APP_VERSION=1.0.0
RUN dotnet publish ./src/CoverGo.Auth.Application/CoverGo.Auth.Application.csproj -c $BUILDCONFIG -o ./out /p:Version=$APP_VERSION --no-restore

FROM mcr.microsoft.com/dotnet/aspnet:7.0-alpine AS service-runtime

ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
RUN apk add --no-cache icu-libs
ADD https://github.com/ufoscout/docker-compose-wait/releases/download/2.9.0/wait /wait
RUN chmod +x /wait

WORKDIR /app
COPY --from=build-service /app/out ./

RUN adduser -D buildadmin
RUN chown buildadmin:buildadmin /app /app/*
USER buildadmin

ENV ASPNETCORE_URLS http://*:8080
ARG COMMIT_SHA
ENV SENTRY_RELEASE=${COMMIT_SHA} REVISION=${COMMIT_SHA}
EXPOSE 8080
ENTRYPOINT ["/bin/sh", "-c" , "/wait && dotnet CoverGo.Auth.Application.dll"]

FROM build-service AS build-tests-unit

COPY ./src/CoverGo.Auth.Tests.Unit/*.csproj ./src/CoverGo.Auth.Tests.Unit/
RUN dotnet restore ./src/CoverGo.Auth.Tests.Unit/CoverGo.Auth.Tests.Unit.csproj
COPY ./src/CoverGo.Auth.Tests.Unit ./src/CoverGo.Auth.Tests.Unit
RUN dotnet publish ./src/CoverGo.Auth.Tests.Unit/CoverGo.Auth.Tests.Unit.csproj -c $BUILDCONFIG -o ./out /p:Version=$VERSION --no-restore
COPY coverlet.runsettings ./

FROM build-tests-unit AS run-tests-unit
WORKDIR /app
ENTRYPOINT ["dotnet", "test","./src/CoverGo.Auth.Tests.Unit/CoverGo.Auth.Tests.Unit.csproj"]
CMD ["--nologo", "--no-restore", "--logger","junit;LogFileName=TestResults.xml","--settings", "coverlet.runsettings"]

# client itself does not need any dll from the service,
# but integration tests will need both service and client,
# so we optimse for integration tests here deriving from build-service
FROM build-service AS build-client
ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
COPY ./src/CoverGo.Auth.Client/*.csproj ./src/CoverGo.Auth.Client/
COPY ./src/CoverGo.Auth.Client ./src/CoverGo.Auth.Client/
RUN dotnet restore ./src/CoverGo.Auth.Client/CoverGo.Auth.Client.csproj
RUN dotnet build ./src/CoverGo.Auth.Client/CoverGo.Auth.Client.csproj -c $BUILDCONFIG --no-restore -v Minimal -p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM build-client AS pack-client
ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
RUN dotnet pack  ./src/CoverGo.Auth.Client/CoverGo.Auth.Client.csproj -c $BUILDCONFIG -o nuget --no-build -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM build-client AS build-tests-integration
ARG BUILDCONFIG="Release"
ARG VERSION="1.0.0"
COPY ./src/CoverGo.Auth.Tests.Integration/*.csproj ./src/CoverGo.Auth.Tests.Integration/
COPY ./src/CoverGo.Auth.Tests.Integration ./src/CoverGo.Auth.Tests.Integration/
RUN dotnet restore ./src/CoverGo.Auth.Tests.Integration/CoverGo.Auth.Tests.Integration.csproj
RUN dotnet publish ./src/CoverGo.Auth.Tests.Integration/CoverGo.Auth.Tests.Integration.csproj -c $BUILDCONFIG -o ./out /p:Version=$VERSION --no-restore
COPY coverlet.runsettings ./

FROM build-tests-integration AS run-tests-integration
WORKDIR /app
ENV AUTH_INTEGRATION_TEST-AuthUrl="http://covergo-auth:60000"
ENTRYPOINT ["dotnet", "test","./src/CoverGo.Auth.Tests.Integration/CoverGo.Auth.Tests.Integration.csproj"]
CMD ["--nologo", "--no-restore", "--logger","junit;LogFileName=TestResults.xml","--settings", "coverlet.runsettings"]
