# Default values for AuthChart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
keda:
  enabled: true

sealed-secrets:
  enabled: true

AuthParentChart:
  databaseSecret:
    enabled: true

  sealedSecret:
    DATABASE_CONNECT_STRING: xxxx
    DATABASE_DRIVER: xxxx
    dockerconfigjson: xxxx
    OTP_LOGIN_CIPHER_IV: xxxx
    OTP_LOGIN_CIPHER_KEY: xxxx
    OTP_LOGIN_HASHER_KEY: xxxx

  labels:
    team: backend
    k8s-app: covergo-auth

  selectorLabels:
    k8s-app: covergo-auth

  podAnnotations:
    linkerd.io/inject: disabled

  annotations:
    reloader.stakater.com/auto: "true"

  appName: covergo-auth

  ## Deployment ##
  replicaCount: 1

  # image:
  #   repository: registry-intl.cn-hongkong.aliyuncs.com/covergo/auth:latest
  #   pullPolicy: Always
  #   # Overrides the image tag whose default is the chart appVersion.
  #   #tag: "2.4.361"

  # imagePullSecrets:
  #   name: secret-covergo2

  deployment:
    name: covergo-auth
  nameOverride: ""
  fullnameOverride: ""

  # podAnnotations:
  #   linkerd.io/inject: disabled

  # annotations:
  #   reloader.stakater.com/auto: "true"

  env:
    - name: datacenterId
      value: covergo-aliyun-hk
    - name: terminationTimeout
      value: "30"
    - name: ASPNETCORE_ENVIRONMENT
      value: "Production"
    - name: TRACING_ENABLED
      value: "true"
    - name: TRACING_CONNECTION_STRING
      value: "http://opentelemetry-collector.open-telemetry:4317"
    - name: TRACING_EXPORT_TIMEOUT
      value: "1000"
 
  sealedSecret:
   enabled: false

  envFrom:
    - secretRef:
        name: covergo-database
    - secretRef:
        name: covergo-auth-credentials

  readinessProbe:
    httpGet:
      path: /readyz
      port: 8080

  livenessProbe:
    httpGet:
      path: /healthz
      port: 8080

  resources:
    limits:
      cpu: 500m
      memory: 1000M
    requests:
      cpu: 300m
      memory: 500M

  ## End of Deployments ##

  ## Service ##
  service:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  ## End of Service ##

  ## Hpa ##
  autoscaling:
    enabled: false
    minReplicas: 2
    maxReplicas: 6
    targetCPUUtilizationPercentage: 80
    cooldownPeriod: 300
    triggers:
      - metadata:
          metricName: http_requests_total
          query: sum(rate(request_total{workload_ns="default",deployment="covergo-auth"}[1m])) by (deployment)
          serverAddress: http://prometheus-kube-prometheus-prometheus.k8s-component-monitoring:9090
          threshold: "30"
        type: prometheus
    pollingInterval: 60
    # targetMemoryUtilizationPercentage: 80

  ## End of Hpa ##

  nodeSelector:
    backend: active

  tolerations: []

  affinity: {}

  terminationGracePeriodSeconds: 60

  # podSecurityContext: {}
  #   # fsGroup: 2000

  # securityContext: {}
  #   # capabilities:
  #   #   drop:
  #   #   - ALL
  #   # readOnlyRootFilesystem: true
  #   # runAsNonRoot: true
  #   # runAsUser: 1000
