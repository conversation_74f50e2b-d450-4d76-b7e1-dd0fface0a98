# How to install Auth chart
## Prerequisites
- helm cli installed 
- kubectl cli installed
- any kind of local cluster tool installed (e.g. kind, minikube)

## Before you deploy Auth, You should already have
- a AuthChart/ folder (storing all K8S templates and values) in this repo
- access to helm repo in AWS s3 bucket (Your aws credentials should be set already. Your account should be authorized to access this bucket)
- a local cluster created in your computer (e.g. Create one by this command `kind create cluster --name cluster-name`)

## Apply Auth chart
Now you are good to install auth chart to your local. Two ways to install:

### A. Clone Git Repo
1. `git clone https://github.com/CoverGo/Auth.git` which you should have cloned already

2. At path `Auth/`, you should see `AuthChart/`, run 

    ```bash
    helm dependency update AuthChart
    ```

    What it does is downloading dependency helm packages that <PERSON><PERSON> will need. Auth needs `keda` and `sealed-secret`, which both are open source.

    **Expected output:**

    ```bash
    Getting updates for unmanaged Helm repositories...
    ...Successfully got an update from the "https://bitnami-labs.github.io/sealed-secrets" chart repository
    Hang tight while we grab the latest from your chart repositories...
    ...Successfully got an update from the "kedacore" chart repository
    Update Complete. ⎈Happy Helming!⎈
    Saving 3 charts
    Dependency AuthParentChart did not declare a repository. Assuming it exists in the charts directory
    Downloading keda from repo https://kedacore.github.io/charts
    Downloading sealed-secrets from repo https://bitnami-labs.github.io/sealed-secrets
    Deleting outdated charts
    ```

    Also, you will see 2 dependency packages added into `charts/` automatically.

    <img src="./image-20220302233423609.png" alt="image-20220302233423609" style="zoom:70%;float:left" />

2. In path Auth/, run
    
    ```bash
    helm install your-release-name AuthChart --namespace your-namespace --create-namespace -f  values.dev.yaml
    ```
    
    What it does is deploy everything in `Auth/AuthChart/charts/` into your cluster of your defined namespace with customized values in `values.dev.yaml`. In this case, you will deploy AuthParentChart, keda and sealed-secret. 
    
    **Please note that you can customize these charts by values in `Auth/AuthChart/values.dev.yaml` (or whatever `Auth/AuthChart/value.xxxxx.yaml`  you define).

    <a href="#our-practice">Our practice</a>
    
    - **Expected output:**
    ```
    NAME: your-release-name
    LAST DEPLOYED: Wed Mar  2 22:15:15 2022
    NAMESPACE: your-namespace
    STATUS: deployed
    REVISION: 1
    ```
    
3. You will see that it is installed into your cluster. Check the pod status with `kubectl get pods -A`
    - **Expected output:**
    
    ```bash
    NAMESPACE  NAME                                      READY  STATUS   RESTARTS   AGE
    default    your-release-name-sealed-secrets-bcf648ff5-vrm7w 1/1 Running  0      2m26s
    default    covergo-auth-88f898d-zdz2f                1/1    Running  0          2m26s
    default    keda-operator-79f74c7949-2r4zt            1/1    Running  0          2m26s
    default    keda-operator-metrics-apiserver-5f96b7b8d7-dxvk6 1/1  Running 0      2m26s
    ```

5. Now everything is up, but one thing is left. Secret needs to be updated. Since sealed secret is created uniquely for each cluster. So `encryptedData` in `AuthChart/charts/AuthParentChart/templates/secret.yaml` should be re-generated and updated into your  `values.xxxx.yaml`. 

   (Please refer to https://engineering.bitnami.com/articles/sealed-secrets.html )

   <a href="#Create-Sealed-Secrets">Create sealed secrets</a>



### Our Practice

- Since helm has a function of overriding values in parent chart, it is a good practice to use   `AuthChart/values.dev.yaml` in `helm install` to override values in parent chart, instead of modifying `AuthChart/charts/AuthParentChart/values.yaml` or `AuthChart/values.yaml`

- When we deploy Auth in local cluster, we also need to deploy dependencies, which are keda for autoscaling and sealed-secret for Auth's secrets.

  But in production, we do not need to handle dependencies. Flux will handle it. 

  Therefore,  `AuthChart/Chart.yaml` provides a way to conditionally deploy those dependencies. Enabling it in `AuthChart/values.dev.yaml` while disabling it in `AuthChart/values.prod.yaml`

  ```yaml
  # AuthChart/Chart.yaml
  - name: keda
     version: 2.1.3
     repository: https://kedacore.github.io/charts
     condition: keda.enabled
   - name: sealed-secrets
     version: 2.1.1
     repository: https://bitnami-labs.github.io/sealed-secrets
     condition: sealed-secrets.enabled
  ```

  ```yaml
  # AuthChart/values.dev.yaml
  keda:
    enabled: true
  
  sealed-secrets:
    enabled: true
  ```

  ```yaml
  # AuthChart/values.prod.yaml
  keda:
    enabled: false
  
  sealed-secrets:
    enabled: false
  ```



### Create Sealed Secrets
Assume you have your working cluster locally.
1. Prepare a .txt file containing your credentials like this. 
`my-credentials.txt`
```
DATABASE_CONNECT_STRING=14FD75F79C572CCDF987484293EB8
DATABASE_DRIVER=5D56B2FE7DB2E2542CC37CB444251
dockerconfigjson=ACDDFB2BC76F7874568DF85997EA8
OTP_LOGIN_CIPHER_IV=47338D2C2DED1B5F1B949FAFF584F
OTP_LOGIN_CIPHER_KEY=BBA5A16F55E8FDDD41658212CC82B
OTP_LOGIN_HASHER_KEY=BE363F36D7718D8396C441634ECE8
```

2. Run this script
```bash
controller_namespace="infra-workspace"

brew install kubeseal
helm repo add sealed-secrets https://bitnami-labs.github.io/sealed-secrets
kubectl create namespace $controller_namespace
helm install sealed-secrets-controller sealed-secrets/sealed-secrets -n $controller_namespace
```

3.
Fetch public key of your cluster (each cluster has its own public key)
```
kubeseal --fetch-cert > encrypt.pem
```

4. Run this script
```bash
raw_credentials="./my-credentials.txt"
generated_secret_path="./my-secrets.yaml"
cluster_public_key_path="./encrypt.pub" # you just generated in step3
generated_sealed_secret_path="./my-sealed-secret.yaml"
controller_name="sealed-secrets"
secret_name="my-secret-name"

kubectl create secret generic $secret_name --from-env-file=$raw_credentials  --dry-run=client -o yaml > $generated_secret_path
kubeseal --cert=$cluster_public_key_path --controller-namespace=$controller_namespace --controller-name=$controller_name --format=yaml < $generated_secret_path > $generated_sealed_secret_path
```

5. Open the generated `my-sealed-secret.yaml`. Copy the sealed secret values into `./AuthChart/values.dev.yaml` or the values.yaml that you will apply into your cluster.
```yaml
AuthParentChart:
  sealedSecret:
    DATABASE_CONNECT_STRING: replace-the-sealed-secret-value-here
    DATABASE_DRIVER: replace-the-sealed-secret-value-here
    dockerconfigjson: replace-the-sealed-secret-value-here
    OTP_LOGIN_CIPHER_IV: replace-the-sealed-secret-value-here
    OTP_LOGIN_CIPHER_KEY: replace-the-sealed-secret-value-here
    OTP_LOGIN_HASHER_KEY: replace-the-sealed-secret-value-here
```