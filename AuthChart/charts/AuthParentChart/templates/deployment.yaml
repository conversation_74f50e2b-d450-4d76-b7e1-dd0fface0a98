apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    {{- include "AuthChart.labels" . | nindent 4 }}
  name: {{ .Values.appName }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "AuthChart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "AuthChart.labels" . | nindent 8 }}
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      name: {{ .Values.appName }}
    spec:
      shareProcessNamespace: true
      terminationGracePeriodSeconds: 60
      containers:
        - image: "{{ .Values.image.repository}}:{{ .Values.image.tag }}" 
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          name: {{ .Values.deployment.name }}
          env:
            {{ toYaml .Values.env | nindent 12 }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          envFrom:
            {{ toYaml .Values.envFrom | nindent 12 }}
          {{if .Values.readinessProbe }}
          readinessProbe:
            {{ toYaml .Values.readinessProbe | nindent 12 }}
          {{end}}
          {{if .Values.livenessProbe }}
          livenessProbe:
            {{ toYaml .Values.livenessProbe | nindent 12 }}
          {{end}}
          {{if .Values.resources }}
          resources:
            {{ toYaml .Values.resources| nindent 12 }}
          {{end}}
      imagePullSecrets:
        - name: {{ .Values.imagePullSecrets.name }}
      restartPolicy: Always
