---
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  creationTimestamp: null
  name: covergo-auth-credentials
spec:
  encryptedData:
    OTP_LOGIN_CIPHER_IV: {{ .Values.sealedSecret.OTP_LOGIN_CIPHER_IV }}
    OTP_LOGIN_CIPHER_KEY: {{ .Values.sealedSecret.OTP_LOGIN_CIPHER_KEY }}
    OTP_LOGIN_HASHER_KEY: {{ .Values.sealedSecret.OTP_LOGIN_HASHER_KEY }}
  template:
    data: null
    metadata:
      creationTimestamp: null
      name: covergo-auth-credentials
    type: Opaque

---
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  creationTimestamp: null
  name: {{ .Values.imagePullSecrets.name }}
spec:
  encryptedData:
    .dockerconfigjson: {{ .Values.sealedSecret.dockerconfigjson }}
  template:
    data: null
    metadata:
      creationTimestamp: null
      name: {{ .Values.imagePullSecrets.name }}
    type: kubernetes.io/dockerconfigjson