{{- if .Values.autoscaling.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ .Values.appName }}
  labels:
    {{ toYaml .Values.autoscaling.labels | nindent 4 }}
spec:
  cooldownPeriod: {{ .Values.autoscaling.cooldownPeriod }}
  maxReplicaCount: {{ .Values.autoscaling.maxReplicas }}
  minReplicaCount: {{ .Values.autoscaling.minReplicas }}
  pollingInterval: {{ .Values.autoscaling.pollingInterval }}
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ .Values.appName }}
  triggers:
    {{ toYaml .Values.autoscaling.triggers | nindent 4 }}
{{- end }}