covergo-app:
  fullnameOverride: "covergo-auth"
  replicaCount: 2
  labels:
    team: "backend"
  image:
    repository: registry-intl.cn-hongkong.aliyuncs.com/covergo/auth
    tag: 2.42.0 # {"$imagepolicy": "flux-system:covergo-auth:tag"}
  imagePullSecrets:
    - name: registry-intl.cn-hongkong.aliyuncs.com
  namespace: production
  env:
    - name: FeatureManagement__PermissionV2
      value: "true"
    - name: datacenterId
      value: covergo-aliyun-hk
    - name: terminationTimeout
      value: "30"
    - name: ASPNETCORE_ENVIRONMENT
      value: "Production"
    - name: PROMETHEUS_ENABLED
      value: "true"
    - name: PROMETHEUS_MONGO_CONNECTION_METRICS_ENABLED
      value: "true"
    - name: PROMETHEUS_HTTP_METRICS_ENABLED
      value: "true"
    - name: PROMETHEUS_REQUEST_COUNT_TENANT_FILTERING_ENABLED
      value: "true"
    - name: POD_NAME
      valueFrom:
        fieldRef:
          fieldPath: metadata.name
    - name: TRACING_ENABLED
      value: "true"
    - name: TRACING_CONNECTION_STRING
      value: "http://monitoring-tempo.monitoring:4317"
    - name: TRACING_EXPORT_TIMEOUT
      value: "1000"
  envFrom:
    - name: covergo-auth-credentials
  admin:
    existingSecret: covergo-password
  database:
    adapter: mongoDb
    dsn:
      existingSecret: "covergo-database"
  flux:
    create: true
  autoscaling:
    enabled: true
    cooldownPeriod: 300
    maxReplicaCount: 6
    minReplicaCount: 2
    pollingInterval: 30
    triggers:
      - typeName: Utilization
        typeValue: "75"
        type: memory
      - typeName: Utilization
        typeValue: "75"
        type: cpu
