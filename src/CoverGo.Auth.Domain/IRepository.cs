﻿using CoverGo.Auth.Domain.PermissionSchemas;
using CoverGo.DomainUtils;
using IdentityServer4.Models;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain
{
    public class MongoLoginDao : Microsoft.AspNetCore.Identity.MongoDB.IdentityUser // TODO: to clean. Belongs in infra
    {
        public string EntityId { get; set; }
        public string EntityType { get; set; }
        public string CreatedById { get; set; }
        public string LastModifiedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public DateTime? PasswordLastUpdated { get; set; }
        public bool IsTemporaryPassword { get; set; } = false;
        public bool IgnorePasswordLifespan { get; set; }
        public bool IgnoreAccountLockout { get; set; }
        public List<string> PreviouslyUsedPasswords { get; set; }
        public List<string> TargetedPermissionSchemaIds { get; set; }
        public string PasswordResetToken { get; set; }
        public bool IsActive { get; set; } = true;
        public string LatestOtp { get; set; }
    }

    public interface IRepository
    {
        string ProviderId { get; }

        UserManager<MongoLoginDao> GetMongoUserManager();
        Task<IEnumerable<string>> GetLoginIdsAsync(string tenantId, LoginWhere @where, OrderBy orderBy, int? skip,
            int? first, CancellationToken cancellationToken);
        Task<MongoLoginDao> GetLoginAsync(string tenantId, string id, CancellationToken cancellationToken);
        Task<IEnumerable<MongoLoginDao>> GetLoginsAsync(string tenantId, LoginWhere @where, OrderBy orderBy, int? skip,
            int? first, CancellationToken cancellationToken);
        Task<long> GetLoginTotalCount(string tenantId, LoginWhere @where, CancellationToken cancellationToken);
        Task<MongoLoginDao> FindLoginByIdAsync(string loginId, CancellationToken cancellationToken);
        Task<IdentityResult> UpdateLoginAsync(MongoLoginDao loginDao, CancellationToken cancellationToken);
        Task<IdentityResult> AddClaimToLoginAsync(MongoLoginDao loginDao, Claim claim,
            CancellationToken cancellationToken);
        Task<IdentityResult> RemoveClaimFromLoginAsync(MongoLoginDao loginDao, Claim claim,
            CancellationToken cancellationToken);

        Task<IEnumerable<TargetGroup>> GetAllTargetGroups(string tenantId, CancellationToken cancellationToken);
        Task<TargetGroup> GetTargetGroupAsync(string tenantId, string id, CancellationToken cancellationToken);
        Task<Result<string>> CreateTargetGroupAsync(string tenantId, CreateTargetGroupCommand command,
            CancellationToken cancellationToken);
        Task<Result> UpdateTargetGroupAsync(string tenantId, string id, UpdateTargetGroupCommand command, CancellationToken cancellationToken);
        Task<Result> DeleteTargetGroupAsync(string tenantId, string id, CancellationToken cancellationToken);
        Task<Result> AddUserToTargetGroupAsync(string tenantId, string id, string targetId,
            CancellationToken cancellationToken);
        Task<Result> RemoveUserFromTargetGroupAsync(string tenantId, string id, string targetId,
            CancellationToken cancellationToken);
        Task<Result> UpdateLoginLockoutEndDate(string tenantId, string id, DateTime lockoutEndDate,
            CancellationToken cancellationToken);
        Task<Result> AddTargetGroupToTargetGroupAsync(string tenantId, string id, string targetGroupId,
            CancellationToken cancellationToken);
        Task<Result> RemoveTargetGroupFromTargetGroupAsync(string tenantId, string id, string targetGroupId,
            CancellationToken cancellationToken);

        Task<IEnumerable<PermissionGroup>> GetAllPermissionGroupsAsync(string tenantId,
            CancellationToken cancellationToken);
        Task<PermissionGroup> GetPermissionGroupAsync(string tenantId, string id, CancellationToken cancellationToken);
        Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId, IEnumerable<string> ids,
            CancellationToken cancellationToken);
        Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId, PermissionGroupWhere @where,
            CancellationToken cancellationToken);
        Task<IEnumerable<PermissionGroup>> GetPermissionGroupsByCreatorIdAsync(string tenantId, string creatorId,
            CancellationToken cancellationToken);
        Task<PermissionGroup> GetDefaultPermissionGroupAsync(string tenantId, CancellationToken cancellationToken);

        Task<Result<string>> CreatePermissionGroupAsync(string tenantId, CreatePermissionGroupCommand command,
            CancellationToken cancellationToken);
        Task<Result> UpdatePermissionGroupAsync(string tenantId, string id, UpdatePermissionGroupCommand command,
            CancellationToken cancellationToken);
        Task<Result> DeletePermissionGroupAsync(string tenantId, string id, CancellationToken cancellationToken);
        Task<Result> AddPermissionToPermissionGroupAsync(string tenantId, string id,
            AddPermissionToPermissionGroupCommand command, CancellationToken cancellationToken);
        Task<Result> RemovePermissionFromPermissionGroupAsync(string tenantId, string id,
            RemovePermissionFromPermissionGroupCommand command, CancellationToken cancellationToken);
        Task<Result> AddPermissionGroupToPermissionGroupAsync(string tenantId, string id,
            AddPermissionGroupToPermissionGroupCommand command, CancellationToken cancellationToken);
        Task<Result> RemovePemissionGroupFromPermissionGroupAsync(string tenantId, string id,
            RemovePermissionGroupFromPermissionGroupCommand command, CancellationToken cancellationToken);
        Task<Result> AddLoginPermissionsToPermissionGroupAsync(string tenantId, string id,
            AddLoginPermissionsToPermissionGroupCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveLoginPermissionsFromPermissionGroupAsync(string tenantId, string id,
            RemoveLoginPermissionsFromPermissionGroupCommand command, CancellationToken cancellationToken);
        Task<PasswordValidators> GetPasswordValidatorsAsync(string tenantId, string clientId,
            CancellationToken cancellationToken);

        Task<Result> UpdatePasswordValidatorsAsync(string tenantId, string clientId, UpdatePasswordValidatorsCommand command, CancellationToken cancellationToken);
        Task<IEnumerable<JToken>> GetIndexStats(string tenantId, string colName, CancellationToken cancellationToken);
        Task<Result> AddTargetedPermissionSchemaToLoginForIds(string tenantId,
            AddTargetedPermissionSchemaToLoginCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveTargetedPermissionSchemaFromLoginForIds(string tenantId,
            RemoveTargetedPermissionSchemaFromLoginCommand command, CancellationToken cancellationToken);
        Task<IReadOnlyCollection<TargetedPermissionSchema>> GetTargetedPermissionSchemas(string tenantId,
            IReadOnlyCollection<string> targetedPermissionSchemaIds, CancellationToken cancellationToken);

        Task<Result> UpdateLatestOtpAsync(string tenantId, string loginId, string latestOtp, CancellationToken cancellationToken);
    }

    public class TargetGroup
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public IEnumerable<string> TargetIds { get; set; }
        public IEnumerable<string> TargetGroupIds { get; set; }
    }

    public class CreateTargetGroupCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class UpdateTargetGroupCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class AddUserToTargetGroupCommand
    {
        public string UserId { get; set; }
        public string AddedByid { get; set; }
    }

    public class RemoveUserFromTargetGroupCommand
    {
        public string UserId { get; set; }
        public string RemovedByid { get; set; }
    }

    public class ChangeUserLockoutDateCommand
    {
        public DateTime? EndDateTime { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddTargetGroupToTargetGroupCommand
    {
        public string TargetGroupId { get; set; }
        public string AddedByid { get; set; }
    }

    public class RemoveTargetGroupFromTargetGroupCommand
    {
        public string TargetGroupId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AppWhere : Where
    {
        public IEnumerable<AppWhere> Or { get; set; }
        public IEnumerable<AppWhere> And { get; set; }
        public string AppId { get; set; }
        public IEnumerable<string> AppId_in { get; set; }
    }

    public class LoginWhere : Where
    {
        public IEnumerable<LoginWhere> Or { get; set; }
        public IEnumerable<LoginWhere> And { get; set; }

        public IEnumerable<string> Ids { get; set; }
        public IEnumerable<string> EntityIds { get; set; }
        public IEnumerable<string> EntityTypes { get; set; }
        public IEnumerable<string> Usernames { get; set; }
        public string Username_contains { get; set; }
        public DateTime? PasswordLastUpdated_lt { get; set; }
        public DateTime? PasswordLastUpdated_gt { get; set; }
        public IEnumerable<string> Email_in { get; set; }

        public bool? IsEmailConfirmed { get; set; }
        public bool? IsTelephoneNumberConfirmed { get; set; }
        public bool? IsActive { get; set; }
        public IEnumerable<string> PermissionGroupIds { get; set; }

        public bool ExcludePermissions { get; set; }
    }

    public class AppQueryArguments
    {
        public AppWhere Where { get; set; }
        public OrderBy OrderBy { get; set; }
        public int? First { get; set; }
        public int? Skip { get; set; }
    }

    public class LoginQueryArguments
    {
        public LoginWhere Where { get; set; }
        public OrderBy OrderBy { get; set; }
        public int? First { get; set; }
        public int? Skip { get; set; }
    }

    public class PermissionGroup : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public Dictionary<string, IEnumerable<string>> TargettedPermissions { get; set; }
        public IEnumerable<string> PermissionGroupIds { get; set; }
        public IEnumerable<string> InheritedLoginIds { get; set; }
        public IEnumerable<string> ProductTypes { get; set; } // temporary solution to handle complex permissions by product type, supposed to be used only by Insured Nomads untill the generic permissions will be ready for production
    }

    public class PermissionGroupWhere : Where
    {
        public IEnumerable<PermissionGroupWhere> And { get; set; }
        public IEnumerable<PermissionGroupWhere> Or { get; set; }

        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string Name { get; set; }
        public IEnumerable<string> Name_in { get; set; }
    }

    public class CreatePermissionGroupCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string CreatedById { get; set; }
        public IEnumerable<string> ProductTypes { get; set; }
    }

    public class UpdatePermissionGroupCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string ModifiedById { get; set; }
        public IEnumerable<string> ProductTypes { get; set; }
    }

    public class AddPermissionToPermissionGroupCommand
    {
        public string PermissionId { get; set; }
        public string TargetId { get; set; } = "";
        public string AddedById { get; set; }
    }

    public class RemovePermissionFromPermissionGroupCommand
    {
        public string PermissionId { get; set; }
        public string TargetId { get; set; } = "";
        public string RemovedById { get; set; }
    }

    public class AddPermissionGroupToPermissionGroupCommand
    {
        public string PermissionGroupId { get; set; }
        public string AddedById { get; set; }
    }


    public class RemovePermissionGroupFromPermissionGroupCommand
    {
        public string PermissionGroupId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AddLoginPermissionsToPermissionGroupCommand
    {
        public string LoginId { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveLoginPermissionsFromPermissionGroupCommand
    {
        public string LoginId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AppDao : Client
    {
        public UrlRouting UrlRouting { get; set; }
        public bool Requires2FA { get; set; }

        public DateTime? CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }

        public string CreatedById { get; set; }
        public string LastModifiedById { get; set; }

        public bool UseNotificationConfig { get; set; }
        public TimeSpan? EmailConfirmationTokenLifespan { get; set; }
        public TimeSpan? DataProtectionTokenLifespan { get; set; }
        public TimeSpan? PasswordExpiryLifespan { get; set; }
        public string DefaultTimeZone { get; set; }
        public bool ActivationTokenExpiryDisabled { get; set; }
        public bool RequiresEmail2FA { get; set; }
        public BsonDocument AppConfig { get; set; }
        public string Domain { get; set; }
        public ForgotPasswordEmailSettings ForgotPasswordEmailSettings { get; set; }

        public static App ToApp(AppDao dao)
        {
            if (dao == null)
                return null;

            Dictionary<string, string> clientProperties = dao.Properties?.ToDictionary(p => p.Key, p => p.Value);

            return new App
            {
                AppId = dao.ClientId,
                AppName = dao.ClientName,
                Email = clientProperties?.FirstOrDefault(d => d.Key == "email").Value,
                EmailSenderName = clientProperties?.FirstOrDefault(d => d.Key == "senderName").Value,
                OneTimePasswordEmailSubject = clientProperties?.FirstOrDefault(d => d.Key == "oneTimePasswordEmailSubject").Value,
                UseNotificationConfig = dao.UseNotificationConfig,
                AccessTokenLifetime = dao.AccessTokenLifetime,
                Requires2FA = dao.Requires2FA,
                RedirectUris = dao.RedirectUris?.ToList(),
                CreatedAt = dao.CreatedAt.GetValueOrDefault(),
                LastModifiedAt = dao.LastModifiedAt.GetValueOrDefault(),
                CreatedById = dao.CreatedById,
                LastModifiedById = dao.LastModifiedById,
                UrlRouting = dao.UrlRouting,
                AbsoluteRefreshTokenLifetime = dao.AbsoluteRefreshTokenLifetime,
                AccessTokenType = dao.AccessTokenType,
                AllowAccessTokensViaBrowser = dao.AllowAccessTokensViaBrowser,
                AllowedCorsOrigins = dao.AllowedCorsOrigins,
                AllowedGrantTypes = dao.AllowedGrantTypes,
                AllowedScopes = dao.AllowedScopes,
                AllowOfflineAccess = dao.AllowOfflineAccess,
                AllowPlainTextPkce = dao.AllowPlainTextPkce,
                AllowRememberConsent = dao.AllowRememberConsent,
                AlwaysIncludeUserClaimsInIdToken = dao.AlwaysIncludeUserClaimsInIdToken,
                AlwaysSendClientClaims = dao.AlwaysSendClientClaims,
                AuthorizationCodeLifetime = dao.AuthorizationCodeLifetime,
                UpdateAccessTokenClaimsOnRefresh = dao.UpdateAccessTokenClaimsOnRefresh,
                BackChannelLogoutSessionRequired = dao.BackChannelLogoutSessionRequired,
                BackChannelLogoutUri = dao.BackChannelLogoutUri,
                Claims = dao.Claims,
                ClientClaimsPrefix = dao.ClientClaimsPrefix,
                ClientSecrets = dao.ClientSecrets,
                ClientUri = dao.ClientUri,
                ConsentLifetime = dao.ConsentLifetime,
                Description = dao.Description,
                DeviceCodeLifetime = dao.DeviceCodeLifetime,
                Enabled = dao.Enabled,
                EnableLocalLogin = dao.EnableLocalLogin,
                FrontChannelLogoutSessionRequired = dao.FrontChannelLogoutSessionRequired,
                FrontChannelLogoutUri = dao.FrontChannelLogoutUri,
                IdentityProviderRestrictions = dao.IdentityProviderRestrictions,
                IdentityTokenLifetime = dao.IdentityTokenLifetime,
                IncludeJwtId = dao.IncludeJwtId,
                LogoUri = dao.LogoUri,
                PairWiseSubjectSalt = dao.PairWiseSubjectSalt,
                PostLogoutRedirectUris = dao.PostLogoutRedirectUris,
                Properties = dao.Properties,
                ProtocolType = dao.ProtocolType,
                RefreshTokenExpiration = dao.RefreshTokenExpiration,
                RefreshTokenUsage = dao.RefreshTokenUsage,
                RequireClientSecret = dao.RequireClientSecret,
                RequireConsent = dao.RequireConsent,
                RequirePkce = dao.RequirePkce,
                SlidingRefreshTokenLifetime = dao.SlidingRefreshTokenLifetime,
                UserCodeType = dao.UserCodeType,
                UserSsoLifetime = dao.UserSsoLifetime,
                EmailConfirmationTokenLifespan = dao.EmailConfirmationTokenLifespan,
                DataProtectionTokenLifespan = dao.DataProtectionTokenLifespan,
                PasswordExpiryLifespan = dao.PasswordExpiryLifespan,
                DefaultTimeZone = dao.DefaultTimeZone,
                ActivationTokenExpiryDisabled = dao.ActivationTokenExpiryDisabled,
                RequiresEmail2FA = dao.RequiresEmail2FA,
                AppConfig = dao.AppConfig?.ToJson(new MongoDB.Bson.IO.JsonWriterSettings() { Indent = false }),
                ForgotPasswordEmailSettings = dao.ForgotPasswordEmailSettings
            };
        }

        public static AppDao ToAppDao(App app)
        {
            if (app == null)
                return null;

            return new AppDao
            {
                AbsoluteRefreshTokenLifetime = app.AbsoluteRefreshTokenLifetime,
                AccessTokenLifetime = app.AccessTokenLifetime,
                AccessTokenType = app.AccessTokenType,
                AllowAccessTokensViaBrowser = app.AllowAccessTokensViaBrowser,
                AllowedCorsOrigins = app.AllowedCorsOrigins ?? new List<string> { },
                AllowedGrantTypes = app.AllowedGrantTypes,
                AllowedScopes = app.AllowedScopes,
                AllowOfflineAccess = app.AllowOfflineAccess,
                AllowPlainTextPkce = app.AllowPlainTextPkce,
                AllowRememberConsent = app.AllowRememberConsent,
                AlwaysIncludeUserClaimsInIdToken = app.AlwaysIncludeUserClaimsInIdToken,
                AlwaysSendClientClaims = app.AlwaysSendClientClaims,
                AuthorizationCodeLifetime = app.AuthorizationCodeLifetime,
                CreatedAt = app.CreatedAt,
                LastModifiedAt = app.LastModifiedAt,
                Requires2FA = app.Requires2FA,
                UpdateAccessTokenClaimsOnRefresh = app.UpdateAccessTokenClaimsOnRefresh,
                BackChannelLogoutSessionRequired = app.BackChannelLogoutSessionRequired,
                BackChannelLogoutUri = app.BackChannelLogoutUri,
                Claims = app.Claims ?? new List<Claim> { },
                ClientClaimsPrefix = app.ClientClaimsPrefix,
                ClientId = app.AppId,
                ClientName = app.AppName,
                ClientSecrets = app.ClientSecrets ?? new List<IdentityServer4.Models.Secret> { },
                ClientUri = app.ClientUri,
                ConsentLifetime = app.ConsentLifetime,
                CreatedById = app.CreatedById,
                Description = app.Description,
                DeviceCodeLifetime = app.DeviceCodeLifetime,
                Enabled = app.Enabled,
                EnableLocalLogin = app.EnableLocalLogin,
                FrontChannelLogoutSessionRequired = app.FrontChannelLogoutSessionRequired,
                FrontChannelLogoutUri = app.FrontChannelLogoutUri,
                IdentityProviderRestrictions = app.IdentityProviderRestrictions,
                IdentityTokenLifetime = app.IdentityTokenLifetime,
                IncludeJwtId = app.IncludeJwtId,
                LastModifiedById = app.LastModifiedById,
                LogoUri = app.LogoUri,
                PairWiseSubjectSalt = app.PairWiseSubjectSalt,
                PostLogoutRedirectUris = app.PostLogoutRedirectUris ?? new List<string> { },
                Properties = app.Properties ?? new Dictionary<string, string> { },
                ProtocolType = app.ProtocolType,
                RedirectUris = app.RedirectUris ?? new List<string> { },
                RefreshTokenExpiration = app.RefreshTokenExpiration,
                RefreshTokenUsage = app.RefreshTokenUsage,
                RequireClientSecret = app.RequireClientSecret,
                RequireConsent = app.RequireConsent,
                RequirePkce = app.RequirePkce,
                SlidingRefreshTokenLifetime = app.SlidingRefreshTokenLifetime,
                UrlRouting = app.UrlRouting,
                UserCodeType = app.UserCodeType,
                UserSsoLifetime = app.UserSsoLifetime,
                EmailConfirmationTokenLifespan = app.EmailConfirmationTokenLifespan,
                DataProtectionTokenLifespan = app.DataProtectionTokenLifespan,
                PasswordExpiryLifespan = app.PasswordExpiryLifespan,
                UseNotificationConfig = app.UseNotificationConfig,
                DefaultTimeZone = app.DefaultTimeZone,
                ActivationTokenExpiryDisabled = app.ActivationTokenExpiryDisabled,
                RequiresEmail2FA = app.RequiresEmail2FA,
                AppConfig = app.AppConfig == null ? null : BsonDocument.Parse(app.AppConfig),
                ForgotPasswordEmailSettings = app.ForgotPasswordEmailSettings
            };
        }
    }

    public class CustomEmailConfirmationTokenProvider : IUserTwoFactorTokenProvider<MongoLoginDao>
    {
        public CustomEmailConfirmationTokenProvider(
            IDataProtectionProvider dataProtectionProvider,
            IOptions<CustomEmailConfirmationTokenProviderOptions> options,
            ILoggerFactory loggerFactory)
        {
            ILogger<DataProtectorTokenProvider<MongoLoginDao>> logger = loggerFactory.CreateLogger<DataProtectorTokenProvider<MongoLoginDao>>();
            _defaultDataProtectorTokenProvider = CreateProvider(dataProtectionProvider, null, logger);
            _providers = options.Value.AppNameToEmailTokenLifespan
                .ToDictionary(kvp => kvp.Key, kvp => CreateProvider(dataProtectionProvider, kvp.Value, logger));
        }

        readonly IReadOnlyDictionary<string, DataProtectorTokenProvider<MongoLoginDao>> _providers;

        readonly DataProtectorTokenProvider<MongoLoginDao> _defaultDataProtectorTokenProvider;
        DataProtectorTokenProvider<MongoLoginDao> CreateProvider(IDataProtectionProvider dataProtectionProvider, TimeSpan? tokenLifespan, ILogger<DataProtectorTokenProvider<MongoLoginDao>> logger)
        {
            DataProtectionTokenProviderOptions options = tokenLifespan == null
                ? new DataProtectionTokenProviderOptions()
                : new DataProtectionTokenProviderOptions { TokenLifespan = tokenLifespan.Value };

            return new DataProtectorTokenProvider<MongoLoginDao>(
                dataProtectionProvider,
                new OptionsWrapper<DataProtectionTokenProviderOptions>(options),
                logger);
        }

        DataProtectorTokenProvider<MongoLoginDao> GetProvider(MongoLoginDao user)
        {
            string appId = user.Claims?.Where(c => c.Type == "clientId")?.Select(c => c.Value)?.FirstOrDefault();
            DataProtectorTokenProvider<MongoLoginDao> provider = _providers.FirstOrDefault(p => p.Key == appId).Value;
            return provider ?? _defaultDataProtectorTokenProvider;
        }

        public Task<string> GenerateAsync(string purpose, UserManager<MongoLoginDao> manager, MongoLoginDao user) =>
            GetProvider(user).GenerateAsync(purpose, manager, user);

        public Task<bool> ValidateAsync(string purpose, string token, UserManager<MongoLoginDao> manager, MongoLoginDao user) =>
            GetProvider(user).ValidateAsync(purpose, token, manager, user);

        public Task<bool> CanGenerateTwoFactorTokenAsync(UserManager<MongoLoginDao> manager, MongoLoginDao user) =>
            GetProvider(user).CanGenerateTwoFactorTokenAsync(manager, user);
    }

    public class CustomEmailConfirmationTokenProviderOptions
    {
        public IReadOnlyDictionary<string, TimeSpan> AppNameToEmailTokenLifespan { get; set; }
    }

    public class CustomDataProtectorTokenProvider<TUser>
       : DataProtectorTokenProvider<TUser> where TUser : Microsoft.AspNetCore.Identity.MongoDB.IdentityUser
    {

        private readonly AppDao _app;

        public CustomDataProtectorTokenProvider(IDataProtectionProvider dataProtectionProvider, IOptions<DataProtectionTokenProviderOptions> options, AppDao app, ILogger<CustomDataProtectorTokenProvider<TUser>> logger)
            : base(dataProtectionProvider, options, logger)
        {
            _app = app;
        }

        public override async Task<bool> ValidateAsync(string purpose, string token, UserManager<TUser> manager, TUser user)
        {
            try
            {
                byte[] unprotectedData = Protector.Unprotect(Convert.FromBase64String(token));

                var ms = new MemoryStream(unprotectedData);
                using BinaryReader reader = ms.CreateReader();

                DateTimeOffset creationTime = reader.ReadDateTimeOffset();
                if (!(_app?.ActivationTokenExpiryDisabled ?? false) || user.EmailConfirmed)
                {
                    DateTimeOffset expirationTime = creationTime + (!string.IsNullOrEmpty(user.PasswordHash) ? Options.TokenLifespan : _app.EmailConfirmationTokenLifespan.Value);
                    if (expirationTime < DateTimeOffset.UtcNow)
                    {
                        Logger.InvalidExpirationTime();
                        return false;
                    }
                }

                string userId = reader.ReadString();
                string actualUserId = await manager.GetUserIdAsync(user);
                if (userId != actualUserId)
                {
                    Logger.UserIdsNotEquals();
                    return false;
                }

                string purp = reader.ReadString();
                if (!string.Equals(purp, purpose))
                {
                    Logger.PurposeNotEquals(purpose, purp);
                    return false;
                }

                string stamp = reader.ReadString();
                if (reader.PeekChar() != -1)
                {
                    Logger.UnexpectedEndOfInput();
                    return false;
                }

                if (manager.SupportsUserSecurityStamp)
                {
                    bool isEqualsSecurityStamp = stamp == await manager.GetSecurityStampAsync(user);
                    if (!isEqualsSecurityStamp)
                    {
                        Logger.SecurityStampNotEquals();
                    }

                    return isEqualsSecurityStamp;
                }


                bool stampIsEmpty = stamp == "";
                if (!stampIsEmpty)
                {
                    Logger.SecurityStampIsNotEmpty();
                }

                return stampIsEmpty;
            }
            // ReSharper disable once EmptyGeneralCatchClause
            catch
            {
                // Do not leak exception
                Logger.UnhandledException();
            }

            return false;
        }
    }

    public class CustomPasswordResetTokenProvider : IUserTwoFactorTokenProvider<MongoLoginDao>
    {
        readonly ILogger<CustomPasswordResetTokenProvider> _logger;
        readonly CustomPasswordResetTokenProviderContext _customPasswordResetTokenProviderContext;

        public CustomPasswordResetTokenProvider(
            IDataProtectionProvider dataProtectionProvider,
            IOptions<CustomPasswordResetTokenProviderOptions> options,
            ILoggerFactory loggerFactory,
            CustomPasswordResetTokenProviderContext customPasswordResetTokenProviderContext)
        {
            _logger = loggerFactory.CreateLogger<CustomPasswordResetTokenProvider>();
            _customPasswordResetTokenProviderContext = customPasswordResetTokenProviderContext;
            ILogger<CustomDataProtectorTokenProvider<MongoLoginDao>> logger = loggerFactory.CreateLogger<CustomDataProtectorTokenProvider<MongoLoginDao>>();
            _defaultDataProtectorTokenProvider = CreateProvider(dataProtectionProvider, null, logger);
            _providers = options.Value.AppNameToAppSettings
                .ToDictionary(kvp => kvp.Key, kvp => CreateProvider(dataProtectionProvider, kvp.Value, logger));
        }

        readonly IReadOnlyDictionary<string, CustomDataProtectorTokenProvider<MongoLoginDao>> _providers;

        readonly CustomDataProtectorTokenProvider<MongoLoginDao> _defaultDataProtectorTokenProvider;
        CustomDataProtectorTokenProvider<MongoLoginDao> CreateProvider(IDataProtectionProvider dataProtectionProvider, AppDao app, ILogger<CustomDataProtectorTokenProvider<MongoLoginDao>> logger)
        {
            DataProtectionTokenProviderOptions options;
            if (app?.DataProtectionTokenLifespan.HasValue ?? false)
            {
                options = new DataProtectionTokenProviderOptions { TokenLifespan = app.DataProtectionTokenLifespan.Value };
            }
            else if (app?.EmailConfirmationTokenLifespan.HasValue ?? false)
            {
                options = new DataProtectionTokenProviderOptions { TokenLifespan = app.EmailConfirmationTokenLifespan.Value };
            }
            else
            {
                options = new DataProtectionTokenProviderOptions();
            }

            return new CustomDataProtectorTokenProvider<MongoLoginDao>(
                dataProtectionProvider,
                new OptionsWrapper<DataProtectionTokenProviderOptions>(options),
                app,
                logger);
        }

        CustomDataProtectorTokenProvider<MongoLoginDao> GetProvider(MongoLoginDao user)
        {
            string appId = user.Claims?.Where(c => c.Type == "clientId")?.Select(c => c.Value)?.FirstOrDefault();
            return GetProvider(appId);
        }

        CustomDataProtectorTokenProvider<MongoLoginDao> GetProvider(string appId)
        {
            CustomDataProtectorTokenProvider<MongoLoginDao> provider = _providers.FirstOrDefault(p => p.Key == appId).Value;
            return provider ?? _defaultDataProtectorTokenProvider;
        }

        public Task<string> GenerateAsync(string purpose, UserManager<MongoLoginDao> manager, MongoLoginDao user) =>
            GetProvider(user).GenerateAsync(purpose, manager, user);


        public async Task<bool> ValidateAsync(string purpose, string token, UserManager<MongoLoginDao> manager, MongoLoginDao user)
        {
            if (user.PasswordResetToken == token)
            {
                string clientId = _customPasswordResetTokenProviderContext.ClientId;
                var provider = string.IsNullOrEmpty(clientId) ? GetProvider(user) : GetProvider(clientId);
                return await provider.ValidateAsync(purpose, token, manager, user);
            }
            else
            {
                _logger.UnknownToken();
                return false;
            }

        }

        public Task<bool> CanGenerateTwoFactorTokenAsync(UserManager<MongoLoginDao> manager, MongoLoginDao user) =>
            GetProvider(user).CanGenerateTwoFactorTokenAsync(manager, user);
    }

    public class CustomPasswordResetTokenProviderOptions
    {
        public IReadOnlyDictionary<string, AppDao> AppNameToAppSettings { get; set; }
    }

    public class CustomPasswordResetTokenProviderContext
    {
        public string ClientId { get; set; }
    }
}
