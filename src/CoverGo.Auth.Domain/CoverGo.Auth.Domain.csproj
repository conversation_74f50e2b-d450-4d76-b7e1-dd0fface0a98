﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Contrib-2.Microsoft.AspNetCore.Identity.MongoDB" Version="2.0.6" />
    <PackageReference Include="CoverGo.Applications.Domain" Version="3.59.0" />
    <PackageReference Include="CoverGo.ChannelManagement.Client" Version="1.39.0-rc.3" />
    <PackageReference Include="CoverGo.Claims3.Client" Version="1.5.0-rc.8" />
    <PackageReference Include="CoverGo.Configuration" Version="3.59.0" />
    <PackageReference Include="CoverGo.DomainUtils" Version="3.68.33" />
    <PackageReference Include="CoverGo.FeatureManagement" Version="3.68.48" />
    <PackageReference Include="HotChocolate.Types" Version="12.12.1" />
    <PackageReference Include="IdentityServer4" Version="2.5.4" />
    <PackageReference Include="Microsoft.AspNetCore.App" Version="2.2.8">
      <PrivateAssets Condition="'%(PackageReference.Version)' == ''">all</PrivateAssets>
      <Publish Condition="'%(PackageReference.Version)' == ''">true</Publish>
    </PackageReference>
    <PackageReference Include="MongoDB.Driver" Version="2.19.0" />
    <PackageReference Include="morelinq" Version="3.3.2" />
    <PackageReference Include="CoverGo.Policies.Client" Version="2.151.1" />
  </ItemGroup>

</Project>
