using CoverGo.Auth.Domain.PermissionSchemas;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Identity;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain
{
    public class AuthService : IAuthService
    {
        private readonly IEnumerable<IRepository> _repositories;
        private readonly IEnumerable<IEventStore> _eventStores;
        readonly IEnumerable<ITenantRepository> _tenantRepositories;
        readonly ISendNotificationScheduleRepository _sendNotificationScheduleRepository;
        private readonly JsonSerializer _jsonSerializer;

        public AuthService(IEnumerable<IRepository> repositories, IEnumerable<IEventStore> eventStores, IEnumerable<ITenantRepository> tenantRepositories, ISendNotificationScheduleRepository sendNotificationScheduleRepository)
        {
            _repositories = repositories;
            _eventStores = eventStores;
            _tenantRepositories = tenantRepositories;
            _sendNotificationScheduleRepository = sendNotificationScheduleRepository;
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                TypeNameHandling = TypeNameHandling.Auto,
                NullValueHandling = NullValueHandling.Ignore,
            };
            settings.Converters.Add(new StringEnumConverter());

            _jsonSerializer = JsonSerializer.Create(settings);
        }

        public UserManager<MongoLoginDao> GetMongoUserManager(string tenantId)
        {
            IRepository repository = GetRepository(tenantId);

            return repository.GetMongoUserManager();
        }

        public async Task<MongoLoginDao> FindLoginByIdAsync(string tenantId, string loginId, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            return await repository.FindLoginByIdAsync(loginId, cancellationToken);
        }

        public async Task<IdentityResult> UpdateLoginAsync(string tenantId, MongoLoginDao login, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            return await repository.UpdateLoginAsync(login, cancellationToken);
        }

        public async Task<IdentityResult> AddClaimToLoginAsync(string tenantId, MongoLoginDao login, Claim claim, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            return await repository.AddClaimToLoginAsync(login, claim, cancellationToken);
        }

        public async Task<IdentityResult> RemoveClaimFromLogin(string tenantId, MongoLoginDao login, Claim claim, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            return await repository.RemoveClaimFromLoginAsync(login, claim, cancellationToken);
        }

        public async Task<App> GetAppAsync(string tenantId, string appId, CancellationToken cancellationToken)
        {
            ITenantRepository repository = GetTenantRepository(tenantId);

            App app = await repository.GetAppAsync(tenantId, appId, cancellationToken);
            return app;
        }

        public Task<IEnumerable<AppEvent>> GetAppEventsAsync(string tenantId, IEnumerable<AppEventType> types,
            IEnumerable<string> ids, CancellationToken cancellationToken)
        {
            IEventStore eventStore = GetEventStore(tenantId);

            return eventStore.GetAppEventsAsync(tenantId, types, ids, cancellationToken);
        }

        public async Task<IEnumerable<JToken>> GetIndexStats(string tenantId, string colName,
            CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            IEnumerable<JToken> client = await repository.GetIndexStats(tenantId, colName, cancellationToken);
            return client;
        }

        public async Task<IEnumerable<App>> GetAppsAsync(string tenantId, AppWhere @where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken)
        {
            ITenantRepository repository = GetTenantRepository(tenantId);

            IEnumerable<App> apps = await repository.GetAppsAsync(tenantId, @where, orderBy, skip, first, cancellationToken);
            return apps;
        }

        public async Task<long> GetAppTotalCount(string tenantId, AppWhere @where, CancellationToken cancellationToken)
        {
            ITenantRepository repository = GetTenantRepository(tenantId);

            long counts = await repository.GetAppTotalCount(tenantId, @where, cancellationToken);
            return counts;
        }

        public async Task<Result> DeleteAppAsync(string tenantId, string appId, DeleteCommand command, CancellationToken cancellationToken)
        {
            ITenantRepository repository = GetTenantRepository(tenantId);

            Result result = await repository.DeleteAppAsync(tenantId, appId, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result addEventResult = await eventStore.AddEventAsync(tenantId, new AppEvent(appId, AppEventType.deleteApp, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);
            return addEventResult;
        }

        public async Task<Result> UpdateAppAsync(string tenantId, string appId, UpdateAppCommand command, CancellationToken cancellationToken)
        {
            ITenantRepository repository = GetTenantRepository(tenantId);

            Result result = await repository.UpdateAppAsync(tenantId, appId, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result addEventResult = await eventStore.AddEventAsync(tenantId, new AppEvent(appId, AppEventType.updateApp, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return addEventResult;
        }

        public async Task<Result> CreateAppAsync(string tenantId, CreateAppCommand command, CancellationToken cancellationToken)
        {
            ITenantRepository repository = GetTenantRepository(tenantId);

            Result result = await repository.CreateAppAsync(tenantId, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result addEventResult = await eventStore.AddEventAsync(tenantId, new AppEvent(command.AppId, AppEventType.createApp, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);
            return addEventResult;
        }

        public async Task<TenantSettings> GetTenantSettingsAsync(string tenantId, CancellationToken cancellationToken)
        {
            ITenantRepository repository = GetTenantRepository(tenantId);

            TenantSettings result = await repository.GetTenantSettingsAsync(tenantId, cancellationToken);
            return result;
        }

        public async Task<Result> AddHostToTenantSettingsAsync(string tenantId, string domain, CancellationToken cancellationToken)
        {
            ITenantRepository repository = GetTenantRepository(tenantId);

            Result result = await repository.AddHostToTenantSettingsAsync(tenantId, domain, cancellationToken);
            return result;
        }

        public async Task<Result> RemoveHostFromTenantSettingsAsync(string tenantId, string domain, CancellationToken cancellationToken)
        {
            ITenantRepository repository = GetTenantRepository(tenantId);

            Result result = await repository.RemoveHostFromTenantSettingsAsync(tenantId, domain, cancellationToken);
            return result;
        }

        public async Task<PasswordValidators> GetPasswordValidatorsAsync(string tenantId, string clientId, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            PasswordValidators result = await repository.GetPasswordValidatorsAsync(tenantId, clientId, cancellationToken);
            return result;
        }

        public async Task<PermissionGroup> GetDefaultPermissionGroupAsync(string tenantId, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            PermissionGroup result = await repository.GetDefaultPermissionGroupAsync(tenantId, cancellationToken);
            return result;
        }

        public async Task<IEnumerable<MongoLoginDao>> GetLoginsAsync(string tenantId, LoginWhere @where,
            OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            IEnumerable<MongoLoginDao> result = await repository.GetLoginsAsync(tenantId, @where, orderBy, skip, first, cancellationToken);
            return result;
        }

        public async Task<long> GetLoginTotalCount(string tenantId, LoginWhere @where, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            long result = await repository.GetLoginTotalCount(tenantId, @where, cancellationToken);
            return result;
        }

        public async Task<MongoLoginDao> GetLoginAsync(string tenantId, string loginId, CancellationToken cancellationToken)
        {
            var dbConfig = DbConfig.GetConfig(tenantId);
            IRepository repository = _repositories.FirstOrDefault(r => r.ProviderId == dbConfig.ProviderId);

            MongoLoginDao result = await repository.GetLoginAsync(tenantId, loginId, cancellationToken);
            return result;
        }

        public Task<IEnumerable<string>> GetLoginIdsAsync(string tenantId, LoginWhere @where, CancellationToken cancellationToken)
        {
            return GetLoginIdsAsync(tenantId, where, null, null, null, cancellationToken);
        }

        public async Task<IEnumerable<string>> GetLoginIdsAsync(string tenantId, LoginWhere @where, OrderBy orderBy,
            int? skip, int? first, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            IEnumerable<string> result = await repository.GetLoginIdsAsync(tenantId, @where, orderBy, skip, first, cancellationToken);
            return result;
        }

        public async Task<IEnumerable<PermissionGroup>> GetPermissionGroupsByCreatorIdAsync(string tenantId, string id,
            CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            IEnumerable<PermissionGroup> result = await repository.GetPermissionGroupsByCreatorIdAsync(tenantId, id, cancellationToken);
            return result;
        }

        public async Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId,
            IEnumerable<string> permissionGroupIds, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            IEnumerable<PermissionGroup> result = await repository.GetPermissionGroupsAsync(tenantId, permissionGroupIds, cancellationToken);
            return result;
        }

        public Task<IEnumerable<LoginEvent>> GetLoginEventsAsync(string tenantId, IEnumerable<LoginEventType> types,
            IEnumerable<string> ids, CancellationToken cancellationToken, DateTime? fromDate = null, DateTime? toDate = null)
        {
            IEventStore eventStore = GetEventStore(tenantId);

            return eventStore.GetLoginEventsAsync(tenantId, types, ids, cancellationToken, fromDate, toDate);
        }

        public Task<IEnumerable<LoginEvent>> GetLoginEventsV2Async(string tenantId,
            QueryArguments<LoginEventWhere> query, CancellationToken cancellationToken)
        {
            IEventStore eventStore = GetEventStore(tenantId);

            return eventStore.GetLoginEventsV2Async(tenantId, query, cancellationToken);
        }

        public async Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId,
            CancellationToken cancellationToken, PermissionGroupWhere permissionGroupIds = null)
        {
            IRepository repository = GetRepository(tenantId);

            IEnumerable<PermissionGroup> result = await repository.GetPermissionGroupsAsync(tenantId, permissionGroupIds, cancellationToken);
            return result;
        }


        public async Task<Result> CreateTargetGroupAsync(string tenantId, CreateTargetGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result<string> result = await repository.CreateTargetGroupAsync(tenantId, command, cancellationToken);
            if (result.Status != "success")
                return new Result { Status = result.Status, Errors = result.Errors };

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new TargetGroupEvent(result.Value, TargetGroupEventType.createTargetGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> UpdateTargetGroupAsync(string tenantId, string id, UpdateTargetGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.UpdateTargetGroupAsync(tenantId, id, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new TargetGroupEvent(id, TargetGroupEventType.updateTargetGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> DeleteTargetGroupAsync(string tenantId, string id, DeleteCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);
            Result result = await repository.DeleteTargetGroupAsync(tenantId, id, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new TargetGroupEvent(id, TargetGroupEventType.deleteTargetGroup, DateTime.UtcNow) //NOTE: need to get deletedByid
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<TargetGroup> GetTargetGroupAsync(string tenantId, string id, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            TargetGroup result = await repository.GetTargetGroupAsync(tenantId, id, cancellationToken);
            return result;
        }

        public async Task<IEnumerable<TargetGroup>> GetAllTargetGroups(string tenantId,
            CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            IEnumerable<TargetGroup> result = await repository.GetAllTargetGroups(tenantId, cancellationToken);
            return result;
        }

        public async Task<Result<string>> CreatePermissionGroupAsync(string tenantId, CreatePermissionGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            //Find if permission group "default" exist
            if (command.Name == "default")
            {
                IEnumerable<PermissionGroup> permissionGps = await repository.GetPermissionGroupsAsync(tenantId,
                    new PermissionGroupWhere { Name = "default" }, cancellationToken);

                if (permissionGps.FirstOrDefault() != null)
                    return new Result<string>
                    {
                        Status = "failure",
                        Errors = new List<string> { "default permission group already exist" }
                    };
            }

            Result<string> result = await repository.CreatePermissionGroupAsync(tenantId, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new PermissionGroupEvent(result.Value, PermissionGroupEventType.createPermissionGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return new Result<string> { Status = eventResult.Status, Errors = eventResult.Errors, Value = result.Value };
        }

        public async Task<Result> AddUserToTargetGroupAsync(string tenantId, string id, string targetId,
            CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.AddUserToTargetGroupAsync(tenantId, id, targetId, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new TargetGroupEvent(id, TargetGroupEventType.addUserToTargetGroup, DateTime.UtcNow) //NOTE: need to addedByid
            {
                Values = JObject.FromObject(new AddUserToTargetGroupCommand
                {
                    UserId = targetId,
                }, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> UpdatePermissionGroupAsync(string tenantId, string id,
            UpdatePermissionGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.UpdatePermissionGroupAsync(tenantId, id, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new PermissionGroupEvent(id, PermissionGroupEventType.updatePermissionGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> AddTargetGroupToTargetGroupAsync(string tenantId, string id, string targetGroupId,
            CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.AddTargetGroupToTargetGroupAsync(tenantId, id, targetGroupId, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new TargetGroupEvent(id, TargetGroupEventType.addTargetGroupToTargetGroup, DateTime.UtcNow) //NOTE: need to addedByid
            {
                Values = JObject.FromObject(new AddTargetGroupToTargetGroupCommand
                {
                    TargetGroupId = targetGroupId,
                }, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> DeletePermissionGroupAsync(string tenantId, string id, DeleteCommand command,
            CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.DeletePermissionGroupAsync(tenantId, id, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new PermissionGroupEvent(id, PermissionGroupEventType.deletePermissionGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result> RemoveUserFromTargetGroupAsync(string tenantId, string id, string targetId,
            CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.RemoveUserFromTargetGroupAsync(tenantId, id, targetId, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new TargetGroupEvent(id, TargetGroupEventType.removeUserFromTargetGroup, DateTime.UtcNow) //NOTE: need to removedByid
            {
                Values = JObject.FromObject(new RemoveUserFromTargetGroupCommand
                {
                    UserId = targetId,
                }, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> UpdateLoginLockoutEndDate(string tenantId, string id, ChangeUserLockoutDateCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.UpdateLoginLockoutEndDate(tenantId, id, command.EndDateTime ?? DateTime.UtcNow.AddSeconds(-1), cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new LoginEvent(id, LoginEventType.updateLockoutEndDate, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> RemoveTargetGroupFromTargetGroupAsync(string tenantId, string id, string targetGroupId, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.RemoveTargetGroupFromTargetGroupAsync(tenantId, id, targetGroupId, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new TargetGroupEvent(id, TargetGroupEventType.removeTargetGroupFromTargetGroup, DateTime.UtcNow) //NOTE: need to removedByid
            {
                Values = JObject.FromObject(new RemoveTargetGroupFromTargetGroupCommand
                {
                    TargetGroupId = targetGroupId,
                }, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<IEnumerable<PermissionGroup>> GetAllPermissionGroupsAsync(string tenantId,
            CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            IEnumerable<PermissionGroup> result = await repository.GetAllPermissionGroupsAsync(tenantId, cancellationToken);
            return result;
        }

        public async Task<PermissionGroup> GetPermissionGroupAsync(string tenantId, string id,
            CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            PermissionGroup result = await repository.GetPermissionGroupAsync(tenantId, id, cancellationToken);
            return result;
        }

        public async Task<Result> AddPermissionToPermissionGroupAsync(string tenantId, string id,
            AddPermissionToPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.AddPermissionToPermissionGroupAsync(tenantId, id, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new PermissionGroupEvent(id, PermissionGroupEventType.addPermissionToPermissionGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> AddPermissionGroupToPermissionGroupAsync(string tenantId, string id,
            AddPermissionGroupToPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.AddPermissionGroupToPermissionGroupAsync(tenantId, id, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new PermissionGroupEvent(id, PermissionGroupEventType.addPermissionGroupToPermissionGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> AddEventAsync(string tenantId, LoginEvent loginEvent,
            CancellationToken cancellationToken)
        {
            var dbConfig = DbConfig.GetConfig(tenantId);

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, loginEvent, cancellationToken);

            return eventResult;
        }

        public async Task<Result> RemovePermissionFromPermissionGroupAsync(string tenantId, string id,
            RemovePermissionFromPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.RemovePermissionFromPermissionGroupAsync(tenantId, id, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new PermissionGroupEvent(id, PermissionGroupEventType.removePermissionFromPermissionGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> RemovePemissionGroupFromPermissionGroupAsync(string tenantId, string id,
            RemovePermissionGroupFromPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.RemovePemissionGroupFromPermissionGroupAsync(tenantId, id, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new PermissionGroupEvent(id, PermissionGroupEventType.removePermissionFromPermissionGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> AddLoginPermissionsToPermissionGroupAsync(string tenantId, string id,
            AddLoginPermissionsToPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.AddLoginPermissionsToPermissionGroupAsync(tenantId, id, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new PermissionGroupEvent(id, PermissionGroupEventType.addLoginPermissionsToPermissionGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> RemoveLoginPermissionsFromPermissionGroupAsync(string tenantId, string id,
            RemoveLoginPermissionsFromPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.RemoveLoginPermissionsFromPermissionGroupAsync(tenantId, id, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new PermissionGroupEvent(id, PermissionGroupEventType.removeLoginPermissionsFromPermissionGroup, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> AddTargetedPermissionSchemaToLogin(string tenantId,
            AddTargetedPermissionSchemaToLoginCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.AddTargetedPermissionSchemaToLoginForIds(tenantId, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new LoginEvent(command.LoginId, LoginEventType.addTargetedPermissionSchema, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public async Task<Result> RemoveTargetedPermissionSchemaFromLogin(string tenantId,
            RemoveTargetedPermissionSchemaFromLoginCommand command, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            Result result = await repository.RemoveTargetedPermissionSchemaFromLoginForIds(tenantId, command, cancellationToken);
            if (result.Status != "success")
                return result;

            IEventStore eventStore = GetEventStore(tenantId);
            Result eventResult = await eventStore.AddEventAsync(tenantId, new LoginEvent(command.LoginId, LoginEventType.removeTargetedPermissionSchema, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return eventResult;
        }

        public Task<IReadOnlyCollection<TargetedPermissionSchema>> GetTargetedPermissionSchemas(string tenantId,
            IReadOnlyCollection<string> targetedPermissionSchemaIds, CancellationToken cancellationToken)
        {
            DbConfig dbConfig = DbConfig.GetConfig(tenantId);
            IRepository repository = _repositories.FirstOrDefault(r => r.ProviderId == dbConfig.ProviderId);

            return repository.GetTargetedPermissionSchemas(tenantId, targetedPermissionSchemaIds, cancellationToken);
        }

        public async Task<Result> UpdateLatestOtpAsync(string tenantId, string loginId, string latestOtp, CancellationToken cancellationToken)
        {
            IRepository repository = GetRepository(tenantId);

            return await repository.UpdateLatestOtpAsync(tenantId, loginId, latestOtp, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> CreateSendNotificationScheduleAsync(string tenantId, UpsertSendNotificationScheduleCommand command, CancellationToken cancellationToken)
        {
            return await _sendNotificationScheduleRepository.CreateAsync(tenantId, command, cancellationToken);
        }

        public async Task<IEnumerable<SendNotificationSchedule>> GetScheduledNotifications(string tenantId, CancellationToken cancellationToken)
        {
            return await _sendNotificationScheduleRepository.QueryAsync(tenantId, new QueryArguments<Filter<SendNotificationScheduleFilter>>{
                Where = new Filter<SendNotificationScheduleFilter>
                {
                    And = new List<Filter<SendNotificationScheduleFilter>>
                    {
                        new Filter<SendNotificationScheduleFilter>
                        {
                            Where = new SendNotificationScheduleFilter
                            {
                                Status = "Scheduled"
                            }
                        },
                        new Filter<SendNotificationScheduleFilter>
                        {
                            Where = new SendNotificationScheduleFilter
                            {
                                ScheduleToSendAt_lt = DateTime.UtcNow
                            }
                        }
                    }
                    
                }
            }, cancellationToken);
        }

        public async Task<IEnumerable<SendNotificationSchedule>> QueryScheduledNotifications(string tenantId, QueryArguments<Filter<SendNotificationScheduleFilter>> filter, CancellationToken cancellationToken)
        {
            return await _sendNotificationScheduleRepository.QueryAsync(tenantId, filter, cancellationToken);
        }

        public async Task<Result> UpdateSendNotificationScheduleAsync(string tenantId, UpsertSendNotificationScheduleCommand command, CancellationToken cancellationToken)
        {
            return await _sendNotificationScheduleRepository.UpdateAsync(tenantId, command, cancellationToken);
        }

        private IRepository GetRepository(string tenantId)
        {
            var dbConfig = DbConfig.GetConfig(tenantId);

            return _repositories.FirstOrDefault(r => r.ProviderId == dbConfig.ProviderId);
        }

        private ITenantRepository GetTenantRepository(string tenantId)
        {
            var dbConfig = DbConfig.GetConfig(tenantId);

            return _tenantRepositories.FirstOrDefault(r => r.ProviderId == dbConfig.ProviderId);
        }

        private IEventStore GetEventStore(string tenantId)
        {
            var dbConfig = DbConfig.GetConfig(tenantId);

            return _eventStores.FirstOrDefault(r => r.ProviderId == dbConfig.ProviderId);
        }
    }
}
