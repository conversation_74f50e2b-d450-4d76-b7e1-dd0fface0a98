﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain
{
    public interface IEventStore
    {
        string ProviderId { get; }

        Task<Result> AddEventAsync(string tenantId, AppEvent authEvent, CancellationToken cancellationToken);
        Task<Result> AddEventAsync(string tenantId, LoginEvent authEvent, CancellationToken cancellationToken);
        Task<Result> AddEventAsync(string tenantId, PermissionGroupEvent authEvent, CancellationToken cancellationToken);
        Task<Result> AddEventAsync(string tenantId, TargetGroupEvent authEvent, CancellationToken cancellationToken);

        Task<IEnumerable<AppEvent>> GetAppEventsAsync(string tenantId, IEnumerable<AppEventType> types,
            IEnumerable<string> appIds, CancellationToken cancellationToken, DateTime? fromDate = null,
            DateTime? toDate = null);
        Task<IEnumerable<LoginEvent>> GetLoginEventsAsync(string tenantId, IEnumerable<LoginEventType> types,
            IEnumerable<string> loginIds, CancellationToken cancellationToken, DateTime? fromDate = null,
            DateTime? toDate = null);
        Task<IEnumerable<LoginEvent>> GetLoginEventsV2Async(string tenantId,
                    QueryArguments<LoginEventWhere> query,
                    CancellationToken cancellationToken);
        Task<IEnumerable<PermissionGroupEvent>> GetPermissionGroupEventsAsync(string tenantId,
            IEnumerable<PermissionGroupEventType> types, IEnumerable<string> permissionGroupIds,
            CancellationToken cancellationToken, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<TargetGroupEvent>> GetTargetGroupEventsAsync(string tenantId,
            IEnumerable<TargetGroupEventType> types, IEnumerable<string> targetGroupIds,
            CancellationToken cancellationToken, DateTime? fromDate = null, DateTime? toDate = null);
    }

    public enum AppEventType
    {
        createApp,
        updateApp,
        deleteApp
    }

    public enum LoginEventType
    {
        createLogin,
        verifyCode,
        verifyResetPasswordCode,
        sendCode,
        resendEmail,
        updateLogin,
        deleteLogin,
        confirmEmail,
        resetPassword,
        forgotPassword,
        changePassword,
        changeExpiredPassword,
        addPermission,
        removePermission,
        updateLockoutEndDate,
        requestAccessTokenSuccess,
        addTargetedPermissionSchema,
        removeTargetedPermissionSchema,
        deactivate,
        reactivate
    }

    public enum PermissionGroupEventType
    {
        createPermissionGroup,
        updatePermissionGroup,
        deletePermissionGroup,
        addPermissionToPermissionGroup,
        addPermissionGroupToPermissionGroup,
        removePermissionFromPermissionGroup,
        removePermissionGroupFromPermissionGroup,
        addLoginPermissionsToPermissionGroup,
        removeLoginPermissionsFromPermissionGroup
    }

    public enum TargetGroupEventType
    {
        createTargetGroup,
        updateTargetGroup,
        deleteTargetGroup,
        addUserToTargetGroup,
        removeUserFromTargetGroup,
        addTargetGroupToTargetGroup,
        removeTargetGroupFromTargetGroup
    }

    public abstract class AuthEvent
    {
        public string Id { get; set; }
        public JToken Values { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class AppEvent : AuthEvent
    {
        public string AppId { get; set; }
        public AppEventType Type { get; set; }

        public AppEvent(string appId, AppEventType type, DateTime timestamp)
        {
            AppId = appId;
            Type = type;
            Timestamp = timestamp;
        }
    }

    public class LoginEvent : AuthEvent
    {
        public string LoginId { get; set; }
        public LoginEventType Type { get; set; }

        public LoginEvent(string loginId, LoginEventType type, DateTime timestamp)
        {
            LoginId = loginId;
            Type = type;
            Timestamp = timestamp;
        }
    }

    public class PermissionGroupEvent : AuthEvent
    {
        public string PermissionGroupId { get; set; }
        public PermissionGroupEventType Type { get; set; }

        public PermissionGroupEvent(string permissionGroupId, PermissionGroupEventType type, DateTime timestamp)
        {
            PermissionGroupId = permissionGroupId;
            Type = type;
            Timestamp = timestamp;
        }
    }

    public class TargetGroupEvent : AuthEvent
    {
        public string TargetGroupId { get; set; }
        public TargetGroupEventType Type { get; set; }

        public TargetGroupEvent(string targetGroupId, TargetGroupEventType type, DateTime timestamp)
        {
            TargetGroupId = targetGroupId;
            Type = type;
            Timestamp = timestamp;
        }
    }

    public sealed class LoginEventWhere : Where
    {
        public IEnumerable<string> LoginIds { get; set; }
        public IEnumerable<LoginEventType> Types { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }
}
