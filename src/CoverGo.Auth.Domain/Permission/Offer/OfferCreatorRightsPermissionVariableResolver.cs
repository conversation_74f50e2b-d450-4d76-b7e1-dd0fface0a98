﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Offer;

public class OfferCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readOffers", "writeOffers", "overrideOffers", "updateOffers", "deleteOffers"
    };
    private readonly ICaseService _caseService;

    public OfferCreatorRightsPermissionVariableResolver(ICaseService caseService)
    {
        _caseService = caseService;
    }
    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && claim.Value == "{creatorRights}";
    }
    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new QueryArguments
        {
            Where = new CaseWhere
            {
                Proposals_contains = new ProposalWhere 
                {
                    Offers_contains = new OfferWhere { CreatedById = context.User.Id }
                }
            }
        };

        return _caseService.GetOfferIdsAsync(context.TenantId, where, cancellationToken);
    }
}