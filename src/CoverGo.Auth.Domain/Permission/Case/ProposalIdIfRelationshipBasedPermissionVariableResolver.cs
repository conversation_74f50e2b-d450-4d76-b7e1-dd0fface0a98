﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class ProposalIdIfRelationshipBasedPermissionVariableResolver : LinkedPermissionVariableResolver
{
    private const string Prefix = "{proposalIdIf";
    private const string Suffix = "IsHolder}";

    private readonly ICaseService _caseService;

    public ProposalIdIfRelationshipBasedPermissionVariableResolver(
        ICaseService caseService, IEntityService entityService) 
        : base(entityService, Prefix, Suffix)
    {
        _caseService = caseService;
    }

    protected override async Task<IEnumerable<string>> ResolveLinksAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        IEnumerable<string> linkedIds,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new CaseWhere {
                HolderId_in = linkedIds
            }
        };
        var cases = await _caseService.GetAsync(context.TenantId, query, cancellationToken);

        return cases.SelectMany(c => c.Proposals?.Select(p => p.Id)
                                     ?? Enumerable.Empty<string>());
    }
}