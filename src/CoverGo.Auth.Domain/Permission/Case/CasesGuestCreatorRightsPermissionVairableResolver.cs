﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case
{
    public class CasesGuestCreatorRightsPermissionVairableResolver : IPermissionVariableResolver
    {
        private static readonly HashSet<string> PermissionTypes = new() {
        "readCases", "writeCases", "updateCases", "deleteCases"
    };

        private readonly ICaseService _caseService;

        public CasesGuestCreatorRightsPermissionVairableResolver(ICaseService caseService)
        {
            _caseService = caseService;
        }
        public bool CanResolve(System.Security.Claims.Claim claim)
        {
            return (PermissionTypes.Contains(claim.Type) && claim.Value.Contains("guestCreatorRights"));
        }
        public Task<IEnumerable<string>> ResolveAsync(PermissionContext context, System.Security.Claims.Claim claim, CancellationToken cancellationToken = default)
        {
            CaseWhere where = GetCaseWhere(loginId: context.User.Id, duration: GetPermissionsGraceDurationInMinutes(claim));

            return _caseService.GetIdsAsync(context.TenantId, new QueryArguments { Where = where }, cancellationToken);
        }

        int GetPermissionsGraceDurationInMinutes(System.Security.Claims.Claim claim)
        {
            var guestCreator = claim.Value.Contains("guestCreatorRights:") ? claim.Value : string.Empty;
            
            return !string.IsNullOrEmpty(guestCreator) ? JObject.Parse(guestCreator).Value<int>("guestCreatorRights") : 0;
        }

        CaseWhere GetCaseWhere(string loginId, int duration)
        {
            return
                duration > 0
                    ? new CaseWhere
                    {
                        And = new()
                        {
                                new() { CreatedById = loginId },
                                new() { CreatedAt_gt = DateTime.Now.AddMinutes(-duration) }
                        }
                    }
                    : new CaseWhere { CreatedById = loginId };
        }
    }
}
