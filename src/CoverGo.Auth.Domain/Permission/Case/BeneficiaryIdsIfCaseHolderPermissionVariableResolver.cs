﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class BeneficiaryIdsIfCaseHolderPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "{beneficiaryIdsIfCaseHolder}"
    };
    private readonly ICaseService _caseService;

    public BeneficiaryIdsIfCaseHolderPermissionVariableResolver(
        ICaseService caseService)
    {
        _caseService = caseService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Any(pt => claim.Value.Contains(pt));
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments
        {
            Where = new CaseWhere
            {
                HolderId = context.User.EntityId
            }
        };
        var cases = await _caseService.GetAsync(context.TenantId, query, cancellationToken);

        return cases.SelectMany(c => c.BeneficiaryEligibilities?
            .Select(b => b.ContractEntity?.Id) ?? 
                                     Enumerable.Empty<string>());
    }
}