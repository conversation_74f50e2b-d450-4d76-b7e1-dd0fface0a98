﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class CaseIdIfStakeholderPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "{caseIdIfStakeholder}"
    };

    private readonly ICaseService _caseService;

    public CaseIdIfStakeholderPermissionVariableResolver(ICaseService caseService)
    {
        _caseService = caseService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Any(pt => claim.Value.Contains(pt));
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments
        {
            Where = new CaseWhere
            {
                Stakeholders_contains = new StakeholderWhere
                {
                    EntityId = context.User.EntityId
                }
            }
        };

        IEnumerable<string> ids = await _caseService.GetIdsAsync(context.TenantId, query, cancellationToken);
        if (ids?.Any() ?? false)
        {
            IEnumerable<string> replacedValues = ids.Select(i => claim.Value.Replace(PermissionTypes.First(), i));
            ids = ids.Concat(replacedValues).Distinct();
        }

        return ids;
    }
}