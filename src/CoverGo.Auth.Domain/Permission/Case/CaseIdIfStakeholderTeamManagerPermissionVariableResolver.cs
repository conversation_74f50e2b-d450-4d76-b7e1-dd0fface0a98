﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class CaseIdIfStakeholderTeamManagerPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "{caseIdIfStakeholderTeamManager}"
    };

    private readonly ICaseService _caseService;
    private readonly ICoverGoChannelManagementService _channelManagementService;
    private readonly IAuthService _authService;

    public CaseIdIfStakeholderTeamManagerPermissionVariableResolver(ICaseService caseService, ICoverGoChannelManagementService channelManagementService, IAuthService authService)
    {
        _caseService = caseService;
        _channelManagementService = channelManagementService;
        _authService = authService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim) => PermissionTypes.Any(claim.Value.Contains);

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        IEnumerable<string> allTeamMemberIds = await _channelManagementService.GetTeamMembersLoginIdsAsync(context.User.Id, cancellationToken);
        if (!allTeamMemberIds?.Any() ?? false)
            return Enumerable.Empty<string>();

        IEnumerable<string> entityIds = (await _authService.GetLoginsAsync(context.TenantId, new LoginWhere
        {
            Ids = allTeamMemberIds,
            ExcludePermissions = true
        }, null, null, null, cancellationToken))?.Select(l => l.EntityId);
        if (!entityIds?.Any() ?? false)
            return Enumerable.Empty<string>();

        var query = new QueryArguments
        {
            Where = new CaseWhere
            {
                Stakeholders_contains = new StakeholderWhere
                {
                    EntityId_in = entityIds?.ToList()
                }
            }
        };

        IEnumerable<string> ids = await _caseService.GetIdsAsync(context.TenantId, query, cancellationToken);
        if (ids?.Any() ?? false)
        {
            IEnumerable<string> replacedValues = ids.Select(i => claim.Value.Replace(PermissionTypes.First(), i));
            ids = ids.Concat(replacedValues).Distinct();
        }

        return ids;
    }
}