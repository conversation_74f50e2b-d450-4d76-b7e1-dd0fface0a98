﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class CaseIdIfRelationshipBasedPermissionVariableResolver : LinkedPermissionVariableResolver
{
    private const string Prefix = "{caseIdIf";
    private const string Suffix = "IsHolder}";

    private readonly ICaseService _caseService;

    public CaseIdIfRelationshipBasedPermissionVariableResolver(
        ICaseService caseService,
        IEntityService entityService)
        : base(entityService, Prefix, Suffix)
    {
        _caseService = caseService;
    }

    protected override Task<IEnumerable<string>> ResolveLinksAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        IEnumerable<string> linkedIds,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new CaseWhere {
                HolderId_in = linkedIds
            }
        };
        return _caseService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}