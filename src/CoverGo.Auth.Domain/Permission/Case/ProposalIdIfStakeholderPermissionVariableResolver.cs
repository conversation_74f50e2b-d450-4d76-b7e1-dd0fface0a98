﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class ProposalIdIfStakeholderPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionValues = new() {
        "{proposalIdIfStakeholder}"
    };

    private readonly ICaseService _caseService;

    public ProposalIdIfStakeholderPermissionVariableResolver(ICaseService caseService)
    {
        _caseService = caseService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionValues.Any(pt => claim.Value.Contains(pt));
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments
        {
            Where = new CaseWhere
            {
                Stakeholders_contains = new StakeholderWhere
                {
                    EntityId = context.User.EntityId
                }
            }
        };

        IEnumerable<string> ids = await _caseService.GetProposalIdsAsync(context.TenantId, query, cancellationToken);
        if (ids?.Any() ?? false)
        {
            IEnumerable<string> replacedValues = ids.Select(i => claim.Value.Replace(PermissionValues.First(), i));
            ids = ids.Concat(replacedValues).Distinct();
        }

        return ids;
    }
}