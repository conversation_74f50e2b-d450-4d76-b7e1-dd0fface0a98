﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Domain.Utils;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class CaseIdIfSourceContainsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readCases", "writeCases", "updateCases", "deleteCases"
    };
    private readonly ICaseService _caseService;

    public CaseIdIfSourceContainsPermissionVariableResolver(ICaseService caseService)
    {
        _caseService = caseService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && 
               claim.Value.Contains("caseIdIfSourceContains=");
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var sourceContainsList = StringUtils.ExtractSourceIds(claim.Value);
        var query = new QueryArguments {
            Where = new CaseWhere {
                Or = sourceContainsList.Select(sc => new CaseWhere {
                    Source_contains = sc
                }).ToList()
            }
        };

        return _caseService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}