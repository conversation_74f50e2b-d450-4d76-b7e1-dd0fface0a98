﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class CaseIdIfProposalReferrerPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "{caseIdIfProposalReferrer}"
    };
    private readonly IEntityService _entityService;
    private readonly ICaseService _caseService;

    public CaseIdIfProposalReferrerPermissionVariableResolver(
        IEntityService entityService,
        ICaseService caseService)
    {
        _entityService = entityService;
        _caseService = caseService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Any(pt => claim.Value.Contains(pt));
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var entity = await GetEntityAsync(context, cancellationToken);

        if(entity is null)
        {
            return Enumerable.Empty<string>();
        }
        
        var query = new QueryArguments {
            Where = new CaseWhere {
                Proposals_contains = new ProposalWhere {
                    ReferralCode = entity.InternalCode
                }
            }
        };
        return await _caseService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }

    private async Task<OtherServices.Entity> GetEntityAsync(
        PermissionContext context,
        CancellationToken cancellationToken)
    {
        var where = new EntityWhere {
            Id = context.User.EntityId
        };
        var entities = await _entityService.GenericQueryAsync(context.TenantId, where, cancellationToken);

        return entities.FirstOrDefault();
    }
}