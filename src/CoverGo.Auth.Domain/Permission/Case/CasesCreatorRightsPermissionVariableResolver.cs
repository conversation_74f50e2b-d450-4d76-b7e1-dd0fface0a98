﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class CasesCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readCases", "writeCases", "updateCases", "deleteCases"
    };

    private readonly ICaseService _caseService;

    public CasesCreatorRightsPermissionVariableResolver(ICaseService caseService)
    {
        _caseService = caseService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return (PermissionTypes.Contains(claim.Type) && claim.Value == "{creatorRights}") ||
               claim.Value.Contains("{caseIdIfCreator}");
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new CaseWhere {
                CreatedById = context.User.Id
            }
        };

        return _caseService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}