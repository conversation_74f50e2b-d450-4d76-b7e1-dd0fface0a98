﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Advisor;

public class ReviewCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readReviews", "writeReviews"
    };
    private readonly IAdvisorService _advisorService;

    public ReviewCreatorRightsPermissionVariableResolver(
        IAdvisorService advisorService)
    {
        _advisorService = advisorService;
    }
    
    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && claim.Value == "{creatorRights}";
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments
        {
            Where = new ReviewWhere { CreatedById = context.User.Id },
        };

        return _advisorService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}