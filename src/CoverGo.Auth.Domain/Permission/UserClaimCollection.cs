﻿using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Auth.Domain.Permission;

public class UserClaimCollection : IEnumerable<KeyValuePair<string, List<System.Security.Claims.Claim>>>
{
    public static readonly UserClaimCollection Empty = new();

    readonly Dictionary<string, List<System.Security.Claims.Claim>> _groupedClaims = new();

    public UserClaimCollection(Dictionary<string, List<System.Security.Claims.Claim>> groupedClaims)
    {
        _groupedClaims = groupedClaims;
    }

    public UserClaimCollection(IEnumerable<System.Security.Claims.Claim> claims)
    {
        AddRange(claims);
    }
    public UserClaimCollection()
    {
    }
    
    public IReadOnlyCollection<string> PermissionNames => _groupedClaims.Keys;

    public IEnumerator<KeyValuePair<string, List<System.Security.Claims.Claim>>> GetEnumerator() =>
        _groupedClaims.GetEnumerator();

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

    public void AddRange(IEnumerable<KeyValuePair<string, IEnumerable<string>>> claims)
    {
        var groupedClaims = claims.Select(kv => 
            new KeyValuePair<string, List<System.Security.Claims.Claim>>(
                kv.Key,
                kv.Value.Distinct().Select(v => new System.Security.Claims.Claim(kv.Key, v)).ToList()))
            .ToDictionary(g => g.Key, g => g.Value);
        AddRangeInternal(groupedClaims);
    }

    public void AddRange(IEnumerable<System.Security.Claims.Claim> claims)
    {
        var groupedClaims = claims.GroupBy(c => c.Type)
            .ToDictionary(g => g.Key, g => g.ToList());

        AddRangeInternal(groupedClaims);
    }

    public void AddRange(UserClaimCollection collection)
    {
        AddRangeInternal(collection);
    }

    public void Add(System.Security.Claims.Claim claim)
    {
        AddRangeInternal(
            new[]
            {
                new KeyValuePair<string, List<System.Security.Claims.Claim>>(
                    claim.Type, new List<System.Security.Claims.Claim> { claim })
            });
    }

    public bool HasClaim(string type, string value)
    {
        return _groupedClaims.TryGetValue(type, out var list) &&
               list.Any(c => c.Value == value);
    }

    private void AddRangeInternal(IEnumerable<KeyValuePair<string, List<System.Security.Claims.Claim>>> items)
    {
        foreach ((var type, var value) in items) {
            if (!_groupedClaims.TryAdd(type, value)) {
                _groupedClaims[type].AddRange(value);
            }
        }
    }

    public bool TryGetValue(string claimType, out List<System.Security.Claims.Claim> value)
    {
        return _groupedClaims.TryGetValue(claimType, out value);
    }
}