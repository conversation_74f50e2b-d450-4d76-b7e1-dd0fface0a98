﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readPolicies",
        "writePolicies",
        "updatePolicies",
        "deletePolicies",
        "cancelPolicies",
        "writeMemberMovements:submit",
        "writeMemberMovements:draft",
        "readMemberMovements:terminationReasons",
        "writeMemberMovements:terminationReasons"
    };
    private readonly IPolicyService _policyService;

    public PolicyCreatorRightsPermissionVariableResolver(IPolicyService policyService)
    {
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{creatorRights}") &&
               PermissionTypes.Contains(claim.Type);
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new PolicyWhere { CreatedById = context.User.Id };

        return _policyService.GetIdsAsync(context.TenantId, where, cancellationToken);
    }
}