﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyIfUpdateIndividualsPermissionVariableResolver : 
    IPermissionVariableResolver, IHasDependencyOnPermissionType
{
    const string DependentPermissionType = "updateIndividuals";
    private readonly IPolicyService _policyService;

    public PolicyIfUpdateIndividualsPermissionVariableResolver(IPolicyService policyService)
    {
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{policyIdIf{allowedUpdateIndividuals}IsHolder}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var updateIndividualIds = context.ResolvedPermissions[DependentPermissionType];

        var where = new PolicyWhere {
            ContractHolder = new EntityWhere {
                Id_in = updateIndividualIds.ToList()
            }
        };
        return await _policyService.GetIdsAsync(context.TenantId, where, cancellationToken);
    }

    public string GetDependentPermissionType(System.Security.Claims.Claim _) => DependentPermissionType;
}