﻿using CoverGo.Auth.Domain.Utils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Policies.Client;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyIdIfSourceEqualsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readPolicies", "writePolicies", "updatePolicies", "deletePolicies"
    };
    private readonly IPoliciesClient _policyService;

    public PolicyIdIfSourceEqualsPermissionVariableResolver(IPoliciesClient policyService)
    {
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) &&
               claim.Value.Contains("policyIdIfSourceEquals=");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var sourceContainsList = StringUtils.ExtractSourceIds(claim.Value);
        var query = new QueryArgumentsOfPolicyWhere
        {
            Where = new PolicyWhere
            {
                Or = sourceContainsList.Select(sc => new PolicyWhere
                {
                     Source = sc
                }).ToList()
            }
        };

        return await _policyService.Policy_GetPolicyIdsAsync(context.TenantId, query, cancellationToken);
    }
}