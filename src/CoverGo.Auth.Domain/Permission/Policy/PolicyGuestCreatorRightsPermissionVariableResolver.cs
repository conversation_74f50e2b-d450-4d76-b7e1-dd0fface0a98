﻿using CoverGo.Auth.Domain.OtherServices;
using MongoDB.Driver.Core.Operations;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy
{
    public class PolicyGuestCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
    {
        private static readonly HashSet<string> PermissionTypes = new() {
            "readPolicies", "writePolicies", "cancelPolicies", "updatePolicies", "deletePolicies"
        };
        private readonly IPolicyService _policyService;

        public PolicyGuestCreatorRightsPermissionVariableResolver(IPolicyService policyService)
        {
            _policyService = policyService;
        }
        public bool CanResolve(System.Security.Claims.Claim claim)
        {
            return (PermissionTypes.Contains(claim.Type) && claim.Value.Contains("guestCreatorRights"));
        }
        public Task<IEnumerable<string>> ResolveAsync(PermissionContext context, System.Security.Claims.Claim claim, CancellationToken cancellationToken = default)
        {
            PolicyWhere where = GetPolicyWhere(loginId: context.User.Id, duration: GetPermissionsGraceDurationInMinutes(claim));

            return _policyService.GetIdsAsync(context.TenantId, where, cancellationToken);
        }

        int GetPermissionsGraceDurationInMinutes(System.Security.Claims.Claim claim)
        {
            var guestCreator = claim.Value.Contains("guestCreatorRights:") ? claim.Value : string.Empty;

            return !string.IsNullOrEmpty(guestCreator) ? JObject.Parse(guestCreator).Value<int>("guestCreatorRights") : 0;
        }

        PolicyWhere GetPolicyWhere(string loginId, int duration)
        {
            return
                duration > 0
                    ? new PolicyWhere
                    {
                        And = new()
                        {
                                new() { CreatedById = loginId },
                                new() { CreatedAt_gt = DateTime.Now.AddMinutes(-duration) }
                        }
                    }
                    : new PolicyWhere { CreatedById = loginId };
        }
    }
}
