﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Case;

public class PolicyIdIfStakeholderTeamManagerPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "{policyIdIfStakeholderTeamManager}"
    };

    private readonly IPolicyService _policyService;
    private readonly ICoverGoChannelManagementService _channelManagementService;
    private readonly IAuthService _authService;

    public PolicyIdIfStakeholderTeamManagerPermissionVariableResolver(IPolicyService policyService, ICoverGoChannelManagementService channelManagementService, IAuthService authService)
    {
        _policyService = policyService;
        _channelManagementService = channelManagementService;
        _authService = authService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim) => PermissionTypes.Any(claim.Value.Contains);

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        IEnumerable<string> allTeamMemberIds = await _channelManagementService.GetTeamMembersLoginIdsAsync(context.User.Id, cancellationToken);
        if (!allTeamMemberIds?.Any() ?? false)
            return Enumerable.Empty<string>();

        IEnumerable<string> entityIds = (await _authService.GetLoginsAsync(context.TenantId, new LoginWhere
        {
            Ids = allTeamMemberIds,
            ExcludePermissions = true
        }, null, null, null, cancellationToken))?.Select(l => l.EntityId);
        if (!entityIds?.Any() ?? false)
            return Enumerable.Empty<string>();

        var query = new PolicyWhere
        {
            Stakeholders_contains = new StakeHolderWhere
            {
                EntityId_in = entityIds.ToList()
            }
        };

        IEnumerable<string> ids = await _policyService
            .GetIdsAsync(context.TenantId, query, cancellationToken);

        return ids;
    }
}