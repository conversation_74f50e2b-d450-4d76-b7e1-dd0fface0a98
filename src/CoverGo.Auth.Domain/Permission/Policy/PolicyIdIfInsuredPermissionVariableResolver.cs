﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyIdIfInsuredPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IPolicyService _policyService;

    public PolicyIdIfInsuredPermissionVariableResolver(IPolicyService policyService)
    {
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{policyIdIfInsured}");
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new PolicyWhere {
            ContractInsured_some = new EntityWhere {
                Id_in = new List<string> { context.User.EntityId }
            }
        };

        return _policyService.GetIdsAsync(context.TenantId, where, cancellationToken);
    }
}