﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyIdIfRenewalPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IPolicyService _policyService;

    public PolicyIdIfRenewalPermissionVariableResolver(IPolicyService policyService)
    {
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{policyIdIfRenewal}");
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new PolicyWhere { IsRenewal = true };

        return _policyService.GetIdsAsync(context.TenantId, where, cancellationToken);
    }
}