﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyIdIfReferrerPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IPolicyService _policyService;
    private readonly IEntityService _entityService;

    public PolicyIdIfReferrerPermissionVariableResolver(
        IPolicyService policyService,
        IEntityService entityService)
    {
        _policyService = policyService;
        _entityService = entityService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{policyIdIfReferrer}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var entity = await GetEntityAsync(context, cancellationToken);

        if (entity == null) {
            return Enumerable.Empty<string>();
        }

        var where = new PolicyWhere { ReferralCode = entity.InternalCode };
        return await _policyService.GetIdsAsync(context.TenantId, where, cancellationToken);
    }

    private async Task<OtherServices.Entity> GetEntityAsync(
        PermissionContext context,
        CancellationToken cancellationToken = default)
    {
        var where = new EntityWhere {
            Id = context.User.EntityId
        };

        return (await _entityService.GenericQueryAsync(context.TenantId, where, cancellationToken)).FirstOrDefault();
    }
}