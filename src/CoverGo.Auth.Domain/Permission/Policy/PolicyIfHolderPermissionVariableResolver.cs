﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyIfHolderPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IPolicyService _policyService;

    public PolicyIfHolderPermissionVariableResolver(IPolicyService policyService)
    {
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{policyIdIfHolder}") ||
               claim.Value.Contains("{policyNumberIfHolder}") ||
               claim.Value.Contains("{insuredIdsIfHolder}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new PolicyWhere
        {
            ContractHolder = new EntityWhere
            {
                Id = context.User.EntityId
            }
        };
        var policies = await _policyService.GetAsync(context.TenantId, where, cancellationToken);

        if (claim.Value.Contains("{policyIdIfHolder}"))
        {
            return policies.Select(p => p.Id);
        }
        if (claim.Value.Contains("{policyNumberIfHolder}"))
        {
            return policies.Select(p => p.IssuerNumber);
        }
        if (claim.Value.Contains("{insuredIdsIfHolder}"))
        {
            return policies
                .SelectMany(p => p.ContractInsured?
                                        .Select(c => c.Id) 
                                        ?? Enumerable.Empty<string>());
        }
        return Enumerable.Empty<string>();
    }
}