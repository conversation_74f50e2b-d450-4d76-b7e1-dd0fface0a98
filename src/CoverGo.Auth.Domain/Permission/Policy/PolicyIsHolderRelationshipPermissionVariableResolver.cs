﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyIsHolderRelationshipPermissionVariableResolver : LinkedPermissionVariableResolver
{
    private const string Prefix = "{policyIdIf";
    private const string Suffix = "IsHolder}";

    private readonly IPolicyService _policyService;

    public PolicyIsHolderRelationshipPermissionVariableResolver(
        IPolicyService policyService, IEntityService entityService) 
        : base(entityService, Prefix, Suffix)
    {
        _policyService = policyService;
    }

    protected override Task<IEnumerable<string>> ResolveLinksAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        IEnumerable<string> linkedIds,
        CancellationToken cancellationToken = default)
    {
        var where = new PolicyWhere {
            ContractHolder = new EntityWhere {
                Id_in = linkedIds.ToList()
            }
        };
        return _policyService.GetIdsAsync(context.TenantId, where, cancellationToken);
    }
}