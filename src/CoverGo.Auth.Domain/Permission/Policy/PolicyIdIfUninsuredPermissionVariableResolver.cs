﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyIdIfUninsuredPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IPolicyService _policyService;

    public PolicyIdIfUninsuredPermissionVariableResolver(IPolicyService policyService)
    {
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{policyIdIfUninsured}");
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new PolicyWhere {
            ContractTerminated_some = new EntityWhere {
                Id_in = new List<string> { context.User.EntityId }
            }
        };

        return _policyService.GetIdsAsync(context.TenantId, where, cancellationToken);
    }
}