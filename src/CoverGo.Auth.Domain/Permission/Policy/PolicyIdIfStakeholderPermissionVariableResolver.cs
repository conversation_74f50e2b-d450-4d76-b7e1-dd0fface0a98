﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Policy;

public class PolicyIdIfStakeholderPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IPolicyService _policyService;

    public PolicyIdIfStakeholderPermissionVariableResolver(IPolicyService policyService)
    {
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{policyIdIfStakeholder}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new PolicyWhere
        {
            Stakeholders_contains = new StakeHolderWhere
            {
                EntityId = context.User.EntityId
            }
        };

        IEnumerable<string> ids = await _policyService
            .GetIdsAsync(context.TenantId, query, cancellationToken);
        
        return ids;
    }
}