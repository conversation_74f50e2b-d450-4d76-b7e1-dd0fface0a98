﻿using CoverGo.Auth.Domain.Abstraction;
using CoverGo.Auth.Domain.Permission.Queries;
using CoverGo.Auth.Domain.Utils;
using CoverGo.FeatureManagement;
using IdentityServer4.Stores;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission;

public class PermissionContextBuilder
{

    public static readonly string[] DefaultPermissionNames = {
        UserClaim.ReadLogins.ToString(),
        UserClaim.WriteLogins.ToString(),
        UserClaim.AccessRestrictedContent.ToString()
    };

    readonly IQueryHandler<GetLoginQuery, MongoLoginDao> _loginQueryHandler;
    readonly IQueryHandler<GetLoginClaimsQuery, UserClaimCollection> _userClaimsQueryHandler;
    readonly IResourceStore _resourceStore;
    readonly IMultiTenantFeatureManager _multiTenantFeatureManager;
    readonly IPermissionService _permissionService;

    public PermissionContextBuilder(
        IQueryHandler<GetLoginQuery, MongoLoginDao> loginQueryHandler,
        IQueryHandler<GetLoginClaimsQuery, UserClaimCollection> userClaimsQueryHandler,
        IResourceStore resourceStore, IMultiTenantFeatureManager multiTenantFeatureManager,
        IPermissionService permissionService)
    {
        _loginQueryHandler = loginQueryHandler;
        _userClaimsQueryHandler = userClaimsQueryHandler;
        _resourceStore = resourceStore;
        _multiTenantFeatureManager = multiTenantFeatureManager;
        _permissionService = permissionService;
    }

    public async Task<PermissionContext> BuildAsync(
        string tenantId,
        string loginId,
        CancellationToken cancellationToken = default)
    {
        var user = await _loginQueryHandler.Handle(
            new GetLoginQuery(tenantId, loginId), cancellationToken);

        if (user == null)
        {
            return PermissionContext.Empty;
        }

        var userClaims = await _userClaimsQueryHandler.Handle(
            new GetLoginClaimsQuery(tenantId, user), cancellationToken);
        bool IsAdminUser() => userClaims.HasClaim("role", "admin");

        if (IsAdminUser())
        {
            await AddAdminPermissionsIfUserIsAdmin(userClaims);
            return new PermissionContext(tenantId, user, userClaims);
        }

        AddDefaultPermissions(userClaims, user);
        AddWriteMemberMovementPermissions(userClaims);
        await AddContentRestrictionPermissionsAsync(user, userClaims, tenantId, cancellationToken);

        return new PermissionContext(tenantId, user, userClaims);
    }

    private async Task AddContentRestrictionPermissionsAsync(MongoLoginDao user, UserClaimCollection userClaims, string tenantId, CancellationToken cancellationToken)
    {
        bool isEnableContentRestriction =
            await _multiTenantFeatureManager.IsEnabled(FeatureNames.EnableContentRestriction, tenantId);

        if (!isEnableContentRestriction)
        {
            userClaims.Add(new System.Security.Claims.Claim(UserClaim.AccessRestrictedContent.ToString(), AccessRestrictedContentValues.Full));
        }
        else
        {
            var userPermissionGroups = await _permissionService.GetPermissionGroupsFromClaims(tenantId, user, cancellationToken);
            UserClaimsHelper.TryAddAccessRestrictedContentPermission(userClaims, userPermissionGroups);
        }
    }

    private async Task AddAdminPermissionsIfUserIsAdmin(UserClaimCollection userClaims)
    {
        var apiResource = await _resourceStore.FindApiResourceAsync("all_user_claims");
        var adminClaims = apiResource.UserClaims
            .Where(uc => uc != "creatorRights")
            .Select(c => new System.Security.Claims.Claim(c, "all"))
            .ToArray();
        userClaims.AddRange(adminClaims);
    }

    private void AddDefaultPermissions(UserClaimCollection userClaims, MongoLoginDao user)
    {
        userClaims.Add(new System.Security.Claims.Claim(UserClaim.ReadLogins.ToString(), user.Id));

        if (!IsGuestUser())
        {
            userClaims.Add(new System.Security.Claims.Claim(UserClaim.WriteLogins.ToString(), user.Id));
        }

        bool IsGuestUser() => userClaims.HasClaim("role", "guest");
    }

    private void AddWriteMemberMovementPermissions(UserClaimCollection userClaims)
    {
        if (userClaims == null || userClaims.Any(p => p.Key == "writeMemberMovements:draft" || p.Key == "writeMemberMovements:submit"))
            return;

        var writeOrUpdatePoliciesClaims = userClaims.Where(p => p.Key == "writePolicies" || p.Key == "updatePolicies").ToList();
        foreach (var writeOrUpdatePoliciesClaim in writeOrUpdatePoliciesClaims)
        {
            var claimValues = writeOrUpdatePoliciesClaim.Value.Select(x => x.Value);
            userClaims.AddRange(new KeyValuePair<string, IEnumerable<string>>[]
            {
                new ("writeMemberMovements:draft", claimValues),
                new ("writeMemberMovements:submit", claimValues)
            });
        }
    }
}
