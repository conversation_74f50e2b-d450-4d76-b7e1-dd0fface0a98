﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Gop;

public class GopIdIfMemberPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IGuaranteeOfPaymentService _guaranteeOfPaymentService;

    public GopIdIfMemberPermissionVariableResolver(IGuaranteeOfPaymentService guaranteeOfPaymentService)
    {
        _guaranteeOfPaymentService = guaranteeOfPaymentService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{gopIdIfMember}");
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new GuaranteeOfPaymentWhere {
                MemberId = context.User.EntityId
            }
        };
        return _guaranteeOfPaymentService.GetIds(context.TenantId, query, cancellationToken);
    }
}