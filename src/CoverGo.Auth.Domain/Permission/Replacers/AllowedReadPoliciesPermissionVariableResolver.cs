﻿using System.Collections.Generic;

namespace CoverGo.Auth.Domain.Permission.Replacers;

public class AllowedReadPoliciesPermissionVariableResolver : ReplacerPermissionVariableResolver
{
    const string Variable = "{allowedReadPolicies}";
    const string Dependent = "readPolicies";

    static readonly HashSet<string> IgnoredVariables = new() {
        "{claimIdIf{allowedReadPolicies}}",
        "{transactionIdIf{allowedReadPolicies}}",
        "{memberEntityIds{allowedReadPolicies}}",
    };

    public AllowedReadPoliciesPermissionVariableResolver()
        : base(Variable, Dependent)
    {
    }

    public override bool CanResolve(System.Security.Claims.Claim claim)
    {
        return !IgnoredVariables.Contains(claim.Value) && base.CanResolve(claim);
    }
}