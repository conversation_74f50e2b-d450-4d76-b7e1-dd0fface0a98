﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Replacers;

public abstract class ReplacerPermissionVariableResolver 
    : IPermissionVariableResolver , IHasDependencyOnPermissionType
{
    readonly string _variable;
    readonly string _dependentPermissionType;

    public ReplacerPermissionVariableResolver(
        string variable, string dependentPermissionType)
    {
        _variable = variable;
        _dependentPermissionType = dependentPermissionType;
    }

    public virtual bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains(_variable);
    }

    public string GetDependentPermissionType(System.Security.Claims.Claim _)
    {
        return _dependentPermissionType;
    }

    public Task<IEnumerable<string>> ResolveAsync(PermissionContext context, System.Security.Claims.Claim claim, CancellationToken cancellationToken = default)
    {
        IEnumerable<string> ids = context.ResolvedPermissions[_dependentPermissionType]
            .Select(id => claim.Value.Replace(_variable, id))
            .ToArray();

        return Task.FromResult(ids);
    }
}