﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Auth;

public class LoginsCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readLogins", "writeLogins"
    };
    readonly IAuthService _authService;

    public LoginsCreatorRightsPermissionVariableResolver(
        IAuthService authService)
    {
        _authService = authService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && claim.Value == "{creatorRights}";
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new LoginWhere {
            CreatedById = context.User.Id
        };
        
        return _authService.GetLoginIdsAsync(context.TenantId, @where, cancellationToken);
    }
}