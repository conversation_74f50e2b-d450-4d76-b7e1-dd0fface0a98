﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Auth;

public class PermissionGroupsCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readPermissionGroups", "writePermissionGroups", "updatePermissionGroups", "deletePermissionGroups"
    };
    readonly IAuthService _authService;

    public PermissionGroupsCreatorRightsPermissionVariableResolver(
        IAuthService authService)
    {
        _authService = authService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && claim.Value == "{creatorRights}";
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var permissionGroups = await 
            _authService.GetPermissionGroupsByCreatorIdAsync(
                context.TenantId, 
                context.User.Id, 
                cancellationToken);

        return permissionGroups.Select(pg => pg.Id);
    }
}