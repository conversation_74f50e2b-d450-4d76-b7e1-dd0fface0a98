﻿using CoverGo.Auth.Domain.Extensions;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission;

// Base class to be used for linked/relationship based permission variables. It provides a template method to be overriden by derived classes
// We recognize linked/permission based permissions by checking whether they contain  fromLinkSource or fromLinkTarget variables
//TODO: Rework on this one.
public abstract class LinkedPermissionVariableResolver 
    : IPermissionVariableResolver, IHasDependencyOnPermissionType
{
    private static readonly HashSet<string> LinkVariables = new() { "fromLinkSource", "fromLinkTarget" };

    readonly IEntityService _entityService;
    readonly string _suffix;
    readonly string _prefix;

    protected LinkedPermissionVariableResolver(
        IEntityService entityService,
        string prefix,
        string suffix)
    {
        _entityService = entityService;
        _prefix = prefix;
        _suffix = suffix;
    }

    public virtual bool CanResolve(System.Security.Claims.Claim claim)
    {
        return LinkVariables.Any(claim.Value.Contains) &&
               claim.Value.Contains(_prefix) &&
               claim.Value.Contains(_suffix);
    }

    public string GetDependentPermissionType(System.Security.Claims.Claim claim)
    {
        var splits = SplitValue(claim.Value);

        if (splits.Length > 2)
        {
            if (splits[2].Contains("allowedWriteIndividuals"))
            {
                return "writeIndividuals";
            }
            if (splits[2].Contains("allowedUpdateIndividuals"))
            {
                return "updateIndividuals";
            }
            if (splits[2].Contains("allowedReadIndividuals"))
            {
                return "readIndividuals";
            }
        }

        return default;
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        if (claim.Value.Contains("{entityId}")) {
            var linkedIds = await GetSelfLinkedIdsAsync(context, claim, cancellationToken);

            return await ResolveLinksAsync(context, claim, linkedIds, cancellationToken);
        }

        IEnumerable<string> allowedEntityIds = null;
        var splits = SplitValue(claim.Value);

        if (splits.Length > 2)
        {
            if (splits[2].Contains("allowedWriteIndividuals")) {
                allowedEntityIds = context.ResolvedPermissions["writeIndividuals"]; 
            }
            else if (splits[2].Contains("allowedUpdateIndividuals")) {
                allowedEntityIds = context.ResolvedPermissions["updateIndividuals"];
            }
            else if (splits[2].Contains("allowedReadIndividuals")) {
                allowedEntityIds = context.ResolvedPermissions["readIndividuals"];
            }
        }

        if (allowedEntityIds != null) {
            var linkedIds = await GetAllowedEntityLinkedIdsAsync(
                context,
                allowedEntityIds.ToList(),
                claim,
                cancellationToken);
            return await ResolveLinksAsync(context, claim, linkedIds, cancellationToken);
        }

        return Enumerable.Empty<string>();
    }

    protected abstract Task<IEnumerable<string>> ResolveLinksAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        IEnumerable<string> linkedIds,
        CancellationToken cancellationToken = default);

    private async Task<IEnumerable<string>> GetAllowedEntityLinkedIdsAsync(
        PermissionContext context,
        List<string> allowedEntityIds,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken)
    {
        var splits = SplitValue(claim.Value);
        var isFromSource = splits[0] == "fromLinkSource";
        var type = splits[1];

        var where = isFromSource
            ? new RelationshipWhere { Link = new LinkFilter { SourceId_in = allowedEntityIds, Type = type } }
            : new RelationshipWhere { Link = new LinkFilter { TargetId_in = allowedEntityIds, Type = type } };
        var query = new QueryArguments {
            Where = where
        };
        var relationships = await _entityService.GetRelationshipsAsync(context.TenantId, query, cancellationToken);

        var linkedIds = relationships?
            .Where(r => allowedEntityIds.Contains(r.EntityId))
            .SelectMany(r => r.Links?.Select(l => l.TargetId));

        return linkedIds;
    }

    private async Task<IEnumerable<string>> GetSelfLinkedIdsAsync(
        PermissionContext context, System.Security.Claims.Claim claim, CancellationToken cancellationToken)
    {
        var splits = SplitValue(claim.Value);
        var isFromSource = splits[0] == "fromLinkSource";
        var type = splits[1];

        var where = isFromSource
            ? new RelationshipWhere { Link = new LinkFilter { SourceId_in = new List<string> { context.User.EntityId }, Type = type } }
            : new RelationshipWhere { Link = new LinkFilter { TargetId_in = new List<string> { context.User.EntityId }, Type = type } };
        var query = new QueryArguments {
            Where = where
        };
        var relationships = await _entityService.GetRelationshipsAsync(context.TenantId, query, cancellationToken);
        var linkedIds = relationships?
            .Where(r => r.EntityId == context.User.EntityId)
            .SelectMany(r => r.Links?.Select(l => l?.TargetId) ??
                             Enumerable.Empty<string>()) 
                        ?? Enumerable.Empty<string>();

        return linkedIds.ToArray();
    }

    private string[] SplitValue(string value)
    {
        return value
            .DropPrefixAndSuffix(_prefix , _suffix)
            .DropPrefixAndSuffix("{", "}")
            .Split(":");
    }
}