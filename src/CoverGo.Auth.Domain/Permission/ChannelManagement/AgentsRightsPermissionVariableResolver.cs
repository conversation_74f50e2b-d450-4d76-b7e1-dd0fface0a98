﻿using CoverGo.Auth.Domain.OtherServices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.ChannelManagement
{
    public class AgentsRightsPermissionVariableResolver : IPermissionVariableResolver
    {
        private static readonly HashSet<string> PermissionTypes = new() { "writeAgents" };

        private readonly ICoverGoChannelManagementService _channelManagementService;

        public AgentsRightsPermissionVariableResolver(ICoverGoChannelManagementService channelManagementService)
        {
            _channelManagementService = channelManagementService;
        }

        public bool CanResolve(System.Security.Claims.Claim claim)
        {
            return PermissionTypes.Contains(claim.Type) && claim.Value.Contains("{creatorRights}");
        }

        public Task<IEnumerable<string>> ResolveAsync(PermissionContext context, System.Security.Claims.Claim claim, CancellationToken cancellationToken = default)
        {
            return _channelManagementService.GetIdsAsync(context.User.Id, cancellationToken);
        }
    }
}
