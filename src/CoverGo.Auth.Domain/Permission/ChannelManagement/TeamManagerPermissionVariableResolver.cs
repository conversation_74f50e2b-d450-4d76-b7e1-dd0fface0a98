﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.ChannelManagement;

public class TeamManagerPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() { "writeCases", "writePolicies", "readCases", "readPolicies" };

    private readonly ICoverGoChannelManagementService _channelManagementService;

    public TeamManagerPermissionVariableResolver(ICoverGoChannelManagementService channelManagementService)
    {
        _channelManagementService = channelManagementService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && claim.Value.Contains("{teamManagerCreatorRights}");
    }

    public Task<IEnumerable<string>> ResolveAsync(PermissionContext context, System.Security.Claims.Claim claim, CancellationToken cancellationToken = default)
    {
        return _channelManagementService.GetTeamMembersLoginIdsAsync(context.User.Id, cancellationToken);
    }
}
