﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Transaction;

public class TransactionPolicyIdHolderPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IPolicyService _policyService;
    private readonly ITransactionService _transactionService;

    public TransactionPolicyIdHolderPermissionVariableResolver(IPolicyService policyService, ITransactionService transactionService)
    {
        _policyService = policyService;
        _transactionService = transactionService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{transactionIdForPolicyIdIfHolder}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new TransactionWhere {
                PolicyId_in = await GetPolicyIdsAsync(context, cancellationToken)
            }
        };
        return await _transactionService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }

    private Task<IEnumerable<string>> GetPolicyIdsAsync(
        PermissionContext context,
        CancellationToken cancellationToken)
    {
        var where = new PolicyWhere {
            ContractHolder = new EntityWhere {
                Id = context.User.EntityId
            }
        };
            
        return _policyService.GetIdsAsync(context.TenantId, @where, cancellationToken);
    }
}