﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Transaction;

public class TransactionIfClaimantPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IClaimsService _claimsService;
    private readonly ITransactionService _transactionService;

    public TransactionIfClaimantPermissionVariableResolver(
        IClaimsService claimsService,
        ITransactionService transactionService)
    {
        _claimsService = claimsService;
        _transactionService = transactionService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{transactionIdForClaimIdIfClaimant}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new TransactionWhere {
                ClaimId_in = (await GetClaimIdsAsync(context, cancellationToken)).ToList()
            }
        };
        return await _transactionService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }

    private Task<IEnumerable<string>> GetClaimIdsAsync(
        PermissionContext context,
        CancellationToken cancellationToken)
    {
        var query = new QueryArguments {
            Where = new ClaimWhere {
                ClaimantId = context.User.EntityId
            }
        };

        return _claimsService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}