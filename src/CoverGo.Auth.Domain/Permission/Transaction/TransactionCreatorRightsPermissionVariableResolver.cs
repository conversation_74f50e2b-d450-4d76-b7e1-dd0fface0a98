﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Transaction;

public class TransactionCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readTransactions", "writeTransactions", "updateTransactions", "deleteTransactions"
    };
    private readonly ITransactionService _transactionService;

    public TransactionCreatorRightsPermissionVariableResolver(
        ITransactionService transactionService)
    {
        _transactionService = transactionService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && 
               claim.Value == "{creatorRights}";
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new TransactionWhere {
                CreatedById = context.User.Id
            }
        };
        return _transactionService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}