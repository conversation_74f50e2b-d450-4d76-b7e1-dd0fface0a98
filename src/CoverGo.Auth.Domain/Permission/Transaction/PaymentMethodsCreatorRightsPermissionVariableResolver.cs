﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Transaction;

public class PaymentMethodsCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readPaymentMethods", "writePaymentMethods"
    };
    private readonly ITransactionService _transactionService;

    public PaymentMethodsCreatorRightsPermissionVariableResolver(
        ITransactionService transactionService)
    {
        _transactionService = transactionService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && 
               claim.Value == "{creatorRights}";
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new PaymentMethodWhere {
            CreatedById = context.User.Id
        };
        var paymentMethods = await _transactionService.GetPaymentMethodsAsync(context.TenantId, where, cancellationToken);

        return paymentMethods.Select(p => p.Id);
    }
}