﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Transaction;

public class TransactionRelationshipPermissionVariableResolver : LinkedPermissionVariableResolver
{
    private const string Prefix = "{transactionIdForClaimIdIf";
    private const string Suffix = "IsClaimant}";

    private readonly ITransactionService _transactionService;
    private readonly IClaimsService _claimsService;

    public TransactionRelationshipPermissionVariableResolver(
        ITransactionService transactionService,
        IClaimsService claimsService,
        IEntityService entityService)
        : base(entityService, Prefix, Suffix)
    {
        _transactionService = transactionService;
        _claimsService = claimsService;
    }
    protected override async Task<IEnumerable<string>> ResolveLinksAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        IEnumerable<string> linkedIds,
        CancellationToken cancellationToken = default)
    {
        var claimIds = await GetClaimIdsAsync(context, linkedIds, cancellationToken);
        var query = new QueryArguments {
            Where = new TransactionWhere {
                ClaimId_in = claimIds.ToList()
            }
        };
        return await _transactionService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }

    private Task<IEnumerable<string>> GetClaimIdsAsync(
        PermissionContext context,
        IEnumerable<string> allowedClaimantIds,
        CancellationToken cancellationToken)
    {
        var query = new QueryArguments {
            Where = new ClaimWhere {
                ClaimantId_in = allowedClaimantIds
            }
        };

        return _claimsService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}