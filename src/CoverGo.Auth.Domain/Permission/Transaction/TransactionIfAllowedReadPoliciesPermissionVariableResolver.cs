﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Transaction;

public class TransactionIfAllowedReadPoliciesPermissionVariableResolver :
    IPermissionVariableResolver, IHasDependencyOnPermissionType
{
    const string DependentPermissionType = "readPolicies";

    private readonly ITransactionService _transactionService;

    public TransactionIfAllowedReadPoliciesPermissionVariableResolver(
        ITransactionService transactionService)
    {
        _transactionService = transactionService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value == "{transactionIdIf{allowedReadPolicies}}";
    }

    public string GetDependentPermissionType(System.Security.Claims.Claim _)
    {
        return DependentPermissionType;
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var policyIds = context.ResolvedPermissions[DependentPermissionType];

        if (!policyIds.Any())
        {
            return Enumerable.Empty<string>();
        }

        var query = new QueryArguments
        {
            Where = new TransactionWhere
            {
                PolicyId_in = policyIds
            }
        };

        return await _transactionService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}