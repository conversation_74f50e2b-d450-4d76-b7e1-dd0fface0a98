﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Proposal;

public class ProposalsCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readProposals", "writeProposals"
    };
    private readonly ICaseService _caseService;

    public ProposalsCreatorRightsPermissionVariableResolver(
        ICaseService caseService)
    {
        _caseService = caseService;
    }
    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && claim.Value == "{creatorRights}";
    }
    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new QueryArguments {
            Where = new CaseWhere
            {
                Proposals_contains = new ProposalWhere { CreatedById = context.User.Id }
            }
        };

        return  _caseService.GetProposalIdsAsync(context.TenantId, where, cancellationToken);
    }
}