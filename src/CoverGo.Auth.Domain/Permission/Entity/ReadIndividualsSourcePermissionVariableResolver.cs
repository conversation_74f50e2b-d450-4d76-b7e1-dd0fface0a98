﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Domain.Utils;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Entity;

public class ReadIndividualsSourcePermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readIndividuals"
    };

    private readonly IEntityService _entityService;

    public ReadIndividualsSourcePermissionVariableResolver(IEntityService entityService)
    {
        _entityService = entityService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) &&
               claim.Value.Contains("individualIdIfSourceContains:");
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new IndividualWhere {
                Source_contains = StringUtils.ExtractSourceId(claim.Value)
            }
        };
        return _entityService.GetIndividualIdsAsync(context.TenantId, query, cancellationToken);
    }
}