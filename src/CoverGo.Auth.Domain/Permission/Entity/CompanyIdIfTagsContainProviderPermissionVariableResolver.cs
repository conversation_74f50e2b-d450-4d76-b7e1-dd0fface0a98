﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Entity;

public class CompanyIdIfTagsContainProviderPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IEntityService _entityService;

    public CompanyIdIfTagsContainProviderPermissionVariableResolver(
        IEntityService entityService)
    {
        _entityService = entityService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{companyIdIfTagsContainProvider}");
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new EntityWhere { Tags_contains = "provider" };

        return _entityService.GenericQueryIdsAsync(context.TenantId, where, cancellationToken);
    }
}