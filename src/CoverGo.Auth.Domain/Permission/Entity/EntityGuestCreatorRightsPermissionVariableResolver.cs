﻿using CoverGo.Auth.Domain.OtherServices;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Entity
{
    public class EntityGuestCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
    {
        private static readonly HashSet<string> PermissionTypes = new() {
        "readIndividuals",
        "writeIndividuals",
        "updateIndividuals",
        "deleteIndividuals",
        "readCompanies",
        "writeCompanies",
        "updateCompanies",
        "deleteCompanies",
        "readInternals",
        "writeInternals",
        "readOrganizations",
        "writeOrganizations",
        "updateOrganizations",
        "deleteOrganizations",
        "readObjects",
        "writeObjects"
    };

        private readonly IEntityService _entityService;

        public EntityGuestCreatorRightsPermissionVariableResolver(
            IEntityService entityService)
        {
            _entityService = entityService;
        }
        public bool CanResolve(System.Security.Claims.Claim claim)
        {
            return (PermissionTypes.Contains(claim.Type) && claim.Value.Contains("guestCreatorRights"));
        }
        public Task<IEnumerable<string>> ResolveAsync(PermissionContext context, System.Security.Claims.Claim claim, CancellationToken cancellationToken = default)
        {
            EntityWhere where = GetEntityWhere(context.User.Id,GetPermissionsGraceDurationInMinutes(claim));

            return _entityService.GenericQueryIdsAsync(context.TenantId, where, cancellationToken);
        }

        int GetPermissionsGraceDurationInMinutes(System.Security.Claims.Claim claim)
        {
            var guestCreator = claim.Value.Contains("guestCreatorRights:") ? claim.Value : string.Empty;

            return !string.IsNullOrEmpty(guestCreator) ? JObject.Parse(guestCreator).Value<int>("guestCreatorRights") : 0;
        }

        EntityWhere GetEntityWhere(string loginId, int duration, bool unitInSeconds = false)
        {
            return
                duration > 0
                    ? new EntityWhere
                    {
                        And = new()
                        {
                                new() { CreatedById = loginId },
                                new()
                                {
                                    CreatedAt_gt = (unitInSeconds
                                        ? DateTime.Now.AddSeconds(-duration)
                                        : DateTime.Now.AddMinutes(-duration))
                                }
                        }
                    }
                    : new EntityWhere { CreatedById = loginId };
        }
    }
}
