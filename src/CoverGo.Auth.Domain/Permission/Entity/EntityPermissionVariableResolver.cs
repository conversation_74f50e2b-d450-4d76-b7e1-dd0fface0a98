﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Entity;

public class EntityPermissionVariableResolver : IPermissionVariableResolver
{
    public EntityPermissionVariableResolver()
    {
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{entityId}") && 
               NotALink(claim);
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        return Task.FromResult<IEnumerable<string>>(new[] { context.User.EntityId });
    }

    private static bool NotALink(System.Security.Claims.Claim claim)
    {
        return !claim.Value.Contains("fromLinkSource") &&
               !claim.Value.Contains("fromLinkTarget");
    }
}