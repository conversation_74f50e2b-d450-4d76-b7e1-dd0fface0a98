﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Entity;

public class EntityCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readIndividuals",
        "writeIndividuals",
        "updateIndividuals",
        "deleteIndividuals",
        "readCompanies",
        "writeCompanies",
        "updateCompanies",
        "deleteCompanies",
        "readInternals",
        "writeInternals",
        "readOrganizations",
        "writeOrganizations",
        "updateOrganizations",
        "deleteOrganizations",
        "readObjects",
        "writeObjects"
    };

    private readonly IEntityService _entityService;

    public EntityCreatorRightsPermissionVariableResolver(
        IEntityService entityService)
    {
        _entityService = entityService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value == "{creatorRights}" && PermissionTypes.Contains(claim.Type);
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var where = new EntityWhere { CreatedById = context.User.Id };

        return _entityService.GenericQueryIdsAsync(context.TenantId, where, cancellationToken);
    }
}