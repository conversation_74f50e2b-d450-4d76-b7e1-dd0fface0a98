﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Domain.Utils;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Entity;

public class ReadLoginsAssociatedIndividualSourcePermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readLogins"
    };

    private readonly IEntityService _entityService;
    private readonly IAuthService _authService;

    public ReadLoginsAssociatedIndividualSourcePermissionVariableResolver(
        IEntityService entityService, IAuthService authService)
    {
        _entityService = entityService;
        _authService = authService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) &&
               claim.Value.Contains("loginIdIfAssociatedIndividualSourceContains:");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new IndividualWhere {
                Source_contains = StringUtils.ExtractSourceId(claim.Value)
            }
        };
        var individualIds = await _entityService.GetIndividualIdsAsync(
            context.TenantId, query, cancellationToken);
        var where = new LoginWhere {
            EntityIds = individualIds
        };

        return await _authService.GetLoginIdsAsync(context.TenantId, where, null, null, null,
            cancellationToken: cancellationToken);
    }
}