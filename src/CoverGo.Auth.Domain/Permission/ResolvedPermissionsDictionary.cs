﻿using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Immutable;

namespace CoverGo.Auth.Domain.Permission;

public class ResolvedPermissionsDictionary
{
    readonly ConcurrentDictionary<string, ImmutableList<string>> _resolvedPermissions = new();

    public bool ContainsPermissionType(string permissionType)
    {
        return _resolvedPermissions.ContainsKey(permissionType);
    }

    public void Add(string permissionType, IEnumerable<string> ids)
    {
        var idList = ids.ToImmutableList();
        if(!_resolvedPermissions.TryAdd(permissionType, idList))
        {
            _resolvedPermissions[permissionType] =
                _resolvedPermissions[permissionType].AddRange(idList);
        }
    }

    public IReadOnlyList<string> this [string permissionType] => 
        _resolvedPermissions.TryGetValue(permissionType, out var ids) ? 
            ids : new List<string>();
}