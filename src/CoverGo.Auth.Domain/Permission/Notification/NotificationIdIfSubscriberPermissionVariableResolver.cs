﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Notification;

public class NotificationIdIfSubscriberPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly INotificationService _notificationService;

    public NotificationIdIfSubscriberPermissionVariableResolver(
        INotificationService notificationService)
    {
        _notificationService = notificationService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{notificationIdIfSubscriber}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var subscriptionQuery = new QueryArguments {
            Where = new NotificationSubscriptionWhere {
                EntitiesIds_contains = context.User.EntityId
            }
        };
        var subscriptions = (await _notificationService.GetNotificationSubscriptionsAsync(
            context.TenantId, subscriptionQuery, cancellationToken)).ToArray();
        var topicNames = subscriptions.Where(s => s.TopicName != null).Select(s => s.TopicName).ToList();
        var topicIds = subscriptions.Select(s => s.Id).ToList();
        var notificationQuery = new QueryArguments {
            Where = new NotificationWhere {
                Or = new List<NotificationWhere>
                {
                    new() { ToTopicId_in = topicIds },
                    new() { ToTopic_in = topicNames }
                }
            }
        };
        var notifications = await _notificationService.GetNotificationsAsync(
            context.TenantId, notificationQuery, cancellationToken);

        return notifications.Select(n => n.Id);
    }
}