﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Notification;

public class NotificationSubscriptionsCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readNotificationSubscriptions", "writeNotificationSubscriptions"
    };
    
    private readonly INotificationService _notificationService;

    public NotificationSubscriptionsCreatorRightsPermissionVariableResolver(
        INotificationService notificationService)
    {
        _notificationService = notificationService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && claim.Value == "{creatorRights}";
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new NotificationSubscriptionWhere {
                CreatedById = context.User.Id
            }
        };
        return _notificationService.GetNotificationSubscriptionIdsAsync(
            context.TenantId, query, cancellationToken);
    }
}