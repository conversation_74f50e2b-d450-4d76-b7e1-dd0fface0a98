﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Notification;

public class NotificationSubscriptionIdIfSubscriberPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly INotificationService _notificationService;

    public NotificationSubscriptionIdIfSubscriberPermissionVariableResolver(INotificationService notificationService)
    {
        _notificationService = notificationService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{notificationSubscriptionIdIfSubscriber}");
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new NotificationSubscriptionWhere {
                EntitiesIds_contains = context.User.EntityId
            }
        };

        return _notificationService.GetNotificationSubscriptionIdsAsync(context.TenantId, query, cancellationToken);
    }
}