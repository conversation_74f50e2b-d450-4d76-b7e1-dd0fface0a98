﻿using System.Collections.Generic;

namespace CoverGo.Auth.Domain.Permission;

public class PermissionContext
{
    public static readonly PermissionContext Empty = new(
        "",
        new MongoLoginDao(),
        new UserClaimCollection(new Dictionary<string, List<System.Security.Claims.Claim>>()));

    public PermissionContext(
        string tenantId,
        MongoLoginDao user,
        UserClaimCollection userClaims)
    {
        TenantId = tenantId;
        User = user;
        UserClaims = userClaims;
    }
    
    public string TenantId { get; }
    public MongoLoginDao User { get; }
    public UserClaimCollection UserClaims { get; }
    public ResolvedPermissionsDictionary ResolvedPermissions { get; } = new();
    public IReadOnlyCollection<string> PermissionNames => UserClaims.PermissionNames;
}