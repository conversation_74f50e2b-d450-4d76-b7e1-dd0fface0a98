﻿using CoverGo.Auth.Domain.Abstraction;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Queries;

public class GetMultipleLoginPermissionsQueryHandler
    : IQueryHandler<GetMultipleLoginPermissionsQuery, LoginPermission[]>
{
    readonly IQueryHandler<GetAllPermissionTargetIdsQuery, PermissionTargetIds>
        _getAllPermissionTargetIdsQueryHandler;

    public GetMultipleLoginPermissionsQueryHandler(
        IQueryHandler<GetAllPermissionTargetIdsQuery, PermissionTargetIds> getAllPermissionTargetIdsQueryHandler)
    {
        _getAllPermissionTargetIdsQueryHandler = getAllPermissionTargetIdsQueryHandler;
    }

    public async Task<LoginPermission[]> Handle(GetMultipleLoginPermissionsQuery query, CancellationToken ct)
    {
        var (tenantId, loginIds) = query;
        var parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = 10,
            CancellationToken = ct
        };
        var result = new ConcurrentBag<LoginPermission>();

        await Parallel.ForEachAsync(loginIds, parallelOptions,
            async (id, token) =>
            {
                var query = new GetAllPermissionTargetIdsQuery(tenantId, id);
                var permission = await _getAllPermissionTargetIdsQueryHandler.Handle(query, token);

                result.Add(new LoginPermission(id, permission.Ids));
            });

        return result.ToArray();
    }
}