﻿using CoverGo.Auth.Domain.Abstraction;
using CoverGo.Auth.Domain.Extensions;
using CoverGo.DomainUtils;
using MoreLinq.Extensions;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Queries;

public class LoginClaimsQueryHandler : IQueryHandler<GetLoginClaimsQuery, UserClaimCollection>
{
    private readonly IAuthService _authService;

    public LoginClaimsQueryHandler(IAuthService authService)
    {
        _authService = authService;
    }

    public async Task<UserClaimCollection> Handle(GetLoginClaimsQuery query, CancellationToken ct)
    {
        (var tenantId, var user) = query;

        var userClaimsTask = GetUserClaimsAsync(tenantId, user, ct);
        var inheritedClaimsAsync = GetInheritedClaimsAsync(tenantId, user, ct);
        await Task.WhenAll(userClaimsTask, inheritedClaimsAsync);
        var userClaims = userClaimsTask.Result;
        userClaims.AddRange(inheritedClaimsAsync.Result);

        return userClaims;
    }

    private async Task<UserClaimCollection> GetInheritedClaimsAsync(
        string tenantId,
        MongoLoginDao user,
        CancellationToken cancellationToken)
    {
        var loginIdStack = new Stack<IEnumerable<string>>();
        loginIdStack.Push(user.GetLoginIds());
        var userClaims = new UserClaimCollection();

        while (loginIdStack.Count > 0)
        {
            var loginIds = loginIdStack.Pop();

            if (loginIds.Any())
            {
                var inheritedUsers = await TaskWhenAllWithThrottling.ParallelForEachAsync(loginIds, 10,
                    async (id) => await FindLoginAsync(tenantId, id, cancellationToken));

                var inheritedUserClaimCollections = await TaskWhenAllWithThrottling.ParallelForEachAsync(inheritedUsers, 10
                    , async (u) => await GetUserClaimsAsync(tenantId, u, cancellationToken));
                inheritedUserClaimCollections.ForEach(c => userClaims.AddRange(c));
                loginIdStack.Push(inheritedUsers.SelectMany(u => u.GetLoginIds()));
            }
        }

        return userClaims;
    }

    private Task<MongoLoginDao> FindLoginAsync(
        string tenantId,
        string loginId,
        CancellationToken cancellationToken = default)
    {
        return _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
    }

    private async Task<UserClaimCollection> GetUserClaimsAsync(
        string tenantId, MongoLoginDao user, CancellationToken cancellationToken)
    {
        if (user == null)
        {
            return UserClaimCollection.Empty;
        }
        var userClaims = new UserClaimCollection(user.Claims.Select(c => c.ToSecurityClaim()));
        var userPermissionGroups = await GetUserPermissionGroups(
            tenantId,
            user,
            cancellationToken);
        var targetedPermissions = await GetTargetedPermissionsFromGroupsAsync(
            tenantId, userPermissionGroups, cancellationToken);

        userClaims.AddRange(targetedPermissions);

        return userClaims;
    }

    private async Task<UserClaimCollection> GetTargetedPermissionsFromGroupsAsync(
        string tenantId, IEnumerable<PermissionGroup> permissionGroups, CancellationToken cancellationToken)
    {
        var targetedPermissions = new UserClaimCollection();
        var permissionGroupStack = new Stack<PermissionGroup>(permissionGroups);

        while (permissionGroupStack.Count > 0)
        {
            var pg = permissionGroupStack.Pop();
            targetedPermissions.AddRange(pg.TargettedPermissions ?? Enumerable.Empty<KeyValuePair<string, IEnumerable<string>>>());

            if (pg.PermissionGroupIds?.Any() ?? false)
            {
                var childPgs = (await _authService.GetPermissionGroupsAsync(
                    tenantId, pg.PermissionGroupIds, cancellationToken)).ToArray();
                permissionGroupStack.PushRange(childPgs);
            }
        }

        return targetedPermissions;
    }

    private async Task<IEnumerable<PermissionGroup>> GetUserPermissionGroups(
        string tenantId,
        MongoLoginDao user,
        CancellationToken cancellationToken)
    {
        var permissionGroupIds = user.GetGroupIds();

        return permissionGroupIds.Any()
            ? await _authService.GetPermissionGroupsAsync(tenantId, permissionGroupIds, cancellationToken)
            : Enumerable.Empty<PermissionGroup>();
    }
}