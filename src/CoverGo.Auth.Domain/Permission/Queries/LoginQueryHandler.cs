﻿using CoverGo.Auth.Domain.Abstraction;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Queries;

public class LoginQueryHandler : IQueryHandler<GetLoginQuery, MongoLoginDao>
{
    private readonly IAuthService _authService;

    public LoginQueryHandler(IAuthService authService)
    {
        _authService = authService;
    }

    public Task<MongoLoginDao> Handle(GetLoginQuery query, CancellationToken ct)
    {
        (var tenantId, var loginId) = query;

        return _authService.FindLoginByIdAsync(tenantId, loginId, ct);
    }
}