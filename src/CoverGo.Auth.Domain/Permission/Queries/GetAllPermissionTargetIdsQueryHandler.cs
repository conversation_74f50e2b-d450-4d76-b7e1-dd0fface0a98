﻿using CoverGo.Auth.Domain.Abstraction;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Queries;

public class GetAllPermissionTargetIdsQueryHandler 
    : IQueryHandler<GetAllPermissionTargetIdsQuery, PermissionTargetIds>
{
    readonly PermissionContextBuilder _contextBuilder;
    readonly ConcurrentPermissionVariableResolver _concurrentPermissionVariableResolver;

    public GetAllPermissionTargetIdsQueryHandler(
        PermissionContextBuilder contextBuilder,
        ConcurrentPermissionVariableResolver concurrentPermissionVariableResolver)
    {
        _contextBuilder = contextBuilder;
        _concurrentPermissionVariableResolver = concurrentPermissionVariableResolver;
    }

    public async Task<PermissionTargetIds> Handle(
        GetAllPermissionTargetIdsQuery query, CancellationToken ct)
    {
        var (tenantId, loginId) = query;
        var context = await _contextBuilder.BuildAsync(tenantId, loginId, ct);
        var permissionNames = context.PermissionNames;

        return await _concurrentPermissionVariableResolver.GetTargetIdsAsync(
            context, permissionNames, ct);
    }
}