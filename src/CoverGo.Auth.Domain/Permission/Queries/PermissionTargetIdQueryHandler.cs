﻿using CoverGo.Auth.Domain.Abstraction;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Queries;

public class PermissionTargetIdQueryHandler : IQueryHandler<GetPermissionTargetIdsQuery, PermissionTargetIds>
{
    readonly PermissionContextBuilder _contextBuilder;
    readonly ConcurrentPermissionVariableResolver _concurrentPermissionVariableResolver;

    public PermissionTargetIdQueryHandler(
        PermissionContextBuilder contextBuilder,
        ConcurrentPermissionVariableResolver concurrentPermissionVariableResolver)
    {
        _contextBuilder = contextBuilder;
        _concurrentPermissionVariableResolver = concurrentPermissionVariableResolver;
    }

    public async Task<PermissionTargetIds> Handle(GetPermissionTargetIdsQuery query, CancellationToken ct)
    {
        var (tenantId, loginId,  permissionNames) = query;
        var context = await _contextBuilder.BuildAsync(tenantId, loginId, ct);

        return await _concurrentPermissionVariableResolver.GetTargetIdsAsync(
            context, permissionNames, ct);
    }
}