﻿using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Queries;

public class ConcurrentPermissionVariableResolver
{
    readonly IPermissionVariableResolver _resolver;

    public ConcurrentPermissionVariableResolver(IPermissionVariableResolver resolver)
    {
        _resolver = resolver;
    }

    public async Task<PermissionTargetIds> GetTargetIdsAsync(
        PermissionContext context,
        IEnumerable<string> permissionNames,
        CancellationToken cancellationToken = default)
    {
        var result = new ConcurrentDictionary<string, IEnumerable<string>>();
        var parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = 10,
            CancellationToken = cancellationToken
        };
        await Parallel.ForEachAsync(permissionNames, parallelOptions,
            async (name, token) =>
            {
                if (context.UserClaims.TryGetValue(name, out var claims))
                {
                    var ids = await ResolveAsync(context, claims, token);
                    result.TryAdd(name, ids);
                }
            });

        return new PermissionTargetIds(result);
    }

    private async Task<string[]> ResolveAsync(
        PermissionContext context,
        IEnumerable<System.Security.Claims.Claim> claims,
        CancellationToken cancellationToken)
    {
        var idBag = new ConcurrentBag<string>();
        var parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = 10,
            CancellationToken = cancellationToken
        };
        await Parallel.ForEachAsync(claims, parallelOptions,
            async (c, token) =>
            {
                var ids = await _resolver.ResolveAsync(context, c, token);
                foreach (var id in ids)
                {
                    if (!string.IsNullOrEmpty(id))
                    {
                        idBag.Add(id);
                    }
                }
                idBag.Add(c.Value);
            });

        return idBag.Distinct().ToArray();
    }
}