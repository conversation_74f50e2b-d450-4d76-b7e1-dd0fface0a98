﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission;

public class CompositePermissionVariableResolver : IPermissionVariableResolver
{
    static readonly DefaultPermissionVariableResolver DefaultResolver = new();
    private readonly IEnumerable<IPermissionVariableResolver> _resolvers;
    private readonly ILogger<CompositePermissionVariableResolver> _logger;
    readonly int _perfWarningThresholdInMilliseconds;

    public CompositePermissionVariableResolver(
        IEnumerable<IPermissionVariableResolver> resolvers,
        ILogger<CompositePermissionVariableResolver> logger,
        int perfWarningThresholdInMilliseconds)
    {
        _resolvers = resolvers;
        _logger = logger;
        _perfWarningThresholdInMilliseconds = perfWarningThresholdInMilliseconds;
    }

    public Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var visitedClaims = new HashSet<string>();

        return ResolveAsync(context, claim, visitedClaims, cancellationToken);
    }

    public bool CanResolve(System.Security.Claims.Claim claim) => true;

    private async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        HashSet<string> visitedClaims,
        CancellationToken cancellationToken = default)
    {
        var resolver = FindResolver(claim);
        var resolverType = resolver.GetType();
        visitedClaims.Add(claim.ToString());

        if (resolver is IHasDependencyOnPermissionType hasDependencyOnPermissionType)
        {
            var dependentPermissionType = hasDependencyOnPermissionType
                .GetDependentPermissionType(claim);
            Debug("{claim} has dependency on {dependentPermissionType}",
                claim, dependentPermissionType);
            await ResolveDependentAsync(
                context, dependentPermissionType, visitedClaims, cancellationToken);
        }
        var watch = Stopwatch.StartNew();
        var resolvedIds = await resolver.ResolveAsync(context, claim, cancellationToken);
        watch.Stop();
        LogPermissionTime(claim, resolverType, watch.ElapsedMilliseconds);

        return resolvedIds.Where(id => !string.IsNullOrEmpty(id)).ToArray();
    }

    private async Task ResolveDependentAsync(
        PermissionContext context,
        string permissionType,
        HashSet<string> visitedClaims,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(permissionType) ||
            context.ResolvedPermissions.ContainsPermissionType(permissionType) ||
            !context.UserClaims.TryGetValue(permissionType, out var claims))
        {
            return;
        }

        var filteredClaims = claims
            .Where(c => !visitedClaims.Contains(c.ToString()))
            .ToList();

        var ids = new List<string>();
        foreach (var claim in filteredClaims)
        {
            var resolvedIds = await ResolveAsync(context, claim, visitedClaims, cancellationToken);
            ids.AddRange(resolvedIds);
        }

        context.ResolvedPermissions.Add(permissionType, ids);
    }

    private IPermissionVariableResolver FindResolver(System.Security.Claims.Claim claim)
    {
        var filteredResolvers = _resolvers
            .Where(r => r.CanResolve(claim))
            .ToArray();

        if (filteredResolvers.Length > 1)
        {
            throw new InvalidOperationException($"Found more than 1 resolvers to resolve {claim}");
        }

        return filteredResolvers.FirstOrDefault(DefaultResolver);
    }

    private void Debug(string message, params object[] args)
    {
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug(message, args);
        }
    }

    private void LogPermissionTime(
        System.Security.Claims.Claim claim,
        Type resolverType,
        long timeInMilliseconds)
    {
        Debug("{type} is used to resolve {claim}", resolverType, claim);

        if (timeInMilliseconds > _perfWarningThresholdInMilliseconds)
        {
            _logger.LogWarning("Slow permission resolution detected: Resolver {resolver} took {ms} ms to resolve {claim}",
                resolverType,
                timeInMilliseconds,
                claim);
        }
        else
        {
            Debug("Resolver {resolver} took {ms} ms to resolve {claim}",
                resolverType,
                timeInMilliseconds,
                claim);
        }
    }

    private sealed class DefaultPermissionVariableResolver : IPermissionVariableResolver
    {
        public Task<IEnumerable<string>> ResolveAsync(
            PermissionContext context,
            System.Security.Claims.Claim claim,
            CancellationToken cancellationToken = default)
        {
            Trace.WriteLine($"Default resolver to resolve {claim.Type} and {claim.Value}");

            return Task.FromResult<IEnumerable<string>>(new[] { claim.Value });
        }

        public bool CanResolve(System.Security.Claims.Claim claim)
        {
            return true;
        }
    }
}