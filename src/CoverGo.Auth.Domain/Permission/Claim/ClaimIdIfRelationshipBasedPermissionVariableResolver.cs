﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Claim;

public class ClaimIdIfRelationshipBasedPermissionVariableResolver : LinkedPermissionVariableResolver
{
    private const string Prefix = "{claimIdIf";
    private const string Suffix = "IsClaimant}";

    private readonly IClaimsService _claimsService;

    public ClaimIdIfRelationshipBasedPermissionVariableResolver(
        IEntityService entityService,
        IClaimsService claimsService)
        : base(entityService, Prefix, Suffix)
    {
        _claimsService = claimsService;
    }

    protected override Task<IEnumerable<string>> ResolveLinksAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        IEnumerable<string> linkedIds,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new ClaimWhere {
                ClaimantId_in = linkedIds
            }
        };
        return _claimsService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}