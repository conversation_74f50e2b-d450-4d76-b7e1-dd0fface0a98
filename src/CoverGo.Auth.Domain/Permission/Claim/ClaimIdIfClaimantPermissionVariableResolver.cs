﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Claim;

public class ClaimIdIfClaimantPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IClaimsService _claimsService;

    public ClaimIdIfClaimantPermissionVariableResolver(IClaimsService claimsService)
    {
        _claimsService = claimsService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{claimIdIfClaimant}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new ClaimWhere {
                ClaimantId = context.User.EntityId
            }
        };
        return await _claimsService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
}