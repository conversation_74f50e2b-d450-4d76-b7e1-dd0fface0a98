﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Claim;

public class ClaimIdIfProviderPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IClaimsService _claimsService;
    private readonly IEntityService _entityService;

    public ClaimIdIfProviderPermissionVariableResolver(
        IClaimsService claimsService, IEntityService entityService)
    {
        _claimsService = claimsService;
        _entityService = entityService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{claimIdIfProvider}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var providerRelationships = await GetRelationshipsAsync(context, cancellationToken);
        var query = new QueryArguments {
            Where = new ClaimWhere {
                ProviderId_in = providerRelationships
                    .Where(r => r.EntityId == context.User.EntityId)
                    .SelectMany(r => r.Links.Where(l => l.TargetEntityType == "Company")
                        .Select(l => l.TargetId))
                    .Distinct()
            }
        };
        return await _claimsService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }
    
    private Task<IEnumerable<Relationships>> GetRelationshipsAsync(PermissionContext context, CancellationToken cancellationToken)
    {
        var query = new QueryArguments {
            Where = new RelationshipWhere {
                Link = new LinkFilter {
                    SourceId_in = new[] {
                        context.User.EntityId
                    }, TargetEntityTypes_in = new[] { "Company" }
                }
            }
        };

        return _entityService.GetRelationshipsAsync(context.TenantId, query, cancellationToken);
    }
}