﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Domain.Utils;
using CoverGo.Claims3.Client;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Claim;

public class ClaimCreatorRightsPermissionVariableResolver : IPermissionVariableResolver
{
    private static readonly HashSet<string> PermissionTypes = new() {
        "readClaims", "writeClaims", "updateClaims", "deleteClaims"
    };
    private readonly IClaimsService _claimsService;
    private readonly IClaims3Service _claims3Service;
    private readonly IMultiTenantFeatureManager _multiTenantFeatureManager;

    public ClaimCreatorRightsPermissionVariableResolver(IClaimsService claimsService, IClaims3Service claims3Service, IMultiTenantFeatureManager multiTenantFeatureManager)
    {
        _claimsService = claimsService;
        _claims3Service = claims3Service;
        _multiTenantFeatureManager = multiTenantFeatureManager;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return PermissionTypes.Contains(claim.Type) && claim.Value == "{creatorRights}";
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var query = new QueryArguments {
            Where = new ClaimWhere {
                CreatedById = context.User.Id
            }
        };
        IEnumerable<string> claimIds = await _claimsService.GetIdsAsync(context.TenantId, query, cancellationToken);

        if (!await _multiTenantFeatureManager.IsEnabled(FeatureNames.Claims3Permission, context.TenantId))
            return claimIds;

        IEnumerable<string> claim3Ids = await _claims3Service.GetIdsAsync(
            new ClaimFilterInput { CreatedById = new c3StringOperationFilterInput { Eq = context.User.Id } },
            cancellationToken);

        return claimIds.Concat(claim3Ids);
    }
}
