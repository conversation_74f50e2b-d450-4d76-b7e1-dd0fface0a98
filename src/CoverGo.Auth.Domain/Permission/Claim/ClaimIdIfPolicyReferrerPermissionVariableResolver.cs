﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using IClaimsService = CoverGo.Auth.Domain.OtherServices.IClaimsService;

namespace CoverGo.Auth.Domain.Permission.Claim;

public class ClaimIdIfPolicyReferrerPermissionVariableResolver : IPermissionVariableResolver
{
    private readonly IClaimsService _claimsService;
    private readonly IEntityService _entityService;
    private readonly IPolicyService _policyService;

    public ClaimIdIfPolicyReferrerPermissionVariableResolver(
        IClaimsService claimsService,
        IEntityService entityService,
        IPolicyService policyService)
    {
        _claimsService = claimsService;
        _entityService = entityService;
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value.Contains("{claimIdIfPolicyReferrer}");
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var entity = await GetEntityAsync(context, cancellationToken);
        if (entity == null) {
            return Enumerable.Empty<string>();
        }

        var policyIdsOfReferrer = await GetPolicyIdsOfReferrerAsync(context, entity, cancellationToken);

        if (policyIdsOfReferrer == null || !policyIdsOfReferrer.Any()) {
            return Enumerable.Empty<string>();
        }

        var query = new QueryArguments {
            Where = new ClaimWhere {
                PolicyId_in = policyIdsOfReferrer
            }
        };

        return await _claimsService.GetIdsAsync(context.TenantId, query, cancellationToken);
    }

    private async Task<OtherServices.Entity> GetEntityAsync(PermissionContext context, CancellationToken cancellationToken)
    {
        var where = new EntityWhere {
            Id = context.User.EntityId
        };
        var entities = await _entityService.GenericQueryAsync(
            context.TenantId, where, cancellationToken);

        return entities.FirstOrDefault();
    }

    private Task<IEnumerable<string>> GetPolicyIdsOfReferrerAsync(
        PermissionContext context, OtherServices.Entity entity, CancellationToken cancellationToken)
    {
        var where = new PolicyWhere { ReferralCode = entity.InternalCode };

        return _policyService.GetIdsAsync(context.TenantId, @where, cancellationToken);
    }
}