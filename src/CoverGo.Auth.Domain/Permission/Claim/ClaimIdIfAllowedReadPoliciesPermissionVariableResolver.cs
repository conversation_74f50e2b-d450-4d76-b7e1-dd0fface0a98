﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Domain.Utils;
using CoverGo.Claims3.Client;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission.Claim;

public class ClaimIdIfAllowedReadPoliciesPermissionVariableResolver :
    IPermissionVariableResolver, IHasDependencyOnPermissionType
{
    const string DependentPermissionType = "readPolicies";

    private readonly IClaimsService _claimsService;
    private readonly IClaims3Service _claims3Service;
    readonly IPolicyService _policyService;
    private readonly IMultiTenantFeatureManager _multiTenantFeatureManager;

    public ClaimIdIfAllowedReadPoliciesPermissionVariableResolver(
        IClaimsService claimsService,
        IClaims3Service claims3Service,
        IPolicyService policyService,
        IMultiTenantFeatureManager multiTenantFeatureManager)
    {
        _claimsService = claimsService;
        _claims3Service = claims3Service;
        _multiTenantFeatureManager = multiTenantFeatureManager;
        _policyService = policyService;
    }

    public bool CanResolve(System.Security.Claims.Claim claim)
    {
        return claim.Value == "{claimIdIf{allowedReadPolicies}}";
    }

    public string GetDependentPermissionType(System.Security.Claims.Claim _)
    {
        return DependentPermissionType;
    }

    public async Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default)
    {
        var policyIds = context.ResolvedPermissions[DependentPermissionType];

        if (policyIds == null || !policyIds.Any()) {
            return Enumerable.Empty<string>();
        }
        var query = new QueryArguments {
            Where = new ClaimWhere {
                PolicyId_in = policyIds
            }
        };

        IEnumerable<string> claimIds = await _claimsService.GetIdsAsync(context.TenantId, query, cancellationToken);

        if (!await _multiTenantFeatureManager.IsEnabled(FeatureNames.Claims3Permission, context.TenantId))
            return claimIds;

        IEnumerable<OtherServices.Policy> policies = await _policyService.GetAsync(context.TenantId, new PolicyWhere { Id_in = policyIds.ToList() },
            cancellationToken);

        List<string> issuerNumbers = policies.Select(x => x.IssuerNumber).ToList();

        IEnumerable<string> claim3Ids = await _claims3Service.GetIdsAsync(
            new ClaimFilterInput { PolicyID = new c3StringOperationFilterInput { In = issuerNumbers } },
            cancellationToken);

        return claimIds.Concat(claim3Ids);
    }
}
