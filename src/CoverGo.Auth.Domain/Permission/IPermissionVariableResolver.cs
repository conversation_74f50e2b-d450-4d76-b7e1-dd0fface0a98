﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission;

public interface IPermissionVariableResolver
{
    Task<IEnumerable<string>> ResolveAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        CancellationToken cancellationToken = default);
    
    bool CanResolve(System.Security.Claims.Claim claim);
}