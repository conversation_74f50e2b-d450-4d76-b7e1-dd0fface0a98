﻿using CoverGo.Auth.Domain.OtherServices;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Permission;

// if a permission variable is a linked one(relationship based) then it should be first checked by non-generic linked/relationship based resolvers
// If non-generic resolver can recognize the variable, then it means they'll apply one more transformation on the linked ids (e.g; fetch linked policies )
// Otherwise, it'll be recognized  & resolved by generic resolver (this one). Hence it should be placed after non-generic linked based permission variable resolvers
public class GenericLinkedPermissionVariableResolver : LinkedPermissionVariableResolver
{
    static readonly HashSet<string> IgnoredPrefixes = new()
    {
        "{caseIdIf",
        "{transactionIdForClaimIdIf",
        "{proposalIdIf",
        "{claimIdIf",
        "{policyIdIf"
    };
    static readonly HashSet<string> IgnoredSuffixes = new()
    {
        "IsHolder}",
        "IsInsured}",
        "IsUninsured}",
        "IsClaimant}"
    };

    public GenericLinkedPermissionVariableResolver(
        IEntityService entityService) 
        : base(entityService, "{", "}")
    {
    }

    public override bool CanResolve(System.Security.Claims.Claim claim)
    {
        return base.CanResolve(claim) &&
               !IgnoredPrefixes.Any(p => claim.Value.StartsWith(p)) &&
               !IgnoredSuffixes.Any(p => claim.Value.EndsWith(p));
    }

    protected override Task<IEnumerable<string>> ResolveLinksAsync(
        PermissionContext context,
        System.Security.Claims.Claim claim,
        IEnumerable<string> linkedIds,
        CancellationToken cancellationToken = default)
    {
        return Task.FromResult(linkedIds);
    }
}