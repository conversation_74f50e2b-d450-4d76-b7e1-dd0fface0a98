﻿using CoverGo.DomainUtils;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Schema;
using System.Collections.Generic;

namespace CoverGo.Auth.Domain.PermissionSchemas
{
    public class PermissionSchema : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string ObjectType { get; set; }
        public PermissionSchemaActionType ActionType { get; set; }

        [BsonSerializer(typeof(MongoPermissionSchemaSerializer))]
        public JToken Schema { get; set; }
        public FieldsWhere StateCondition { get; set; }
        public FieldsWhere UpdateCondition { get; set; }
    }

    public enum PermissionSchemaActionType
    {
        Read = 1,
        Write = 2
    }

    public class CreatePermissionSchemaCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string ObjectType { get; set; }
        public PermissionSchemaActionType ActionType { get; set; }
        public string Schema { get; set; }
        public FieldsWhere StateCondition { get; set; }
        public FieldsWhere UpdateCondition { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdatePermissionSchemaCommand
    {
        public string PermissionSchemaId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string ObjectType { get; set; }
        public PermissionSchemaActionType? ActionType { get; set; }
        public string Schema { get; set; }
        public string SchemaPatch { get; set; }
        public FieldsWhere StateCondition { get; set; }
        public FieldsWhere UpdateCondition { get; set; }
        public string ModifiedById { get; set; }
    }

    public class PermissionSchemaWhere : Where
    {
        public IEnumerable<PermissionSchemaWhere> And { get; set; }
        public IEnumerable<PermissionSchemaWhere> Or { get; set; }
        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string ObjectType { get; set; }
        public PermissionSchemaActionType? ActionType { get; set; }
        public string Name { get; set; }
        public FieldsWhere Schema { get; set; }
    }
}