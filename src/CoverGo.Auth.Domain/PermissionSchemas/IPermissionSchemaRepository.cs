﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.PermissionSchemas
{
    public interface IPermissionSchemaRepository
    {
        public string ProviderId { get; }
        Task<IReadOnlyCollection<PermissionSchema>> Get(string tenantId, PermissionSchemaWhere @where,
            CancellationToken cancellationToken, OrderBy orderBy = null, int? skip = null, int? first = null);
        Task<long> GetTotalCount(string tenantId, PermissionSchemaWhere @where, CancellationToken cancellationToken);
        Task<Result<CreatedStatus>> Create(string tenantId, string id, CreatePermissionSchemaCommand command, CancellationToken cancellationToken);
        Task<Result> Update(string tenantId, UpdatePermissionSchemaCommand command, CancellationToken cancellationToken);
        Task<Result> Delete(string tenantId, string permissionSchemaId, DeleteCommand command, CancellationToken cancellationToken);
    }
}