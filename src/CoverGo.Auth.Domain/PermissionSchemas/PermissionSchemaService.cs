﻿using CoverGo.DomainUtils;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MongoDB.Driver;

namespace CoverGo.Auth.Domain.PermissionSchemas
{
    public class PermissionSchemaService
    {
        public PermissionSchemaService(IPermissionSchemaRepository permissionSchemaRepository) =>
            _permissionSchemaRepository = permissionSchemaRepository;

        readonly IPermissionSchemaRepository _permissionSchemaRepository;

        public Task<IReadOnlyCollection<PermissionSchema>> Get(string tenantId, PermissionSchemaWhere @where,
            CancellationToken cancellationToken, OrderBy orderBy = null, int? skip = null, int? first = null) =>
            _permissionSchemaRepository.Get(tenantId, @where, cancellationToken, orderBy, skip: skip, first: first);

        public Task<long> GetTotalCount(string tenantId, PermissionSchemaWhere @where, CancellationToken cancellationToken) =>
            _permissionSchemaRepository.GetTotalCount(tenantId, @where, cancellationToken);

        public Task<Result<CreatedStatus>> Create(string tenantId, CreatePermissionSchemaCommand command, CancellationToken cancellationToken) =>
            _permissionSchemaRepository.Create(tenantId, Guid.NewGuid().ToString(), command, cancellationToken);

        public Task<Result> Update(string tenantId, UpdatePermissionSchemaCommand command, CancellationToken cancellationToken) =>
            _permissionSchemaRepository.Update(tenantId, command, cancellationToken);

        public Task<Result> Delete(string tenantId, string dataSchemaId, DeleteCommand command, CancellationToken cancellationToken) =>
            _permissionSchemaRepository.Delete(tenantId, dataSchemaId, command, cancellationToken);
    }
}