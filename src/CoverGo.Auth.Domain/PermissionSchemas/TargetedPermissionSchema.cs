﻿using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Auth.Domain.PermissionSchemas
{
    public class TargetedPermissionSchema : SystemObject
    {
        public string Id { get; set; }

        public string PermissionSchemaId { get; set; }

        public IReadOnlyCollection<string> TargetIds { get; set; }
    }

    public class AddTargetedPermissionSchemaToLoginCommand
    {
        public string LoginId { get; set; }

        public string PermissionSchemaId { get; set; }

        public IReadOnlyCollection<string> TargetIds { get; set; }

        public string AddedById { get; set; }
    }

    public class RemoveTargetedPermissionSchemaFromLoginCommand
    {
        public string LoginId { get; set; }

        public string PermissionSchemaId { get; set; }

        public IReadOnlyCollection<string> TargetIds { get; set; }

        public string RemovedById { get; set; }
    }
}