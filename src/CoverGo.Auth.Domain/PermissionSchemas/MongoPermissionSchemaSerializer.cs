using System;
using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Auth.Domain.PermissionSchemas
{
    public class MongoPermissionSchemaSerializer : SerializerBase<JToken>
    {

        private static readonly (string Token, string Replacement)[] replacements = new[] { ("$id", "_id"), ("$schema", "_schema") };

        public override JToken Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
        {
            BsonValue bsonValue = BsonValueSerializer.Instance.Deserialize(context);
            string jsonString = JsonConvert.SerializeObject(BsonTypeMapper.MapToDotNetValue(bsonValue));
            foreach ((string Token, string Replacement) in replacements)
            {
                jsonString = jsonString.Replace(Replacement, Token);
            }
            return JToken.Parse(jsonString);
        }

        public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, JToken value)
        {
            string stringValue = value.ToString();
            foreach ((string Token, string Replacement) in replacements)
            {
                stringValue = stringValue.Replace(Token, Replacement);
            }
            BsonValueSerializer.Instance.Serialize(context, BsonDocument.Parse(stringValue));
        }
    }
}
