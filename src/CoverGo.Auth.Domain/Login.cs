using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;

namespace CoverGo.Auth.Domain
{
    public class Login : SystemObject
    {
        public string Id { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public bool IsEmailConfirmed { get; set; }
        public string TelephoneNumber { get; set; }
        public bool IsTelephoneNumberConfirmed { get; set; }
        public bool IsPasswordValidationDobRequire { get; set; }
        public DateTime? LockoutEndDateUtc { get; set; }
        public int AccessFailedCount { get; set; }
        public bool IgnorePasswordLifespan { get; set; }
        public bool IgnoreAccountLockout { get; set; }
        public Dictionary<string, IEnumerable<string>> TargettedPermissions { get; set; }
        public IEnumerable<PermissionGroup> PermissionGroups { get; set; }
        public IEnumerable<string> InheritedLoginIds { get; set; }
        public string EntityId { get; set; }
        public List<string> TargetedPermissionSchemaIds { get; set; }
        public bool PermissionLazyLoadingRequired { get; set; }
        public bool IsActive { get; set; } = true;
        public string LatestOtp { get; set; }
        public DateTime? PasswordLastUpdated { get; set; }
    }

    public class Permissions
    {
        public string LoginId { get; set; }
        public Dictionary<string, IEnumerable<string>> TargettedPermissions { get; set; }
        public IEnumerable<PermissionGroup> PermissionGroups { get; set; }
        public IEnumerable<string> InheritedLoginIds { get; set; }
        public bool LazyLoadingRequired { get; set; }
    }

    public class PasswordValidators : IUniqueSystemObject
    {
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }
        public string ClientId { get; set; }

        public bool EnableLockout { get; set; }
        public int LockoutTimespanInSeconds { get; set; }
        public int LockoutMaxFailedAccessAttempts { get; set; }
        public string LockoutEmailTemplateId { get; set; }

        public bool RequireConfirmedEmail { get; set; } = false;
        public bool RequireConfirmPhoneNumber { get; set; } = false;
        public bool RequireDigit { get; set; } = false;
        public int RequireLength { get; set; } = 8;
        public int RequireUniqueChars { get; set; } = 1;
        public bool RequireLowercase { get; set; } = false;
        public bool RequireUppercase { get; set; } = false;
        [BsonElement("requireNonAlphaNumeric")]
        public bool RequireNonAlphanumeric { get; set; } = false;
        public bool RequireMaxConsecutiveRepeatingCharacters { get; set; } = false; // 6
        public bool RequireMaxIncrementalSequenceCharacters { get; set; } = false; // 6
        public bool RequireLetter { get; set; }

        public bool ExposeErrorMessage { get; set; }

        public int? PasswordLifespanInSeconds { get; set; }
        public int? TempPasswordLifespanInSeonds { get; set; }

        public int? SavePasswordHistoryCount { get; set; } //only for password history check

        public bool AllowExpiredPasswordEasyReset { get; set; }

        public bool PasswordResetRequireDobVerification { get; set; }
        public int? MaxDobVerificationAttempts { get; set; }
        public int? Email2faLifespanInSeconds { get; set; } = 540; // 9 minutes, AspNetCore.Identity default.
        public int? Email2faTimeStepInSeconds { get; set; }

        public bool NotContainUsername { get; set; }
    }

    public class UpdatePasswordValidatorsCommand : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string ClientId { get; set; }
        public bool? EnableLockout { get; set; }
        public int? LockoutTimespanInSeconds { get; set; }
        public int? LockoutMaxFailedAccessAttempts { get; set; }
        public string LockoutEmailTemplateId { get; set; }
        public bool? RequireConfirmedEmail { get; set; }
        public bool? RequireConfirmPhoneNumber { get; set; }
        public bool? RequireDigit { get; set; }
        public int? RequireLength { get; set; }
        public int? RequireUniqueChars { get; set; }
        public bool? RequireLowercase { get; set; }
        public bool? RequireUppercase { get; set; }
        public bool? RequireNonAlphanumeric { get; set; }
        public bool? RequireMaxConsecutiveRepeatingCharacters { get; set; }
        public bool? RequireMaxIncrementalSequenceCharacters { get; set; }
        public bool? RequireLetter { get; set; }
        public bool? ExposeErrorMessage { get; set; }
        public int? PasswordLifespanInSeconds { get; set; }
        public int? TempPasswordLifespanInSeonds { get; set; }
        public int? SavePasswordHistoryCount { get; set; }
        public bool? AllowExpiredPasswordEasyReset { get; set; }
        public bool? PasswordResetRequireDobVerification { get; set; }
        public int? MaxDobVerificationAttempts { get; set; }
        public bool? NotContainUsername { get; set; }
        public int? Email2faLifespanInSeconds { get; set; }
    }

    //public class MaxRepeatedCharactersPasswordValidator<TUser> : IPasswordValidator<TUser>
    //   where TUser : class
    //{
    //    //public int? MaxConsecutiveRepeatingCharacters { get; set; }

    //    public Task<IdentityResult> ValidateAsync(UserManager<TUser> manager, TUser user, string password)
    //    {
    //        manager.Pass
    //        if (MaxConsecutiveRepeatingCharacters == null)
    //            return Task.FromResult(IdentityResult.Success);

    //        int longestRun = password
    //            .Select((c, index) => password.Substring(index).TakeWhile(e => e == c))
    //            .OrderByDescending(e => e.Count())
    //            .First().Count();

    //        return Task.FromResult(longestRun > 6 ?
    //            IdentityResult.Failed(new IdentityError
    //            {
    //                Code = "MaxRepeatedCharacters",
    //                Description = $"The max characters repetition in a row is {6}."
    //            }) :
    //            IdentityResult.Success);
    //    }
    //}

    public class CreateLoginCommand
    {
        public string ClientId { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public string TelephoneNumber { get; set; }
        public string Password { get; set; }
        public string CreatedById { get; set; }
        public string EntityId { get; set; }
        public string EntityType { get; set; }
        public bool IsEmailConfirmed { get; set; }
        public bool IgnorePasswordValidation { get; set; }
        public bool IgnorePasswordLifespan { get; set; }
        public bool IgnoreAccountLockout { get; set; }
        public IEnumerable<string> AppIdsToBeGrantedAccessTo { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
        public string RedirectQueryString { get; set; }
        public bool? UseDefaultPermissions { get; set; }
        public DateTime? ActiveFrom { get; set; }
        public bool ShouldSerializePassword() => false;
    }

    public class UpdateLoginCommand
    {
        public string UserName { get; set; }
        public bool IsUserNameChanged { get; set; }
        public string Email { get; set; }
        public bool IsEmailChanged { get; set; }
        public string TelephoneNumber { get; set; }
        public bool IsTelephoneNumberChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class ConfirmEmailCommand
    {
        public string Code { get; set; }
        public string ConfirmedById { get; set; }
        public string AppId { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
    }

    public class ResetPasswordCommand
    {
        public string Code { get; set; }
        public string Password { get; set; }
        public DateTime? DateOfBirth { get; set; }

        public SendNotificationCommand SendNotificationCommand { get; set; }

        public bool ShouldSerializePassword() => false;
    }

    public class ChangePasswordCommand
    {
        public string CurrentPassword { get; set; }
        public string NewPassword { get; set; }
        public bool IgnorePasswordValidation { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
        public string ChangedById { get; set; }

        public bool ShouldSerializeCurrentPassword() => false;
        public bool ShouldSerializeNewPassword() => false;
    }

    public class ChangeExpiredPasswordCommand
    {
        public string UserName { get; set; }
        public string CurrentPassword { get; set; }
        public string NewPassword { get; set; }
    }

    public class ForgotPasswordCommand
    {
        public string ClientId { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public string Language { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
        public string ForgottenById { get; set; }
        public DateTime? DateOfBirth { get; set; }
    }

    public class ResendEmailCommand
    {
        public string ClientId { get; set; }
        public string ResentById { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
        public string CallbackUrl { get; set; }
    }

    public class VerifyCodeCommand
    {
        public string Code { get; set; }
        public string Purpose { get; set; }
        public string VerifiedById { get; set; }
    }

    public class SendCodeCommand
    {
        public string ClientId { get; set; }
        public string SentById { get; set; }
        public string TemplateId { get; set; }
        public string Purpose { get; set; }
    }
}
