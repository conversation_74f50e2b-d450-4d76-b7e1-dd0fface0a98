﻿using CoverGo.Configuration;
using CoverGo.DomainUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain
{
    public interface ITenantRepository
    {
        string ProviderId { get; }

        Task<TenantSettings> GetTenantSettingsAsync(string tenantId, CancellationToken cancellationToken);
        Task<TenantIdAndAppId> GetTenantIdAndAppIdFromUrl(string appUrl, CancellationToken cancellationToken);
        Task<Result> AddHostToTenantSettingsAsync(string tenantId, string host, CancellationToken cancellationToken);
        Task<Result> RemoveHostFromTenantSettingsAsync(string tenantId, string domain,
            CancellationToken cancellationToken);

        Task<App> GetAppAsync(string tenantId, string clientId, CancellationToken cancellationToken);
        Task<IEnumerable<App>> GetAppsAsync(string tenantId, AppWhere @where, OrderBy orderBy, int? skip, int? first,
            CancellationToken cancellationToken);
        Task<long> GetAppTotalCount(string tenantId, AppWhere @where, CancellationToken cancellationToken);
        Task<Result> CreateAppAsync(string tenantId, CreateAppCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateAppAsync(string tenantId, string appId, UpdateAppCommand command,
            CancellationToken cancellationToken);
        Task<Result> DeleteAppAsync(string tenantId, string appId, DeleteCommand command,
            CancellationToken cancellationToken);
        Task<IList<string>> GetTenantIdsAsync(DbConfig config, CancellationToken cancellationToken);
    }
}
