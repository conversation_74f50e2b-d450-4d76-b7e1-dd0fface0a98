﻿using CoverGo.DomainUtils;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.OtherServices
{
    public interface IEntityService
    {
        Task<IEnumerable<string>> GenericQueryIdsAsync(string tenantId, EntityWhere @where,
            CancellationToken cancellationToken);
        Task<IEnumerable<Entity>> GenericQueryAsync(string tenantId, EntityWhere @where,
            CancellationToken cancellationToken);
        Task<IEnumerable<Relationships>> GetRelationshipsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
        Task<IEnumerable<string>> GetIndividualIdsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
        Task<IEnumerable<Individual>> GetIndividualsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
    }

    public class Relationships
    {
        public string EntityId { get; set; }
        public IEnumerable<Link> Links { get; set; }
    }

    public class Link : SystemObject
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string TargetId { get; set; }
        public string TargetEntityType { get; set; }
    }
    public class Individual : Entity
    {
        public DateTime? DateOfBirth { get; set; }
    }

    public class RelationshipWhere : Where
    {
        public IEnumerable<RelationshipWhere> Or { get; set; }
        public IEnumerable<RelationshipWhere> And { get; set; }
        public IEnumerable<string> EntityId_in { get; set; } // checks both source and target
        public string Type { get; set; }
        public IEnumerable<string> Type_in { get; set; }
        public LinkFilter Link { get; set; }
    }

    public class LinkFilter
    {
        public string SourceId { get; set; }
        public IEnumerable<string> SourceId_in { get; set; }
        public string Type { get; set; }
        public IEnumerable<string> TypeIn { get; set; }
        public string TargetId { get; set; }
        public IEnumerable<string> TargetId_in { get; set; }
        public IEnumerable<string> TargetEntityTypes_in { get; set; }
    }

    public class IndividualWhere : Where
    {
        public List<IndividualWhere> Or { get; set; }
        public string Id { get; set; }
        public string Source_contains { get; set; }
    }
}
