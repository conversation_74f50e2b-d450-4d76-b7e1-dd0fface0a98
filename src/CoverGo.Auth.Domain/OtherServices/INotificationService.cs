﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.OtherServices
{
    public interface INotificationService
    {
        Task<Result> SendAsync(string tenantId, SendNotificationCommand command, CancellationToken cancellationToken);

        Task<IEnumerable<Notification>> GetNotificationsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
        Task<IEnumerable<NotificationSubscription>> GetNotificationSubscriptionsAsync(string tenantId,
            QueryArguments queryArguments, CancellationToken cancellationToken);
        Task<IEnumerable<string>> GetNotificationSubscriptionIdsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
    }

    public class Notification
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string Type { get; set; }
        public PushMessage PushMessage { get; set; }
        public EmailMessage EmailMessage { get; set; }
        public SmsMessage SmsMessage { get; set; }
        public string Status { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class NotificationSubscription : SystemObject
    {
        public string Id { get; set; }

        public string TopicName { get; set; }
    }

    public class SendNotificationCommand
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string FromEntityId { get; set; }
        public string ToEntityId { get; set; }
        public string PolicyId { get; set; }
        public string OfferId { get; set; }
        public PushMessage PushMessage { get; set; }
        public EmailMessage EmailMessage { get; set; }
        public SmsMessage SmsMessage { get; set; }
        public bool? UseConfig { get; set; }
        public string SentById { get; set; }
        public string AppId { get; set; }
        public string LoginId { get; set; }
        public string CallbackUrl { get; set; }
        public string RedirectQueryString { get; set; }
        public string Language { get; set; }
        public DateTime? ScheduleToSendAt { get; set; }
    }

    public class PushMessage
    {
        public string Token { get; set; }
        public string Topic { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public Dictionary<string, string> Data { get; set; }
    }

    public class EmailMessage
    {
        public string From { get; set; }
        public string FromName { get; set; }
        public string To { get; set; }
        public List<string> Tos { get; set; }
        public List<string> Ccs { get; set; }
        public List<string> Bccs { get; set; }
        public string Subject { get; set; }
        public string HtmlContent { get; set; }
        public List<PdfAttachment> PdfAttachments { get; set; }
        public TemplateRendering TemplateRendering { get; set; }
    }

    public class TemplateRendering
    {
        public string TemplateId { get; set; }
        public RenderParameters Input { get; set; }
    }

    public class RenderParameters
    {
        public string Name { get; set; }
        public string ContentJsonString { get; set; }
        public JToken Content { get; set; }

        public string Url { get; set; }
        public string AccessToken { get; set; }
        public JObject Variables { get; set; }
        public string VariablesJsonString { get; set; }
        public List<AttachmentTemplate> OverrideAttachmentTemplates { get; set; }
        public List<AttachmentReference> OverrideAttachmentReferences { get; set; }
    }
    
    public class AttachmentTemplate
    {
        public string FileName { get; set; }
        public string TemplateId { get; set; }
    }
    
    public class AttachmentReference
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
    }

    public class PdfAttachment
    {
        public string FileName { get; set; }
        public string HtmlContent { get; set; }
        public byte[] Bytes { get; set; }
        public string Password { get; set; }
    }

    public class SmsMessage
    {
        public string From { get; set; }
        public string To { get; set; }
        public string Body { get; set; }
        public TemplateRendering TemplateRendering { get; set; }
    }

    public class NotificationWhere : Where
    {
        public List<NotificationWhere> Or { get; set; }
        public List<NotificationWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string FromEntityId { get; set; }
        public List<string> FromEntityId_in { get; set; }
        public string FromEntityId_not { get; set; }
        public string ToEntityId { get; set; }
        public List<string> ToEntityId_in { get; set; }
        public string PolicyId { get; set; }
        public List<string> PolicyId_in { get; set; }
        public string OfferId { get; set; }
        public List<string> OfferId_in { get; set; }
        public string Type { get; set; }
        public List<string> Type_in { get; set; }
        public string Status { get; set; }
        public List<string> Status_in { get; set; }
        public string Status_not { get; set; }
        public string ToTopic { get; set; }
        public List<string> ToTopic_in { get; set; }
        public string ToTopicId { get; set; }
        public List<string> ToTopicId_in { get; set; }
    }

    public class NotificationSubscriptionWhere : Where
    {
        public List<NotificationSubscriptionWhere> Or { get; set; }
        public List<NotificationSubscriptionWhere> And { get; set; }
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string Topic { get; set; }
        public List<string> Topic_in { get; set; }
        public string EntitiesIds_contains { get; set; }
    }

    public static class SendNotificationCommandExtension
    {
        public static SendNotificationCommand AddOtpToEmailMessage(this SendNotificationCommand command, string otp)
        {
            if (command.EmailMessage == null)
            {
                return command;
            }

            EmailMessage emailMessage = command.EmailMessage;

            if (emailMessage.HtmlContent != null)
            {
                emailMessage.HtmlContent = emailMessage.HtmlContent.Replace("{{otp}}", otp);

                command.EmailMessage = emailMessage;
                return command;
            }

            if (emailMessage.TemplateRendering != null)
            {
                var renderContent = JToken.FromObject(new
                {
                    otp = otp,
                });

                if (emailMessage.TemplateRendering.Input == null)
                    emailMessage.TemplateRendering.Input = new RenderParameters
                    {
                        Name = "data",
                        Content = renderContent
                    };
                else
                {
                    if (emailMessage.TemplateRendering.Input.Content?.Any() == true)
                        (emailMessage.TemplateRendering.Input.Content as JObject).Merge(renderContent);
                    else
                        emailMessage.TemplateRendering.Input.Content = renderContent;
                }
            }

            command.EmailMessage = emailMessage;
            return command;
        }

        public static SendNotificationCommand AddOtpToSmsMessage(this SendNotificationCommand command, string otp)
        {
            if (command.SmsMessage == null)
            {
                return command;
            }

            SmsMessage smsMessage = command.SmsMessage;

            if (smsMessage.Body != null)
            {
                command.SmsMessage.Body = command.SmsMessage.Body.Replace("{{otp}}", otp);

                command.SmsMessage = smsMessage;
                return command;
            }

            if (smsMessage.TemplateRendering != null)
            {
                var renderContent = JToken.FromObject(new
                {
                    otp = otp,
                });

                if (smsMessage.TemplateRendering.Input == null)
                    smsMessage.TemplateRendering.Input = new RenderParameters
                    {
                        Name = "data",
                        Content = renderContent
                    };
                else
                {
                    if (smsMessage.TemplateRendering.Input.Content?.Any() == true)
                        (smsMessage.TemplateRendering.Input.Content as JObject).Merge(renderContent);
                    else
                        smsMessage.TemplateRendering.Input.Content = renderContent;
                }
            }

            command.SmsMessage = smsMessage;
            return command;
        }
    }

}
