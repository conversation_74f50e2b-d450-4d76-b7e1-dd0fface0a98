﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.OtherServices
{
    public interface ICoverGoChannelManagementService
    {
        Task<IEnumerable<string>> GetIdsAsync(string createdById, CancellationToken cancellationToken = default);
        Task<IEnumerable<string>> GetTeamMembersLoginIdsAsync(string teamLeadLoginId, CancellationToken cancellationToken = default);
        Task<Tuple<DateTimeOffset?, DateTimeOffset?>> GetAgentActiveAndTerminationDateTimeAsync(string email, string accessToken, CancellationToken cancellationToken = default);
    }
}
