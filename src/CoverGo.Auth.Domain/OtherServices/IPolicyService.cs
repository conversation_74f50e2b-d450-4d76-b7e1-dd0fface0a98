﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.OtherServices
{
    public interface IPolicyService
    {
        Task<IEnumerable<string>> GetIdsAsync(string tenantId, PolicyWhere @where, CancellationToken cancellationToken);
        Task<IEnumerable<Policy>> GetAsync(string tenantId, PolicyWhere @where, CancellationToken cancellationToken);
        Task<IEnumerable<PolicyMember>> GetPolicyMembersAsync(string tenantId, PolicyMembersWhere @where,
            CancellationToken cancellationToken);
    }

    public class Policy
    {
        public string Id { get; set; }
        public string IssuerNumber { get; set; }
        public List<Entity> ContractInsured { get; set; }
        public List<Entity> ContractTerminated { get; set; }
    }

    public class Entity
    {
        public string Id { get; set; }
        public string InternalCode { get; set; }
    }

    public class PolicyMember
    {
        public JToken Fields { get; set; }
    }

    public class PolicyMembersWhere : QueryArguments<Filter<Where>>
    {
        public List<string> PolicyId_In { get; set; }
    }

    public class PolicyWhere : Where
    {
        public EntityWhere ContractHolder { get; set; }
        public EntityWhere ContractInsured_some { get; set; }
        public EntityWhere ContractTerminated_some { get; set; }
        public string ReferralCode { get; set; }
        public bool? IsRenewal { get; set; }
        public List<string> Id_in { get; set; }
        public StakeHolderWhere Stakeholders_contains { get; set; }
        public List<PolicyWhere> And { get; set; }
    }

    public class StakeHolderWhere : Where
    {
        public string EntityId { get; set; }
        public List<string> EntityId_in { get; set; }
    }

    public class EntityWhere : Where
    {
        public List<EntityWhere> Or { get; set; }

        public List<EntityWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string Source_contains { get; set; }
        public string Tags_contains { get; set; }
    }
}
