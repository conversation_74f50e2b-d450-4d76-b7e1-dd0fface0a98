﻿using CoverGo.DomainUtils;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.OtherServices
{
    public interface ICaseService
    {
        Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
        Task<IEnumerable<string>> GetProposalIdsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);

        Task<IEnumerable<string>> GetOfferIdsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
        Task<IEnumerable<Case>> GetAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
    }

    public class Case : SystemObject
    {
        public string Id { get; set; }
        public List<string> InsuredIds { get; set; }
        public IEnumerable<BeneficiaryEligibility> BeneficiaryEligibilities { get; set; }
        public IEnumerable<Proposal> Proposals { get; set; }
    }

    public class BeneficiaryEligibility : SystemObject
    {
        public Entity ContractEntity { get; set; }
    }

    public class Proposal : SystemObject
    {
        public string Id { get; set; }
    }

    public class CaseWhere : Where
    {
        public List<CaseWhere> Or { get; set; }

        public List<CaseWhere> And { get; set; }

        public string Source_contains { get; set; }
        public string HolderId { get; set; }
        public IEnumerable<string> HolderId_in { get; set; }
        public StakeholderWhere Stakeholders_contains { get; set; }

        public ProposalWhere Proposals_contains { get; set; }
    }

    public class ProposalWhere : Where
    {
        public string ReferralCode { get; set; }

        public OfferWhere Offers_contains { get; set; }
    }

    public class StakeholderWhere : Where
    {
        public string EntityId { get; set; }
        public List<string> EntityId_in { get; set; }
    }

    public class OfferWhere : Where
    {
       
    }
}
