﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.OtherServices
{
    public interface ITransactionService
    {
        Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
        Task<IEnumerable<PaymentMethod>> GetPaymentMethodsAsync(string tenantId, QueryArguments queryArguments,
            CancellationToken cancellationToken);
        Task<IEnumerable<PaymentMethod>> GetPaymentMethodsAsync(string tenantId, PaymentMethodWhere @where,
            CancellationToken cancellationToken);
    }

    public class TransactionWhere : Where
    {
        public IEnumerable<string> PolicyId_in { get; set; }
        public List<string> ClaimId_in { get; set; }

        public List<TransactionWhere> And { get; set; }
    }

    public class PaymentMethod
    {
        public string Id { get; set; }
    }

    public class PaymentMethodWhere : Where
    {

    }
}
