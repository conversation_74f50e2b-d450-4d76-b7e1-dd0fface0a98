﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.OtherServices;

public interface ISchedulerService
{
    Task<Result> CreateJobScheduleAsync(string tenantId, string appId, JobSchedule jobScheduleInput, CancellationToken cancellationToken);
    Task<Result> DeleteJobScheduleAsync(string tenantId, string name, CancellationToken cancellationToken);
    Task<IReadOnlyCollection<JobScheduleDto>> GetJobSchedulesAsync(string tenantId, QueryArguments<JobScheduleWhere> queryArguments, CancellationToken cancellationToken);
}

public record JobScheduleDto
{
    public string Id { get; init; }
    public string TenantId { get; init; }
    public string DatacenterId { get; init; }
    public string Name { get; init; }
    public string Description { get; init; }
    public string CronExpression { get; init; }
    public bool IsActive { get; init; }
    public DateTime? LastCheckedAt { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime LastModifiedAt { get; init; }
    public string CreatedById { get; init; }
    public string LastModifiedById { get; init; }
}

public class JobSchedule
{
    public string Name { get; init; }
    public string Description { get; init; }
    public string CronExpression { get; init; }
    public string AsLoginId { get; init; }
    public JobDetail JobDetail { get; init; }
}

public class JobDetail
{
    public string Type { get; init; }
    public string JobId { get; init; }
    public string GraphQlOperationType { get; init; }
    public string GraphQlQuery { get; init; }
    public string GraphQlVariables { get; init; }
    public string CustomJobVariables { get; init; }
}

    public class JobScheduleWhere : Where
    {
        public IEnumerable<JobScheduleWhere> And { get; set; }
        public IEnumerable<JobScheduleWhere> Or { get; set; }

        public string Id { get; set; }
        public string DatacenterId { get; set; }
        public string Name { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? LastCheckedAt_lt { get; set; }
        public DateTime? LastCheckedAt_gt { get; set; }
    }
