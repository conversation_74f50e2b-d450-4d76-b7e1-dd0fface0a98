﻿namespace CoverGo.Auth.Domain.Extensions;

public static class StringExtensions
{
    public static string DropPrefixAndSuffix(this string value, string prefix, string suffix)
    {
        return value.RemovePrefix(prefix).RemoveSuffix(suffix);
    }

    private static string RemovePrefix(this string value, string prefix)
    {
        return value.StartsWith(prefix) ? value.Substring(prefix.Length) : value;
    }

    private static string RemoveSuffix(this string value, string suffix)
    {
        return value.EndsWith(suffix) ? value.Substring(0, value.LastIndexOf(suffix)) : value;
    }
}