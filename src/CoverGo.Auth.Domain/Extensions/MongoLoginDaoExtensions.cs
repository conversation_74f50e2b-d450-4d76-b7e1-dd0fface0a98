﻿using System;
using System.Linq;

namespace CoverGo.Auth.Domain.Extensions;

internal static class MongoLoginDaoExtensions
{
    public static string[] GetLoginIds(this MongoLoginDao user)
    {
        return user?.Claims
            ?.Where(c => c?.Type == "logins")
            ?.Select(c => c.Value)
            ?.ToArray() ?? Array.Empty<string>();
    }

    public static string[] GetGroupIds(this MongoLoginDao user)
    {
        return user?.Claims
            ?.Where(c => c.Type == "groups")
            .Select(c => c.Value)
            .ToArray() ?? Array.Empty<string>();
    }
}