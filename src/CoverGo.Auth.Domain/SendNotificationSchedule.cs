﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;

namespace CoverGo.Auth.Domain
{
    public class SendNotificationSchedule : IUniqueSystemObject
    {
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }
        public string Type { get; set; }
        public string FromEntityId { get; set; }
        public string ToEntityId { get; set; }
        public string PolicyId { get; set; }
        public string OfferId { get; set; }
        public PushMessage PushMessage { get; set; }
        public EmailMessage EmailMessage { get; set; }
        public SmsMessage SmsMessage { get; set; }
        public bool? UseConfig { get; set; }
        public string SentById { get; set; }
        public string AppId { get; set; }
        public string LoginId { get; set; }
        public string CallbackUrl { get; set; }
        public string RedirectQueryString { get; set; }
        public string Language { get; set; }
        public DateTime? ScheduleToSendAt { get; set; }
        public DateTime? LastSentAt { get; set; }
        public string Status { get; set; }
    }

    public class UpsertSendNotificationScheduleCommand : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string FromEntityId { get; set; }
        public string ToEntityId { get; set; }
        public string PolicyId { get; set; }
        public string OfferId { get; set; }
        public PushMessage PushMessage { get; set; }
        public EmailMessage EmailMessage { get; set; }
        public SmsMessage SmsMessage { get; set; }
        public bool? UseConfig { get; set; }
        public string SentById { get; set; }
        public string AppId { get; set; }
        public string LoginId { get; set; }
        public string CallbackUrl { get; set; }
        public string RedirectQueryString { get; set; }
        public string Language { get; set; }
        public DateTime? ScheduleToSendAt { get; set; }
        public DateTime? LastSentAt { get; set; }
        public string Status { get; set; }
    }

    public class SendNotificationScheduleFilter
    {
        [FilterCondition(FilterCondition.Eq)]
        public string Type { get; set; }

        [FilterCondition(FilterCondition.Eq)]
        public string LoginId { get; set; }

        [FilterCondition(FilterCondition.Eq)]
        public string Status { get; set; }

        [FilterCondition(FilterCondition.Lt, nameof(SendNotificationSchedule.ScheduleToSendAt))]
        public DateTime? ScheduleToSendAt_lt { get; set; }
    }

    public class RescheduleNotificationCommand
    {
        public string LoginId { get; set; }
        public string Type { get; set; }
        public DateTime? ScheduleToSendAt { get; set; }
    }
}
