using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Identity;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain
{
    public interface IAuthService
    {
        Task<IEnumerable<App>> GetAppsAsync(string tenantId, AppWhere @where, OrderBy orderBy, int? skip, int? first,
            CancellationToken cancellationToken);

        Task<IEnumerable<MongoLoginDao>> GetLoginsAsync(string tenantId, LoginWhere where,
            OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken);

        UserManager<MongoLoginDao> GetMongoUserManager(string tenantId);

        Task<MongoLoginDao> FindLoginByIdAsync(string tenantId, string loginId, CancellationToken cancellationToken);

        Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId,
            IEnumerable<string> permissionGroupIds, CancellationToken cancellationToken);

        Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId,
            CancellationToken cancellationToken, PermissionGroupWhere permissionGroupIds = null);

        Task<IEnumerable<string>> GetLoginIdsAsync(string tenantId, LoginWhere @where,
            CancellationToken cancellationToken);
        
        Task<IEnumerable<string>> GetLoginIdsAsync(string tenantId, LoginWhere @where, OrderBy orderBy,
            int? skip, int? first, CancellationToken cancellationToken);

        Task<IEnumerable<PermissionGroup>> GetPermissionGroupsByCreatorIdAsync(string tenantId, string id,
            CancellationToken cancellationToken);

        Task<Result> UpdateLatestOtpAsync(string tenantId, string loginId, string latestOtp, CancellationToken cancellationToken);
    }
}
