﻿using System;
namespace CoverGo.Auth.Domain.Otp
{
    public class Token
    {
        public string AccessToken { get; set; }
        public string IdentityToken { get; set; }
        public string TokenType { get; set; }
        public string RefreshToken { get; set; }
        public string ErrorDescription { get; set; }
        public int ExpiresIn { get; set; }
        public string Error { get; set; }
    }
}
