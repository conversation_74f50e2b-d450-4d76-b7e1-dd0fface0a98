﻿using System.Threading;
using System.Threading.Tasks;
using CoverGo.Auth.Domain.Otp.Commands;
using CoverGo.Auth.Domain.Otp.Responses;
using CoverGo.DomainUtils;

namespace CoverGo.Auth.Domain.Otp
{
    public interface IOtpService
    {
        public Result<PreOtpLogin> CreatePreOtpLogin(string tenantId, CreatePreOtpLoginCommand command);
        public Task<Result<OtpLogin>> CreateOtpLoginAsync(string tenantId, CreateOtpLoginCommand command,
            CancellationToken cancellationToken);
        public Task<Result<Token>> CreateAccessTokenFromOtpLoginAsync(string tenantId,
            CreateAccessTokenFromOtpLoginCommand command, CancellationToken cancellationToken);

        public Task<Result> GetRemarksOTPAsync(string tenantId, string username, CancellationToken cancellationToken);
        public Task<Result> ValidateRemarksOTPAsync(string tenantId, OTPRemarks lstUser, CancellationToken cancellationToken);
    }
}
