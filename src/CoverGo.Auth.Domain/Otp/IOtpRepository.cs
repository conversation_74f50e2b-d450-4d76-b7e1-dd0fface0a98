﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Auth.Infrastructure.Adapters.Mongo.Otp;
using CoverGo.DomainUtils;

namespace CoverGo.Auth.Domain.Otp
{
    public interface IOtpRepository
    {
        public Task<IReadOnlyCollection<OtpDao>> GetOtpNotificationsAsync(string tenantId, OtpWhere where, CancellationToken cancellationToken);
        public Task UpdateAsync(string tenantId, UpdateOtpCommand command, CancellationToken cancellationToken);
        public Task CreateOtpNotificationAsync(string tenantId, DateTime currentTime, CancellationToken cancellationToken, string otp, string otpToken, string email = null, string phoneNumber = null);
    }
}
