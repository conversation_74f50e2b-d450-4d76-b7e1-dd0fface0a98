﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace CoverGo.Auth.Domain.Otp
{
    public static class OtpHelpers
    {
        private static readonly DateTime UnixEpoch = new(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        public static string RandomSixDigitsString()
        {
            Random r = new ();
            int randNum = r.Next(1000000);
            return randNum.ToString("D6");
        }

        public static string BuildTokenHashInputMsg(OtpLoginToken token, string otp = "") =>
            token.ClientId + token.Username + token.Password + token.Email + token.PhoneNumber + token.Timestamp + otp;

        public static bool IsTokenTimestampValid(OtpLoginToken token)
        {
            bool valid = long.TryParse(token.Timestamp, out long tokenIssuedTimeInMilliseconds);
            if (!valid)
                return false;

            DateTime tokenIssuedTime = UnixEpoch.AddMilliseconds(tokenIssuedTimeInMilliseconds);
            TimeSpan duration = DateTime.UtcNow - tokenIssuedTime;

            return duration.TotalSeconds is > 0 and < OtpConstants.DefaultTokenValidDurationInSecond;
        }

        public static string ComputeHmac(string message, HMAC hmac) =>
            Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(message)));

        public static bool VerifyHmac(string message, string hash, HMAC hmac)
        {
            string computedHash = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(message)));

            return String.Equals(hash, computedHash);
        }

        public static Aes CreateAesCipher(string key, string iv)
        {
            var aesCipher = Aes.Create();
            aesCipher.KeySize = 256;
            aesCipher.Mode = CipherMode.CBC;
            aesCipher.Padding = PaddingMode.PKCS7;
            aesCipher.Key = Convert.FromBase64String(key);
            aesCipher.IV = Convert.FromBase64String(iv);

            return aesCipher;
        }

        public static byte[] Encrypt(string plainText, Aes aes)
        {
            byte[] encrypted;

            // Create an encryptor to perform the stream transform.
            ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

            // Create the streams used for encryption.
            using (MemoryStream msEncrypt = new MemoryStream())
            {
                using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                {
                    using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                    {
                        //Write all data to the stream.
                        swEncrypt.Write(plainText);
                    }
                    encrypted = msEncrypt.ToArray();
                }

                // Return the encrypted bytes from the memory stream.
                return encrypted;
            }
        }

        public static string Decrypt(byte[] cipherText, Aes aes)
        {
            string plaintext = null;

            // Create a decryptor to perform the stream transform.
            ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

            // Create the streams used for decryption.
            using (MemoryStream msDecrypt = new MemoryStream(cipherText))
            {
                using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                {
                    using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                    {

                        // Read the decrypted bytes from the decrypting stream
                        // and place them in a string.
                        plaintext = srDecrypt.ReadToEnd();
                    }
                }
            }

            return plaintext;
        }
    }
}
