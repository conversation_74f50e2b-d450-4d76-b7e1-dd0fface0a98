﻿using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Auth.Domain.Utils
{
    public class SourceContainsHelper
    {
        public static bool IsSourceContainsPermission(string targettedPermission) =>
            targettedPermission.Contains("caseIdIfSourceContains=");

        public static List<string> GetSourceIds(string targettedId)
        {
            var targetIds = targettedId.Substring(targettedId.IndexOf("=") + 1).Replace(" ", "");

            return targetIds.Length == 0 ? Enumerable.Empty<string>().ToList() : targetIds.Remove(targetIds.Length - 1).Split(',').ToList();
        }
        public static bool IsIndividualSourceContainsPermission(string targettedPermission) =>
                   targettedPermission.Contains("individualIdIfSourceContains:");

        public static bool IsPolicySourceEqualsPermission(string targettedPermission) =>
            targettedPermission.Contains("policyIdIfSourceEquals=");
    }
}
