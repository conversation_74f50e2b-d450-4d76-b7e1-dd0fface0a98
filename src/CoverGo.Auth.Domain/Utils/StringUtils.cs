﻿using System.Collections.Generic;

namespace CoverGo.Auth.Domain.Utils;

public static class StringUtils
{
    public static string ExtractSourceId(string value)
    {
        return value
            .Substring(value.IndexOf(":") + 1)
            .Replace(" ", "");
    }

    public static IEnumerable<string> ExtractSourceIds(string value)
    {
        var str = value.Substring(value.IndexOf("=") + 1).Replace(" ", "");

        return str.Remove(str.Length - 1).Split(',');
    }
}