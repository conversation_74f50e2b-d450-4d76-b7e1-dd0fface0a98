﻿using Microsoft.AspNetCore.Identity;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Utils
{
    public class UsernameAsPasswordValidator<TUser> : IPasswordValidator<TUser>
        where TUser : MongoLoginDao
    {
        public Task<IdentityResult> ValidateAsync(UserManager<TUser> manager, TUser user, string password)
        {
            if (!password.Any(c => char.IsLetter(c)))
            {
                return Task.FromResult(IdentityResult.Failed(new IdentityError
                {
                    Code = "RequireLetter",
                    Description = "Your password must contain at least one letter"
                }));
            }
            return Task.FromResult(IdentityResult.Success);
        }
    }
}
