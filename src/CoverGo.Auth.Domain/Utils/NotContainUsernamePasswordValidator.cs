﻿using Microsoft.AspNetCore.Identity;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Auth.Domain.Utils
{
    public class NotContainUsernamePasswordValidator<TUser> : IPasswordValidator<TUser>
        where TUser : MongoLogin<PERSON>ao
    {
        public Task<IdentityResult> Validate<PERSON><PERSON>(UserManager<TUser> manager, TUser user, string password)
        {
            var emailPart = user?.Email?.Split('@')[0]?.ToUpper();

            if ((!string.IsNullOrEmpty(user?.UserName) && password.ToUpper().Contains(user.UserName.ToUpper())) || (!string.IsNullOrEmpty(emailPart) && password.ToUpper().Contains(emailPart)))
            {
                return Task.FromResult(IdentityResult.Failed(new IdentityError
                {
                    Code = "NotContainUsername",
                    Description = "Your password must not contain username or email"
                }));
            }
            return Task.FromResult(IdentityResult.Success);
        }
    }
}
