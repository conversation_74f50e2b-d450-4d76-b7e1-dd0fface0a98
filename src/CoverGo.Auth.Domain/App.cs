﻿using CoverGo.DomainUtils;
using IdentityServer4.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;

namespace CoverGo.Auth.Domain
{
    public class App : SystemObject
    {
        public string AppId { get; set; }
        public string AppName { get; set; }
        public List<string> RedirectUris { get; set; }
        public string Email { get; set; }
        public string EmailSenderName { get; set; }
        public string OneTimePasswordEmailSubject { get; set; }
        public bool UseNotificationConfig { get; set; }
        public int AccessTokenLifetime { get; set; }
        public bool Requires2FA { get; set; }
        public UrlRouting UrlRouting { get; set; }
        public int AbsoluteRefreshTokenLifetime { get; set; }
        public AccessTokenType AccessTokenType { get; set; }
        public bool AllowAccessTokensViaBrowser { get; set; }
        public ICollection<string> AllowedCorsOrigins { get; set; }
        public ICollection<string> AllowedGrantTypes { get; set; }
        public bool AllowPlainTextPkce { get; set; }
        public ICollection<string> AllowedScopes { get; set; }
        public bool AllowOfflineAccess { get; set; }
        public bool AllowRememberConsent { get; set; }
        public bool AlwaysIncludeUserClaimsInIdToken { get; set; }
        public bool AlwaysSendClientClaims { get; set; }
        public int AuthorizationCodeLifetime { get; set; }
        public bool UpdateAccessTokenClaimsOnRefresh { get; set; }
        public bool BackChannelLogoutSessionRequired { get; set; }
        public string BackChannelLogoutUri { get; set; }
        public ICollection<Claim> Claims { get; set; }
        public string ClientClaimsPrefix { get; set; }
        public ICollection<Secret> ClientSecrets { get; set; }
        public string ClientUri { get; set; }
        public int? ConsentLifetime { get; set; }
        public string Description { get; set; }
        public int DeviceCodeLifetime { get; set; }
        public bool Enabled { get; set; }
        public bool EnableLocalLogin { get; set; }
        public bool FrontChannelLogoutSessionRequired { get; set; }
        public string FrontChannelLogoutUri { get; set; }
        public ICollection<string> IdentityProviderRestrictions { get; set; }
        public int IdentityTokenLifetime { get; set; }
        public bool IncludeJwtId { get; set; }
        public string LogoUri { get; set; }
        public string PairWiseSubjectSalt { get; set; }
        public ICollection<string> PostLogoutRedirectUris { get; set; }
        public IDictionary<string, string> Properties { get; set; }
        public string ProtocolType { get; set; }
        public TokenExpiration RefreshTokenExpiration { get; set; }
        public TokenUsage RefreshTokenUsage { get; set; }
        public bool RequireClientSecret { get; set; }
        public bool RequireConsent { get; set; }
        public bool RequirePkce { get; set; }
        public int SlidingRefreshTokenLifetime { get; set; }
        public string UserCodeType { get; set; }
        public int? UserSsoLifetime { get; set; }
        public TimeSpan? EmailConfirmationTokenLifespan { get; set; }
        public TimeSpan? DataProtectionTokenLifespan { get; set; }
        public TimeSpan? PasswordExpiryLifespan { get; set; }
        public string DefaultTimeZone { get; set; }
        public bool ActivationTokenExpiryDisabled { get; set; }
        public bool RequiresEmail2FA { get; set; }
        public string AppConfig { get; set;}
        public ForgotPasswordEmailSettings? ForgotPasswordEmailSettings { get; set; }
    }

    public class ForgotPasswordEmailSettings
    {
        public string From { get; set; }
        public string FromName { get; set; }
        public string Subject { get; set; }
        public string TemplateId { get; set; }
        public string Link { get; set; }
    }

    public class UrlRouting
    {
        public string Url { get; set; }
        public string RegexPattern { get; set; }
        public int Order { get; set; }
    }


    public class CreateAppCommand
    {
        public string AppId { get; set; }
        public string AppName { get; set; }
        public List<string> RedirectUris { get; set; }
        public string Email { get; set; }
        public string EmailSenderName { get; set; }
        public bool UseNotificationConfig { get; set; }
        public int? AccessTokenLifetime { get; set; }
        public int? AbsoluteRefreshTokenLifetime { get; set; }
        public int? SlidingRefreshTokenLifetime { get; set; }
        public bool Requires2FA { get; set; }
        public UrlRouting UrlRouting { get; set; }
        public string CreatedById { get; set; }
        public TimeSpan? EmailConfirmationTokenLifespan { get; set; }
        public TimeSpan? DataProtectionTokenLifespan { get; set; }
        public string DefaultTimeZone { get; set; }
        public bool ActivationTokenExpiryDisabled { get; set; }
        public bool RequiresEmail2FA { get; set; }
        public string AppConfig { get; set; }
        public ForgotPasswordEmailSettings? ForgotPasswordEmailSettings { get; set; }
    }

    public class UrlRoutingToUpdate
    {
        public string Url { get; set; }
        public bool IsUrlChanged { get; set; }
        public string RegexPattern { get; set; }
        public bool IsRegexPatternChanged { get; set; }
        public int Order { get; set; }
        public bool IsOrderChanged { get; set; }
    }

    public class UpdateAppCommand
    {
        public string AppName { get; set; }
        public bool IsAppNameChanged { get; set; }
        public List<string> RedirectUris { get; set; }
        public bool IsRedirectUrisChanged { get; set; }
        public string Email { get; set; }
        public bool IsEmailChanged { get; set; }
        public string EmailSenderName { get; set; }
        public bool IsEmailSenderNameChanged { get; set; }
        public int? AccessTokenLifetime { get; set; }
        public bool IsAccessTokenLifetimeChanged { get; set; }
        public bool IsEmailConfirmationTokenLifespanChanged { get; set; }
        public bool IsDataProtectionTokenLifespanChanged { get; set; }
        public bool? Requires2FA { get; set; }
        public bool IsRequires2FAChanged { get; set; }
        public UrlRoutingToUpdate UrlRouting { get; set; }
        public bool IsUrlRoutingChanged { get; set; }
        public bool? UseNotificationConfig { get; set; }
        public bool IsUseNotificationConfigChanged { get; set; }
        public TimeSpan? EmailConfirmationTokenLifespan { get; set; }
        public TimeSpan? DataProtectionTokenLifespan { get; set; }
        public int? AbsoluteRefreshTokenLifetime { get; set; }
        public bool IsAbsoluteRefreshTokenLifetimeChanged { get; set; }
        public int? SlidingRefreshTokenLifetime { get; set; }
        public bool IsSlidingRefreshTokenLifetimeChanged { get; set; }
        public string ModifiedById { get; set; }
        public string DefaultTimeZone { get; set; }
        public bool IsDefaultTimeZoneChanged { get; set; }

        public bool ActivationTokenExpiryDisabled { get; set; }
        public bool IsActivationTokenExpiryDisabledChanged { get; set; }
        public bool RequiresEmail2FA { get; set; }
        public bool IsRequiresEmail2FAChanged { get; set; }
        public string AppConfig { get; set; }
        public bool IsAppConfigChanged { get; set; }
        public ForgotPasswordEmailSettingsToUpdate? ForgotPasswordEmailSettings { get; set; }
        public bool IsForgotPasswordEmailSettingsChanged { get; set; }
    }

    public class ForgotPasswordEmailSettingsToUpdate
    {
        public string From { get; set; }
        public bool IsFromChanged { get; set; }
        public string FromName { get; set; }
        public bool IsFromNameChanged { get; set; }
        public string Subject { get; set; }
        public bool IsSubjectChanged { get; set; }
        public string TemplateId { get; set; }
        public bool IsTemplateIdChanged { get; set; }
        public string Link { get; set; }
        public bool IsLinkChanged { get; set; }
    }

    public class TenantIdAndAppId
    {
        public string TenantId { get; set; }
        public string AppId { get; set; }
    }

    public static class ClaimExtensions
    {
        public static IEnumerable<Claim> GetClaims(string tenantId, string selectedAppId, MongoLoginDao login)
        {
            var claims = login.Claims.Select(claim => claim.ToSecurityClaim()).ToList();
            claims.Add(new Claim("tenantId", tenantId));
            claims.Add(new Claim("appId", selectedAppId));

            if (login.EntityId != null)
                claims.Add(new Claim("entityId", login.EntityId));
            if (login.EntityType != null)
                claims.Add(new Claim("entityType", login.EntityType));

            if (login.TwoFactorEnabled)
                claims.Add(new Claim("amr", "mfa"));
            else
                claims.Add(new Claim("amr", "pwd"));

            return claims.DistinctBy(c => new { c.Type, c.Value });
        }

        public static IEnumerable<Claim> ToClaims(this IDictionary<string, string> claims) =>
            claims.Select(item => new Claim(item.Key, item.Value)).ToArray();
    }
}
