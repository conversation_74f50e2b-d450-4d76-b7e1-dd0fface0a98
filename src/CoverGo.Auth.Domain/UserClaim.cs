using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Reflection;

namespace CoverGo.Auth.Domain
{
    [SuppressMessage("Usage", "CA2211:Non-constant fields should not be visible")]
    public sealed class UserClaim
    {
        string Name { get; }

        public static readonly UserClaim ResendConfirmationEmail = new("resendConfirmationEmail");
        public static readonly UserClaim ReadLogins = new("readLogins");
        public static readonly UserClaim WriteLogins = new("writeLogins");
        public static readonly UserClaim WriteTargettedPermissions = new("writeTargettedPermissions");
        public static readonly UserClaim ReadTargettedPermissions = new("readTargettedPermissions");
        public static readonly UserClaim WritePermissionGroups = new("writePermissionGroups");
        public static readonly UserClaim CreatePermissionGroups = new("createPermissionGroups");
        public static readonly UserClaim UpdatePermissionGroups = new("updatePermissionGroups");
        public static readonly UserClaim DeletePermissionGroups = new("deletePermissionGroups");
        public static readonly UserClaim ReadPermissionGroups = new("readPermissionGroups");
        public static readonly UserClaim ReadPermissions = new("readPermissions");
        public static readonly UserClaim ReadCompanies = new("readCompanies");
        public static readonly UserClaim ReadIndividuals = new("readIndividuals");
        public static readonly UserClaim ReadInternals = new("readInternals");
        public static readonly UserClaim ReadObjects = new("readObjects");
        public static readonly UserClaim ReadOrganizations = new("readOrganizations");
        public static readonly UserClaim WriteCompanies = new("writeCompanies");
        public static readonly UserClaim CreateCompanies = new("createCompanies");
        public static readonly UserClaim UpdateCompanies = new("updateCompanies");
        public static readonly UserClaim DeleteCompanies = new("deleteCompanies");
        public static readonly UserClaim WriteIndividuals = new("writeIndividuals");
        public static readonly UserClaim CreateIndividuals = new("createIndividuals");
        public static readonly UserClaim UpdateIndividuals = new("updateIndividuals");
        public static readonly UserClaim DeleteIndividuals = new("deleteIndividuals");
        public static readonly UserClaim WriteInternals = new("writeInternals");
        public static readonly UserClaim WriteObjects = new("writeObjects");
        public static readonly UserClaim WriteOrganizations = new("writeOrganizations");
        public static readonly UserClaim CreateOrganizations = new("createOrganizations");
        public static readonly UserClaim UpdateOrganizations = new("updateOrganizations");
        public static readonly UserClaim DeleteOrganizations = new("deleteOrganizations");
        public static readonly UserClaim ReadIndividualAddresses = new("readIndividualAddresses");
        public static readonly UserClaim ReadIndividualIdentities = new("readIndividualIdentities");
        public static readonly UserClaim ReadIndividualContacts = new("readIndividualContacts");
        public static readonly UserClaim ReadIndividualFacts = new("readIndividualFacts");
        public static readonly UserClaim ReadIndividualNotes = new("readIndividualNotes");
        public static readonly UserClaim ReadIndividualAttachments = new("readIndividualAttachments");
        public static readonly UserClaim WriteIndividualAddresses = new("writeIndividualAddresses");
        public static readonly UserClaim WriteIndividualIdentities = new("writeIndividualIdentities");
        public static readonly UserClaim WriteIndividualContacts = new("writeIndividualContacts");
        public static readonly UserClaim WriteIndividualFacts = new("writeIndividualFacts");
        public static readonly UserClaim WriteIndividualNotes = new("writeIndividualNotes");
        public static readonly UserClaim WriteIndividualAttachments = new("writeIndividualAttachments");
        public static readonly UserClaim ReadCompanyAddresses = new("readCompanyAddresses");
        public static readonly UserClaim ReadCompanyIdentities = new("readCompanyIdentities");
        public static readonly UserClaim ReadCompanyContacts = new("readCompanyContacts");
        public static readonly UserClaim ReadCompanyFacts = new("readCompanyFacts");
        public static readonly UserClaim ReadCompanyNotes = new("readCompanyNotes");
        public static readonly UserClaim ReadCompanyAttachments = new("readCompanyAttachments");
        public static readonly UserClaim WriteCompanyAddresses = new("writeCompanyAddresses");
        public static readonly UserClaim WriteCompanyIdentities = new("writeCompanyIdentities");
        public static readonly UserClaim WriteCompanyContacts = new("writeCompanyContacts");
        public static readonly UserClaim WriteCompanyFacts = new("writeCompanyFacts");
        public static readonly UserClaim WriteCompanyNotes = new("writeCompanyNotes");
        public static readonly UserClaim WriteCompanyAttachments = new("writeCompanyAttachments");
        public static readonly UserClaim ReadInternalAddresses = new("readInternalAddresses");
        public static readonly UserClaim ReadInternalIdentities = new("readInternalIdentities");
        public static readonly UserClaim ReadInternalContacts = new("readInternalContacts");
        public static readonly UserClaim ReadInternalFacts = new("readInternalFacts");
        public static readonly UserClaim ReadInternalNotes = new("readInternalNotes");
        public static readonly UserClaim ReadInternalAttachments = new("readInternalAttachments");
        public static readonly UserClaim WriteInternalAddresses = new("writeInternalAddresses");
        public static readonly UserClaim WriteInternalIdentities = new("writeInternalIdentities");
        public static readonly UserClaim WriteInternalContacts = new("writeInternalContacts");
        public static readonly UserClaim WriteInternalFacts = new("writeInternalFacts");
        public static readonly UserClaim WriteInternalNotes = new("writeInternalNotes");
        public static readonly UserClaim WriteInternalAttachments = new("writeInternalAttachments");
        public static readonly UserClaim ReadObjectAddresses = new("readObjectAddresses");
        public static readonly UserClaim ReadObjectIdentities = new("readObjectIdentities");
        public static readonly UserClaim ReadObjectContacts = new("readObjectContacts");
        public static readonly UserClaim ReadObjectFacts = new("readObjectFacts");
        public static readonly UserClaim ReadObjectNotes = new("readObjectNotes");
        public static readonly UserClaim ReadObjectAttachments = new("readObjectAttachments");
        public static readonly UserClaim WriteObjectAddresses = new("writeObjectAddresses");
        public static readonly UserClaim WriteObjectIdentities = new("writeObjectIdentities");
        public static readonly UserClaim WriteObjectContacts = new("writeObjectContacts");
        public static readonly UserClaim WriteObjectFacts = new("writeObjectFacts");
        public static readonly UserClaim WriteObjectNotes = new("writeObjectNotes");
        public static readonly UserClaim WriteObjectAttachments = new("writeObjectAttachments");
        public static readonly UserClaim ReadOrganizationAddresses = new("readOrganizationAddresses");
        public static readonly UserClaim ReadOrganizationIdentities = new("readOrganizationIdentities");
        public static readonly UserClaim ReadOrganizationContacts = new("readOrganizationContacts");
        public static readonly UserClaim ReadOrganizationFacts = new("readOrganizationFacts");
        public static readonly UserClaim ReadOrganizationNotes = new("readOrganizationNotes");
        public static readonly UserClaim ReadOrganizationAttachments = new("readOrganizationAttachments");
        public static readonly UserClaim WriteOrganizationAddresses = new("writeOrganizationAddresses");
        public static readonly UserClaim WriteOrganizationIdentities = new("writeOrganizationIdentities");
        public static readonly UserClaim WriteOrganizationContacts = new("writeOrganizationContacts");
        public static readonly UserClaim WriteOrganizationFacts = new("writeOrganizationFacts");
        public static readonly UserClaim WriteOrganizationNotes = new("writeOrganizationNotes");
        public static readonly UserClaim WriteOrganizationAttachments = new("writeOrganizationAttachments");
        public static readonly UserClaim ReadPolicies = new("readPolicies");
        public static readonly UserClaim WritePolicies = new("writePolicies");
        public static readonly UserClaim CreatePolicies = new("createPolicies");
        public static readonly UserClaim UpdatePolicies = new("updatePolicies");
        public static readonly UserClaim DeletePolicies = new("deletePolicies");
        public static readonly UserClaim CancelPolicies = new("cancelPolicies");
        public static readonly UserClaim WriteMemberMovementsDraft = new("writeMemberMovements:draft");
        public static readonly UserClaim WriteMemberMovementsSubmit = new("writeMemberMovements:submit");
        public static readonly UserClaim ReadMemberMovementsTerminationReasons = new("readMemberMovements:terminationReasons");
        public static readonly UserClaim WriteMemberMovementsTerminationReasons = new("writeMemberMovements:terminationReasons");
        public static readonly UserClaim ReadQuotes = new("readQuotes");
        public static readonly UserClaim WriteQuotes = new("writeQuotes");
        public static readonly UserClaim ReadPolicyNotes = new("readPolicyNotes");
        public static readonly UserClaim WritePolicyNotes = new("writePolicyNotes");
        public static readonly UserClaim WritePolicyAttachments = new("writePolicyAttachments");
        public static readonly UserClaim CreatePolicyAttachments = new("createPolicyAttachments");
        public static readonly UserClaim DeletePolicyAttachments = new("deletePolicyAttachments");
        public static readonly UserClaim ReadPolicyAttachments = new("readPolicyAttachments");
        public static readonly UserClaim ReadProducts = new("readProducts");
        public static readonly UserClaim WriteProducts = new("writeProducts");
        public static readonly UserClaim CreateProducts = new("createProducts");
        public static readonly UserClaim UpdateProducts = new("updateProducts");
        public static readonly UserClaim DeleteProducts = new("deleteProducts");
        public static readonly UserClaim WriteLinks = new("writeLinks");
        public static readonly UserClaim CreatorRights = new("creatorRights");
        public static readonly UserClaim IssuePolicies = new("issuePolicies");
        public static readonly UserClaim SendNotifications = new("sendNotifications");
        public static readonly UserClaim WriteCommissionRules = new("writeCommissionRules");
        public static readonly UserClaim ReadCommissionRules = new("readCommissionRules");
        public static readonly UserClaim ReadInsurers = new("readInsurers");
        public static readonly UserClaim WriteInsurers = new("writeInsurers");
        public static readonly UserClaim ReadProductTypes = new("readProductTypes");
        public static readonly UserClaim WriteProductTypes = new("writeProductTypes");
        public static readonly UserClaim ReadTransactions = new("readTransactions");
        public static readonly UserClaim WriteTransactions = new("writeTransactions");
        public static readonly UserClaim CreateTransactions = new("createTransactions");
        public static readonly UserClaim UpdateTransactions = new("updateTransactions");
        public static readonly UserClaim DeleteTransactions = new("deleteTransactions");
        public static readonly UserClaim ReadOffers = new("readOffers");
        public static readonly UserClaim WriteOffers = new("writeOffers");
        public static readonly UserClaim CreateOffers = new("createOffers");
        public static readonly UserClaim UpdateOffers = new("updateOffers");
        public static readonly UserClaim DeleteOffers = new("deleteOffers");
        public static readonly UserClaim OverrideOffers = new("overrideOffers");
        public static readonly UserClaim ClientId = new("clientId");
        public static readonly UserClaim ReadClaims = new("readClaims");
        public static readonly UserClaim WriteClaims = new("writeClaims");
        public static readonly UserClaim CreateClaims = new("createClaims");
        public static readonly UserClaim UpdateClaims = new("updateClaims");
        public static readonly UserClaim DeleteClaims = new("deleteClaims");
        public static readonly UserClaim RejectClaims = new("rejectClaims");
        public static readonly UserClaim ReverseClaims = new("reverseClaims");
        public static readonly UserClaim ReadFiles = new("readFiles");
        public static readonly UserClaim WriteFiles = new("writeFiles");
        public static readonly UserClaim LockFiles = new("lockFiles");
        public static readonly UserClaim ReadFileSystemConfigs = new("readFileSystemConfigs");
        public static readonly UserClaim WriteFileSystemConfigs = new("writeFileSystemConfigs");
        public static readonly UserClaim CreateFileSystemConfigs = new("createFileSystemConfigs");
        public static readonly UserClaim UpdateFileSystemConfigs = new("updateFileSystemConfigs");
        public static readonly UserClaim DeleteFileSystemConfigs = new("deleteFileSystemConfigs");
        public static readonly UserClaim ReadCms = new("readCms");
        public static readonly UserClaim WriteCms = new("writeCms");
        public static readonly UserClaim ReadTemplates = new("readTemplates");
        public static readonly UserClaim WriteTemplates = new("writeTemplates");
        public static readonly UserClaim CreateTemplates = new("createTemplates");
        public static readonly UserClaim UpdateTemplates = new("updateTemplates");
        public static readonly UserClaim DeleteTemplates = new("deleteTemplates");
        public static readonly UserClaim ReadClaimNotes = new("readClaimNotes");
        public static readonly UserClaim WriteClaimNotes = new("writeClaimNotes");
        public static readonly UserClaim CreateClaimNotes = new("createClaimNotes");
        public static readonly UserClaim UpdateClaimNotes = new("updateClaimNotes");
        public static readonly UserClaim DeleteClaimNotes = new("deleteClaimNotes");
        public static readonly UserClaim WriteClaimWhenStatusApproved = new("writeClaimWhenStatusApproved");
        public static readonly UserClaim ApproveClaim = new("approveClaim");
        public static readonly UserClaim ReadCases = new("readCases");
        public static readonly UserClaim WriteCases = new("writeCases");
        public static readonly UserClaim CreateCases = new("createCases");
        public static readonly UserClaim UpdateCases = new("updateCases");
        public static readonly UserClaim DeleteCases = new("deleteCases");
        public static readonly UserClaim ReadProposals = new("readProposals");
        public static readonly UserClaim WriteProposals = new("writeProposals");
        public static readonly UserClaim ReadBinders = new("readBinders");
        public static readonly UserClaim WriteBinders = new("writeBinders");
        public static readonly UserClaim ReadEndorsements = new("readEndorsements");
        public static readonly UserClaim WriteEndorsements = new("writeEndorsements");
        public static readonly UserClaim UpdateIssuedPolicies = new("updateIssuedPolicies");
        public static readonly UserClaim IssueProposals = new("issueProposals");
        public static readonly UserClaim ReadApps = new("readApps");
        public static readonly UserClaim WriteApps = new("writeApps");
        public static readonly UserClaim CreateApps = new("createApps");
        public static readonly UserClaim UpdateApps = new("updateApps");
        public static readonly UserClaim DeleteApps = new("deleteApps");
        public static readonly UserClaim InviteEntityToLogin = new("inviteEntityToLogin");
        public static readonly UserClaim WriteCmsConfigs = new("writeCmsConfigs");
        public static readonly UserClaim ReadCmsConfigs = new("readCmsConfigs");
        public static readonly UserClaim ReadPaymentMethods = new("readPaymentMethods");
        public static readonly UserClaim WritePaymentMethods = new("writePaymentMethods");
        public static readonly UserClaim ReadPlans = new("readPlans");
        public static readonly UserClaim WritePlans = new("writePlans");
        public static readonly UserClaim ReadSubscriptions = new("readSubscriptions");
        public static readonly UserClaim WriteSubscriptions = new("writeSubscriptions");
        public static readonly UserClaim ReadCourses = new("readCourses");
        public static readonly UserClaim WriteCourses = new("writeCourses");
        public static readonly UserClaim ReadCourseProgressions = new("readCourseProgressions");
        public static readonly UserClaim WriteCourseProgressions = new("writeCourseProgressions");
        public static readonly UserClaim ReadCorrectAnswers = new("readCorrectAnswers");
        public static readonly UserClaim ReadProductConfigs = new("readProductConfigs");
        public static readonly UserClaim WriteProductConfigs = new("writeProductConfigs");
        public static readonly UserClaim TransferPermissions = new("transferPermissions");
        public static readonly UserClaim ReadAchievements = new("readAchievements");
        public static readonly UserClaim WriteAchievements = new("writeAchievements");
        public static readonly UserClaim ReadAchievementTypes = new("readAchievementTypes");
        public static readonly UserClaim WriteAchievementTypes = new("writeAchievementTypes");
        public static readonly UserClaim AssignPermissionGroup = new("assignPermissionGroup");
        public static readonly UserClaim UnassignPermissionGroup = new("unassignPermissionGroup");
        public static readonly UserClaim BatchIntegrate = new("batchIntegrate");
        public static readonly UserClaim WriteL10Ns = new("writeL10ns");
        public static readonly UserClaim CreateL10Ns = new("createL10ns");
        public static readonly UserClaim DeleteL10Ns = new("deleteL10ns");
        public static readonly UserClaim ReadNotificationSubscriptions = new("readNotificationSubscriptions");
        public static readonly UserClaim WriteNotificationSubscriptions = new("writeNotificationSubscriptions");
        public static readonly UserClaim WriteFileExtensions = new("writeFileExtensions");
        public static readonly UserClaim ReadNotifications = new("readNotifications");
        public static readonly UserClaim WriteReviews = new("writeReviews");
        public static readonly UserClaim UpdateLoginLockout = new("updateLoginLockout");
        public static readonly UserClaim ReadNotificationConfigs = new("readNotificationConfigs");
        public static readonly UserClaim WriteNotificationConfigs = new("writeNotificationConfigs");
        public static readonly UserClaim ReadGoPs = new("readGOPs");
        public static readonly UserClaim WriteGoPs = new("writeGOPs");
        public static readonly UserClaim CreateGoPs = new("createGOPs");
        public static readonly UserClaim UpdateGoPs = new("updateGOPs");
        public static readonly UserClaim DeleteGoPs = new("deleteGOPs");
        public static readonly UserClaim ReadGopNotes = new("readGOPNotes");
        public static readonly UserClaim WriteGopNotes = new("writeGOPNotes");
        public static readonly UserClaim CreateGopNotes = new("createGOPNotes");
        public static readonly UserClaim UpdateGopNotes = new("updateGOPNotes");
        public static readonly UserClaim DeleteGopNotes = new("deleteGOPNotes");
        public static readonly UserClaim ReadGopAttachments = new("readGOPAttachments");
        public static readonly UserClaim WriteGopAttachments = new("writeGOPAttachments");
        public static readonly UserClaim CreateGopAttachments = new("createGOPAttachments");
        public static readonly UserClaim UpdateGopAttachments = new("updateGOPAttachments");
        public static readonly UserClaim WriteClaimAttachments = new("writeClaimAttachments");
        public static readonly UserClaim CreateClaimAttachments = new("createClaimAttachments");
        public static readonly UserClaim UpdateClaimAttachments = new("updateClaimAttachments");
        public static readonly UserClaim ReadPermissionSchemas = new("readPermissionSchemas");
        public static readonly UserClaim WritePermissionSchemas = new("writePermissionSchemas");
        public static readonly UserClaim WriteServiceItems = new("writeServiceItems");
        public static readonly UserClaim ReadServiceItems = new("readServiceItems");
        public static readonly UserClaim WritePanelProviderTiers = new("writePanelProviderTiers");
        public static readonly UserClaim CreatePanelProviderTiers = new("createPanelProviderTiers");
        public static readonly UserClaim UpdatePanelProviderTiers = new("updatePanelProviderTiers");
        public static readonly UserClaim DeletePanelProviderTiers = new("deletePanelProviderTiers");
        public static readonly UserClaim ReadPanelProviderTiers = new("readPanelProviderTiers");
        public static readonly UserClaim WriteTransactionAttachments = new("writeTransactionAttachments");
        public static readonly UserClaim ReadJobSchedules = new("readJobSchedules");
        public static readonly UserClaim WriteJobSchedules = new("writeJobSchedules");
        public static readonly UserClaim ReadBenefitDefinitions = new("readBenefitDefinitions");
        public static readonly UserClaim WriteBenefitDefinitions = new("writeBenefitDefinitions");
        public static readonly UserClaim CreateBenefitDefinitions = new("createBenefitDefinitions");
        public static readonly UserClaim UpdateBenefitDefinitions = new("updateBenefitDefinitions");
        public static readonly UserClaim DeleteBenefitDefinitions = new("deleteBenefitDefinitions");
        public static readonly UserClaim ReadBenefitDefinitionTypes = new("readBenefitDefinitionTypes");
        public static readonly UserClaim WriteBenefitDefinitionTypes = new("writeBenefitDefinitionTypes");
        public static readonly UserClaim CreateBenefitDefinitionTypes = new("createBenefitDefinitionTypes");
        public static readonly UserClaim UpdateBenefitDefinitionTypes = new("updateBenefitDefinitionTypes");
        public static readonly UserClaim DeleteBenefitDefinitionTypes = new("deleteBenefitDefinitionTypes");
        public static readonly UserClaim ReadDataSchemas = new("readDataSchemas");
        public static readonly UserClaim WriteDataSchemas = new("writeDataSchemas");
        public static readonly UserClaim CreateDataSchemas = new("createDataSchemas");
        public static readonly UserClaim UpdateDataSchemas = new("updateDataSchemas");
        public static readonly UserClaim DeleteDataSchemas = new("deleteDataSchemas");
        public static readonly UserClaim ReadDiagnoses = new("readDiagnoses");
        public static readonly UserClaim WriteDiagnoses = new("writeDiagnoses");
        public static readonly UserClaim ReadDisabilities = new("readDisabilities");
        public static readonly UserClaim WriteDisabilities = new("writeDisabilities");
        public static readonly UserClaim CreateDisabilities = new("createDisabilities");
        public static readonly UserClaim UpdateDisabilities = new("updateDisabilities");
        public static readonly UserClaim DeleteDisabilities = new("deleteDisabilities");
        public static readonly UserClaim ReadIndividualDisabilities = new("readIndividualDisabilities");
        public static readonly UserClaim WriteIndividualDisabilities = new("writeIndividualDisabilities");
        public static readonly UserClaim CreateIndividualDisabilities = new("createIndividualDisabilities");
        public static readonly UserClaim UpdateIndividualDisabilities = new("updateIndividualDisabilities");
        public static readonly UserClaim ReadTreatments = new("readTreatments");
        public static readonly UserClaim WriteTreatments = new("writeTreatments");
        public static readonly UserClaim CreateTreatments = new("createTreatments");
        public static readonly UserClaim UpdateTreatments = new("updateTreatments");
        public static readonly UserClaim DeleteTreatments = new("deleteTreatments");
        public static readonly UserClaim ReadScripts = new("readScripts");
        public static readonly UserClaim WriteScripts = new("writeScripts");
        public static readonly UserClaim CreateScripts = new("createScripts");
        public static readonly UserClaim UpdateScripts = new("updateScripts");
        public static readonly UserClaim DeleteScripts = new("deleteScripts");
        public static readonly UserClaim ReadClaimReports = new("readClaimReports");
        public static readonly UserClaim WriteClaimReports = new("writeClaimReports");
        public static readonly UserClaim WriteKycStatuses = new("writeKycStatuses");
        public static readonly UserClaim ImportClaims = new("importClaims");
        public static readonly UserClaim ImportPolicies = new("importPolicies");
        public static readonly UserClaim ReadAttachedRules = new("readAttachedRules");
        public static readonly UserClaim WriteAttachedRules = new("writeAttachedRules");
        public static readonly UserClaim ReadDiscountCodes = new("readDiscountCodes");
        public static readonly UserClaim WriteDiscountCodes = new("writeDiscountCodes");
        public static readonly UserClaim ReadClaimInvestigationCases = new("readClaimInvestigationCases");
        public static readonly UserClaim WriteClaimInvestigationCases = new("writeClaimInvestigationCases");
        public static readonly UserClaim WriteUnderwritings = new("writeUnderwritings");
        public static readonly UserClaim ReadNodes = new("readNodes");
        public static readonly UserClaim WriteNodes = new("writeNodes");
        public static readonly UserClaim ReadProductSchemas = new("readProductSchemas");
        public static readonly UserClaim WriteProductSchemas = new("writeProductSchemas");
        public static readonly UserClaim ReadPolicyMembers = new("readPolicyMembers");
        public static readonly UserClaim ReadSSOUsers = new("readSSOUsers");
        public static readonly UserClaim WriteSSOUsers = new("writeSSOUsers");
        public static readonly UserClaim ReadSbus = new("readSbus");
        public static readonly UserClaim WriteSbus = new("writeSbus");
        public static readonly UserClaim ReadMigrations = new("readMigrations");
        public static readonly UserClaim RunMigrations = new("runMigrations");
        public static readonly UserClaim ExportProducts = new("exportProducts");
        public static readonly UserClaim ImportProducts = new("importProducts");
        public static readonly UserClaim CreateSSOConfig = new("createSSOConfig");
        public static readonly UserClaim DeleteSSOConfig = new("deleteSSOConfig");
        public static readonly UserClaim DownloadExactInvoices = new("downloadExactInvoices");
        public static readonly UserClaim CreateRegularClaim = new("createRegularClaim");
        public static readonly UserClaim CreateAndAcceptRegularClaim = new("createAndAcceptRegularClaim");
        public static readonly UserClaim AcceptRegularClaim = new("acceptRegularClaim");
        public static readonly UserClaim UpdateRegularClaim = new("updateRegularClaim");
        public static readonly UserClaim ApproveRegularClaim = new("approveRegularClaim");
        public static readonly UserClaim DeclineRegularClaim = new("declineRegularClaim");
        public static readonly UserClaim UpdateApprovedRegularClaim = new("updateApprovedRegularClaim");
        public static readonly UserClaim TerminateRegularClaim = new("terminateRegularClaim");
        public static readonly UserClaim ApproveUpdatedRegularClaim = new("approveUpdatedRegularClaim");
        public static readonly UserClaim DeclineUpdatesToApprovedRegularClaim = new("declineUpdatesToApprovedRegularClaim");
        public static readonly UserClaim ReadRegularClaim = new("readRegularClaim");
        public static readonly UserClaim WriteAgentsClaim = new("writeAgents");
        public static readonly UserClaim WriteSalesChannelsClaim = new("writeSalesChannels");
        public static readonly UserClaim AccessRestrictedContent = new("accessRestrictedContent");
        public static readonly UserClaim AddAccessRestrictedContentPermission = new("addAccessRestrictedContentPermission");
        public static readonly UserClaim RemoveAccessRestrictedContentPermission = new("removeAccessRestrictedContentPermission");
        public static readonly UserClaim CreateReceipt = new("createReceipt");
        public static readonly UserClaim ReadReceipt = new("readReceipt");
        public static readonly UserClaim ReadTasks = new("readTasks");
        public static readonly UserClaim ReadTasksByLoginId = new("readTasksByLoginId");
        public static readonly UserClaim ReadPayments = new("readPayments");
        public static readonly UserClaim CreatePayments = new("createPayments");

        UserClaim(string name) => Name = name;

        public override string ToString() => this.Name;

        public static ICollection<string> List()
        {
            string[] claims = typeof(UserClaim)
                .GetFields(BindingFlags.Public | BindingFlags.Static)
                .Select(field => ((UserClaim)field.GetValue(null))?.ToString())
                .Where(f => f != null)
                .ToArray();

            return claims;
        }
    }
}
