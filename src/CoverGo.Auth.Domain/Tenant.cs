﻿using System.Collections.Generic;

namespace CoverGo.Auth.Domain
{
    public class Tenant
    {
        public string Id { get; set; }
    }

    public class TenantSettings
    {
        public string TenantId { get; set; }
        public IEnumerable<string> Hosts { get; set; }
        public IEnumerable<SSOConfig> SSOConfigs { get; set; }
    }

    public class CreateTenantCommand
    {
        public CreateAdminCommand AdminSettings { get; set; }
    }

    public class CreateAdminCommand
    {
        public string Username { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
    }
}
