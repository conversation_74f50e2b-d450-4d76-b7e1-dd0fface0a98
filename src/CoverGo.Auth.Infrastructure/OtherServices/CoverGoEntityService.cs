﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Utils;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.OtherServices
{
    public class CoverGoEntityService : IEntityService
    {
        private readonly HttpClient _client;

        public CoverGoEntityService(HttpClient client)
        {
            _client = client;
        }

        public Task<IEnumerable<string>> GenericQueryIdsAsync(string tenantId, EntityWhere @where, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<string>, EntityWhere>($"{tenantId}/api/v1/entities/genericQueryIds", @where, cancellationToken);

        public Task<IEnumerable<Entity>> GenericQueryAsync(string tenantId, EntityWhere @where, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<Entity>, EntityWhere>($"{tenantId}/api/v1/entities/genericQuery", @where, cancellationToken);

        public Task<IEnumerable<Relationships>> GetRelationshipsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<Relationships>, QueryArguments>($"{tenantId}/api/v1/entities/links/query", queryArguments, cancellationToken);

        public Task<IEnumerable<string>> GetIndividualIdsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<string>, QueryArguments>($"{tenantId}/api/v1/entities/individuals/queryIds", queryArguments, cancellationToken); // To do: add the individuals interface

        public Task<IEnumerable<Individual>> GetIndividualsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<Individual>, QueryArguments>($"{tenantId}/api/v1/entities/individuals/query", queryArguments, cancellationToken);
    }
}
