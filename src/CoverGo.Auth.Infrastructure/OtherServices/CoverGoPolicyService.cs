﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Utils;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.OtherServices
{
    public class CoverGoPolicyService : IPolicyService
    {
        private readonly HttpClient _client;

        public CoverGoPolicyService(HttpClient client)
        {
            _client = client;
        }

        public Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<string>, QueryArguments>($"{tenantId}/api/v1/policies/queryids", queryArguments, cancellationToken);

        public Task<IEnumerable<string>> GetIdsAsync(string tenantId, PolicyWhere @where, CancellationToken cancellationToken) =>
            GetIdsAsync(tenantId, new QueryArguments { Where = @where }, cancellationToken);

        public Task<IEnumerable<Policy>> GetAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<Policy>, QueryArguments>($"{tenantId}/api/v1/policies/query", queryArguments, cancellationToken);

        public Task<IEnumerable<Policy>> GetAsync(string tenantId, PolicyWhere @where, CancellationToken cancellationToken) =>
            GetAsync(tenantId, new QueryArguments { Where = @where }, cancellationToken);

        public Task<IEnumerable<PolicyMember>> GetPolicyMembersAsync(string tenantId, PolicyMembersWhere where, CancellationToken cancellationToken) => 
            _client.GenericGetAsync<IEnumerable<PolicyMember>, PolicyMembersWhere>($"{tenantId}/api/v1/policies/members/query", @where, cancellationToken);
    }
}
