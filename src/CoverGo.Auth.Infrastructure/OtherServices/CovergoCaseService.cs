﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Utils;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.OtherServices
{
    public class CoverGoCaseService : ICaseService
    {
        private readonly HttpClient _client;

        public CoverGoCaseService(HttpClient client)
        {
            _client = client;
        }

        public Task<IEnumerable<Case>> GetAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<Case>, QueryArguments>($"{tenantId}/api/v1/cases/query", queryArguments, cancellationToken);

        public Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<string>, QueryArguments>($"{tenantId}/api/v1/cases/queryids", queryArguments, cancellationToken);

        public Task<IEnumerable<string>> GetProposalIdsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<string>, QueryArguments>($"{tenantId}/api/v1/cases/queryProposalIds", queryArguments, cancellationToken);

        public Task<IEnumerable<string>> GetOfferIdsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<string>, QueryArguments>($"{tenantId}/api/v1/cases/queryOfferIds", queryArguments, cancellationToken);

        public Task<IEnumerable<string>> GetIdsAsync(string tenantId, PolicyWhere @where, CancellationToken cancellationToken) =>
            GetIdsAsync(tenantId, new QueryArguments { Where = @where }, cancellationToken);
    }
}
