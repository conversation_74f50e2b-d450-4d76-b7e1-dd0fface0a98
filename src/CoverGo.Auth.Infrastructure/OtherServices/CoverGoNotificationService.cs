﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Utils;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.OtherServices
{
    public class CoverGoNotificationService : INotificationService
    {
        private readonly HttpClient _client;

        public CoverGoNotificationService(HttpClient client)
        {
            _client = client;
        }

        public Task<Result> SendAsync(string tenantId, SendNotificationCommand command, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<Result, SendNotificationCommand>($"{tenantId}/api/v1/notifications/users", command, cancellationToken);

        public Task<IEnumerable<Notification>> GetNotificationsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
             _client.GenericPostAsync<IEnumerable<Notification>, QueryArguments>($"{tenantId}/api/v1/notifications/query", queryArguments, cancellationToken);

        public Task<IEnumerable<NotificationSubscription>> GetNotificationSubscriptionsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
         _client.GenericPostAsync<IEnumerable<NotificationSubscription>, QueryArguments>($"{tenantId}/api/v1/notifications/subscriptions", queryArguments, cancellationToken);

        public Task<IEnumerable<string>> GetNotificationSubscriptionIdsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
         _client.GenericPostAsync<IEnumerable<string>, QueryArguments>($"{tenantId}/api/v1/notifications/subscriptions/queryids", queryArguments, cancellationToken);
    }
}
