﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.ChannelManagement.Client;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using StrawberryShake;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.OtherServices;

public class CoverGoChannelManagementService : ICoverGoChannelManagementService
{
    private readonly IChannelManagementClient _client;
    private readonly ILogger<CoverGoChannelManagementService> _logger;
    private readonly IHttpContextAccessor _contextAccessor;
    private readonly int _maxPageSize = 50;

    public CoverGoChannelManagementService(
        IChannelManagementClient client, 
        IHttpContextAccessor contextAccessor,
        ILogger<CoverGoChannelManagementService> logger)
    {
        _client = client;
        _contextAccessor = contextAccessor;
        _logger = logger;
    }

    public async Task<IEnumerable<string>> GetIdsAsync(string createdById, CancellationToken cancellationToken = default)
    {
        List<string> ids = new();
        int loaded = 0;
        IOperationResult<IAgentsResult> result;
        do
        {
            result = await _client.Agents.ExecuteAsync(loaded, _maxPageSize,
                new AgentFilterInput { CreatedById = createdById }, null, cancellationToken);
            result.EnsureNoErrors();
            if (result.Data?.Agents?.Items != null) ids.AddRange(result.Data.Agents.Items.Select(x => x.AgentID));

            loaded += _maxPageSize;
        } while (result.Data?.Agents?.PageInfo.HasNextPage ?? false);

        return ids;
    }

    public async Task<IEnumerable<string>> GetTeamMembersLoginIdsAsync(string teamLeadLoginId, CancellationToken cancellationToken = default)
    {
        IOperationResult<ITeamManagerAgentMembersResult> result = await _client.TeamManagerAgentMembers.ExecuteAsync(teamLeadLoginId);
        result.EnsureNoErrors();
        ITeamManagerAgentMembers_Agents_Items foundTeamLead = result.Data.Agents?.Items.FirstOrDefault();
        return foundTeamLead != null ? AgentTeamLeadExtensions.GetAllAgentTeamMemberLoginIds(foundTeamLead): Enumerable.Empty<string>();
    }

    public async Task<Tuple<DateTimeOffset?, DateTimeOffset?>> GetAgentActiveAndTerminationDateTimeAsync(string email, string accessToken, CancellationToken cancellationToken = default)
    {
        try
        {
            _contextAccessor.HttpContext.Request.Headers.Remove("Authorization");
            _contextAccessor.HttpContext.Request.Headers.Add("Authorization", $"Bearer {accessToken}");
            IOperationResult<IAgentsResult> result = await _client.Agents.ExecuteAsync(0, _maxPageSize,
                new AgentFilterInput { MainContactEmail = email }, null, cancellationToken);
            result.EnsureNoErrors();

            if (result.Data?.Agents?.Items != null)
                return Tuple.Create(result?.Data?.Agents?.Items?.FirstOrDefault()?.ActiveFromDateTime, result?.Data?.Agents?.Items?.FirstOrDefault()?.TerminationDateTime);
            return Tuple.Create<DateTimeOffset?, DateTimeOffset?>(null, null);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error getting agent active from date time");
            return Tuple.Create<DateTimeOffset?, DateTimeOffset?>(null, null);
        }
    }
}
