﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Utils;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.OtherServices
{
    public class CoverGoTransactionService : ITransactionService
    {
        private readonly HttpClient _client;

        public CoverGoTransactionService(HttpClient client)
        {
            _client = client;
        }

        public Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<string>, QueryArguments>($"{tenantId}/api/v1/transactions/queryids", queryArguments, cancellationToken);

        public Task<IEnumerable<PaymentMethod>> GetPaymentMethodsAsync(string tenantId, QueryArguments queryArguments, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<IEnumerable<PaymentMethod>, QueryArguments>($"{tenantId}/api/v1/paymentMethods/query", queryArguments, cancellationToken);

        public Task<IEnumerable<PaymentMethod>> GetPaymentMethodsAsync(string tenantId, PaymentMethodWhere @where, CancellationToken cancellationToken) =>
            GetPaymentMethodsAsync(tenantId, new QueryArguments { Where = @where }, cancellationToken);
    }
}
