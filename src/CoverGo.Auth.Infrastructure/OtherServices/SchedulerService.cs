﻿using CoverGo.Auth.Domain.OtherServices;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading;
using System.Threading.Tasks;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CoverGo.Auth.Infrastructure.OtherServices;

public class SchedulerService : ISchedulerService
{
    private readonly HttpClient _httpClient;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public SchedulerService(HttpClient httpClient, IHttpContextAccessor httpContextAccessor)
    {
        _httpClient = httpClient;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> CreateJobScheduleAsync(string tenantId, string appId, JobSchedule jobScheduleInput, CancellationToken cancellationToken)
    {
        var body = new
        {
            Name = jobScheduleInput.Name,
            Description = jobScheduleInput.Description,
            CronExpression = jobScheduleInput.CronExpression,
            IsActive = true,
            CreatedAt = DateTime.Now,
            LastModifiedAt = DateTime.Now,
            CreatedById = jobScheduleInput.AsLoginId,
            LastModifiedById = jobScheduleInput.AsLoginId,
            TenantId = tenantId,
            JobDetail = new
            {
                AsLoginId = jobScheduleInput.AsLoginId,
                AsAppId = appId,
                Type = jobScheduleInput.JobDetail.Type,
                JobId = jobScheduleInput.JobDetail.JobId,
                GraphQlOperationType = jobScheduleInput.JobDetail.GraphQlOperationType,
                GraphQlQuery = jobScheduleInput.JobDetail.GraphQlQuery,
                GraphQlVariables = jobScheduleInput.JobDetail.GraphQlVariables,
                CustomJobVariables = jobScheduleInput.JobDetail.CustomJobVariables
            },
        };

        HttpResponseMessage response = await _httpClient.PostAsJsonAsync($"{tenantId}/api/v1/scheduler/jobSchedules/create", body, cancellationToken);

        if (!response.IsSuccessStatusCode)
            return Result.Failure($"Failed to create job schedule {jobScheduleInput.Name}. {response.ReasonPhrase}");

        return await response.Content.ReadFromJsonAsync<Result>(cancellationToken: cancellationToken);
    }

    public async Task<Result> DeleteJobScheduleAsync(string tenantId, string name, CancellationToken cancellationToken)
    {
        HttpResponseMessage response = await _httpClient.DeleteAsync($"{tenantId}/api/v1/scheduler/jobSchedules/{name}/delete", cancellationToken);

        if (!response.IsSuccessStatusCode)
            return Result.Failure($"Failed to delete job schedule {name}. {response.ReasonPhrase}");

        return await response.Content.ReadFromJsonAsync<Result>(cancellationToken: cancellationToken);
    }

    public async Task<IReadOnlyCollection<JobScheduleDto>> GetJobSchedulesAsync(string tenantId, QueryArguments<JobScheduleWhere> queryArguments, CancellationToken cancellationToken)
    {
        HttpResponseMessage response = await _httpClient.PostAsJsonAsync($"{tenantId}/api/v1/scheduler/jobSchedules/filter", queryArguments, cancellationToken);

        if (!response.IsSuccessStatusCode)
            return new List<JobScheduleDto>();

        return await response.Content.ReadFromJsonAsync<IReadOnlyCollection<JobScheduleDto>>(cancellationToken: cancellationToken);
    }
}
