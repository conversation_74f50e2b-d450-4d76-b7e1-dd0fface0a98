﻿using CoverGo.Auth.Domain.PermissionSchemas;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.JsonUtils;
using CoverGo.MongoUtils;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Schema;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.Adapters.Mongo
{
    public class MongoPermissionSchemaRepository : IPermissionSchemaRepository
    {
        const string DbName = "permissionSchemas";

        public string ProviderId { get; } = "mongoDb";

        public async Task<IReadOnlyCollection<PermissionSchema>> Get(string tenantId, PermissionSchemaWhere where, CancellationToken cancellationToken, OrderBy orderBy = null, int? skip = null, int? first = null)
        {
            IMongoDatabase db = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName);

            IMongoCollection<PermissionSchema> collection = db.GetCollection<PermissionSchema>($"{tenantId}-permissionSchemas");

            IFindFluent<PermissionSchema, PermissionSchema> find = collection.Find(CreateFilter(where));

            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<PermissionSchema>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<PermissionSchema>.Sort.Descending(orderBy.FieldName));

            return await find.Skip(skip).Limit(first).ToListAsync(cancellationToken);
        }

        public Task<long> GetTotalCount(string tenantId, PermissionSchemaWhere where, CancellationToken cancellationToken)
        {
            IMongoDatabase db = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName);

            IMongoCollection<PermissionSchema> collection = db.GetCollection<PermissionSchema>($"{tenantId}-permissionSchemas");

            return collection.Find(CreateFilter(where)).CountDocumentsAsync(cancellationToken);
        }

        FilterDefinition<PermissionSchema> CreateFilter(PermissionSchemaWhere where)
        {
            FilterDefinition<PermissionSchema> result = Builders<PermissionSchema>.Filter.Empty;

            if (where?.Or?.Any() == true)
                return CreateOrFilter(result, where);

            if (where?.And?.Any() == true)
                return CreateAndFilter(result, where);

            return CreateFieldFilter(result, where);
        }

        FilterDefinition<PermissionSchema> CreateOrFilter(
            FilterDefinition<PermissionSchema> filter,
            PermissionSchemaWhere where)
        {
            foreach (PermissionSchemaWhere orFilter in where.Or)
                filter = filter != FilterDefinition<PermissionSchema>.Empty
                    ? filter | CreateFilter(orFilter)
                    : CreateFilter(orFilter);

            return filter;
        }

        FilterDefinition<PermissionSchema> CreateAndFilter(
            FilterDefinition<PermissionSchema> filter,
            PermissionSchemaWhere where)
        {
            foreach (PermissionSchemaWhere andFilter in where.And)
                filter &= CreateFilter(andFilter);

            return filter;
        }

        FilterDefinition<PermissionSchema> CreateFieldFilter(
            FilterDefinition<PermissionSchema> filter,
            PermissionSchemaWhere where)
        {
            if (where == null)
                return filter;
            if (where.Id != null)
                return Builders<PermissionSchema>.Filter.Eq(x => x.Id, where.Id);
            if (where.Id_in != null)
                return Builders<PermissionSchema>.Filter.In(x => x.Id, where.Id_in);
            if (where.Name != null)
                return Builders<PermissionSchema>.Filter.Eq(x => x.Name, where.Name);
            if (where.ObjectType != null)
                return Builders<PermissionSchema>.Filter.Eq(x => x.ObjectType, where.ObjectType);
            if (where.ActionType != null)
                return Builders<PermissionSchema>.Filter.Eq(x => x.ActionType, where.ActionType);
            if (where.Schema != null)
                return where.Schema.Build<PermissionSchema>();
            return filter;
        }

        public async Task<Result<CreatedStatus>> Create(string tenantId, string id, CreatePermissionSchemaCommand command, CancellationToken cancellationToken)
        {
            IMongoDatabase db = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName);

            IMongoCollection<PermissionSchema> collection = db.GetCollection<PermissionSchema>($"{tenantId}-permissionSchemas");
            await collection.Indexes.CreateOneAsync(
                new CreateIndexModel<PermissionSchema>(Builders<PermissionSchema>.IndexKeys.Ascending("Id")), cancellationToken: cancellationToken);

            var permissionSchema = new PermissionSchema
            {
                Id = id,
                Name = command.Name,
                Description = command.Description,
                ObjectType = command.ObjectType,
                ActionType = command.ActionType,
                Schema = string.IsNullOrEmpty(command.Schema) ? null : JToken.Parse(command.Schema),
                StateCondition = command.StateCondition,
                UpdateCondition = command.UpdateCondition,
                LastModifiedById = command.CreatedById,
                LastModifiedAt = DateTime.UtcNow,
                CreatedById = command.CreatedById,
                CreatedAt = DateTime.UtcNow
            };

            await collection.InsertOneAsync(permissionSchema, cancellationToken: cancellationToken);

            return new() { Status = "success", Value = new() { Id = permissionSchema.Id } };
        }

        public async Task<Result> Update(string tenantId, UpdatePermissionSchemaCommand command, CancellationToken cancellationToken)
        {
            IMongoDatabase db = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName);

            IMongoCollection<PermissionSchema> collection = db.GetCollection<PermissionSchema>($"{tenantId}-permissionSchemas");

            FilterDefinition<PermissionSchema> filter = Builders<PermissionSchema>.Filter.Eq(p => p.Id, command.PermissionSchemaId);

            UpdateDefinitionBuilder<PermissionSchema> update = Builders<PermissionSchema>.Update;

            var updates = new List<UpdateDefinition<PermissionSchema>>
            {
                update.Set(a => a.LastModifiedAt, DateTime.UtcNow),
                update.Set(a => a.LastModifiedById, command.ModifiedById)
            };
            if (command.Name != null) updates.Add(update.Set(a => a.Name, command.Name));
            if (command.Description != null) updates.Add(update.Set(a => a.Description, command.Description));
            if (command.ObjectType != null) updates.Add(update.Set(a => a.ObjectType, command.ObjectType));
            if (command.ActionType != null) updates.Add(update.Set(a => a.ActionType, command.ActionType));
            if (command.Schema != null) updates.Add(update.Set(a => a.Schema, JSchema.Parse(command.Schema)));
            if (command.SchemaPatch != null)
            {
                PermissionSchema schema = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);
                if (schema.Schema != null)
                {
                    JToken updatedSchema = new JsonProjector().Write(schema.Schema, command.SchemaPatch);
                    updates.Add(update.Set(a => a.Schema, updatedSchema));
                }
            }
            if (command.StateCondition != null) updates.Add(update.Set(a => a.StateCondition, command.StateCondition));
            if (command.UpdateCondition != null) updates.Add(update.Set(a => a.UpdateCondition, command.UpdateCondition));

            UpdateResult result = await collection.UpdateOneAsync(filter, update.Combine(updates), cancellationToken: cancellationToken);

            return new() { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> Delete(string tenantId, string permissionSchemaId, DeleteCommand command, CancellationToken cancellationToken)
        {
            IMongoDatabase db = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName);

            DeleteResult result = await db.GetCollection<PermissionSchema>(
                $"{tenantId}-permissionSchemas").DeleteOneAsync(Builders<PermissionSchema>.Filter.Eq(p => p.Id, permissionSchemaId), cancellationToken);

            return result.DeletedCount > 0
                ? new() { Status = "success" }
                : new() { Status = "failure", Errors = new List<string> { "PermissionSchema with such id doesn't exist" } };
        }
    }
}