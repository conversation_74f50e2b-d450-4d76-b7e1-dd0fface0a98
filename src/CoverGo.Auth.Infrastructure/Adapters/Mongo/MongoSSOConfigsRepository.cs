﻿using CoverGo.Auth.Domain;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.Adapters.Mongo;

public class MongoSSOConfigsRepository
{
    const string DbName = "auth";

    public virtual async Task<Result> AddSSOConfigToTenantSettingsAsync(string tenantId, SSOConfig input,
        CancellationToken cancellationToken)
    {
        MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

        IMongoCollection<TenantSettings> collection =
            client.GetDatabase(DbName).GetCollection<TenantSettings>($"_tenants");

        FilterDefinition<TenantSettings> filter = Builders<TenantSettings>.Filter.Eq(t => t.TenantId, tenantId);

        TenantSettings settings = await collection.Find(filter).SingleOrDefaultAsync(cancellationToken);
        if (settings.SSOConfigs?.Any(c => c.Id == input.Id) ?? false)
            return new Result
            {
                Status = "failure",
                Errors = new List<string> { $"The tenant settings already contains an SSOConfig for id '{input.Id}'." }
            };

        UpdateDefinition<TenantSettings> update = Builders<TenantSettings>.Update
            .AddToSet(s => s.SSOConfigs, input);

        UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

        return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
    }

    public virtual async Task<Result> RemoveSSOConfigFromTenantSettingsAsync(string tenantId, string configId,
        CancellationToken cancellationToken)
    {
        MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

        IMongoCollection<TenantSettings> collection =
            client.GetDatabase(DbName).GetCollection<TenantSettings>($"_tenants");

        FilterDefinition<TenantSettings> filter = Builders<TenantSettings>.Filter.Eq(t => t.TenantId, tenantId);

        TenantSettings settings = await collection.Find(filter).SingleOrDefaultAsync(cancellationToken);
        if (!settings.SSOConfigs?.Any(c => c.Id == configId) ?? false)
            return new Result
            {
                Status = "failure",
                Errors = new List<string> { $"The tenant settings does not contain an SSOConfig with Id '{configId}'." }
            };

        UpdateDefinition<TenantSettings> update = Builders<TenantSettings>.Update
            .PullFilter(s => s.SSOConfigs, c => c.Id == configId);

        UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

        return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
    }

    public virtual async Task<SSOConfig> GetConfigByIdAsync(string id, bool cleanClientSecret = false,
        CancellationToken cancellationToken = default)
    {
        MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig("covergo"));

        FilterDefinition<TenantSettings> filter =
            new FilterDefinitionBuilder<TenantSettings>().ElemMatch(t => t.SSOConfigs, s => s.Id == id);

        TenantSettings tenantSettings = await client.GetDatabase(DbName).GetCollection<TenantSettings>("_tenants")
            .Find(filter).FirstOrDefaultAsync(cancellationToken);

        SSOConfig config = tenantSettings?.SSOConfigs?.FirstOrDefault(s => s.Id == id);
        if (config == null)
        {
            return null;
        }

        config.TenantId = tenantSettings.TenantId;

        if (cleanClientSecret)
        {
            config.ClientSecret = null;
        }

        return config;
    }

    public virtual async Task<SSOConfig> GetConfigByIdAndClientIdAsync(string id, string clientId, bool cleanClientSecret = false,
        CancellationToken cancellationToken = default)
    {
        MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig("covergo"));

        FilterDefinition<TenantSettings> filter =
            new FilterDefinitionBuilder<TenantSettings>().ElemMatch(t => t.SSOConfigs, s => s.Id == id);

        TenantSettings tenantSettings = await client.GetDatabase(DbName).GetCollection<TenantSettings>("_tenants")
            .Find(filter).FirstOrDefaultAsync(cancellationToken);

        SSOConfig config = tenantSettings?.SSOConfigs?.FirstOrDefault(s => s.Id == id && s.ClientId == clientId);
        if (config == null)
        {
            return null;
        }

        config.TenantId = tenantSettings.TenantId;

        if (cleanClientSecret)
        {
            config.ClientSecret = null;
        }

        return config;
    }
}
