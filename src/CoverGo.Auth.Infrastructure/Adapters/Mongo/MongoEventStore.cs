﻿using CoverGo.Auth.Domain;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.Adapters.Mongo
{
    public class MongoEventStore : IEventStore
    {

        public string ProviderId => "mongoDb";
        private const string DbName = "auth";

        public async Task<Result> AddEventAsync(string tenantId, AppEvent authEvent, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<AppEvent> clientCollection = db.GetCollection<AppEvent>($"{tenantId}-clients_events");
            await clientCollection.Indexes.CreateManyAsync(new[]
            {
                new CreateIndexModel<AppEvent>(Builders<AppEvent>.IndexKeys.Ascending(f => f.Id)),
                new CreateIndexModel<AppEvent>(Builders<AppEvent>.IndexKeys.Ascending(f => f.Timestamp)),
            }, cancellationToken);

            await clientCollection.InsertOneAsync(authEvent, cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> AddEventAsync(string tenantId, LoginEvent authEvent, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<LoginEvent> clientCollection = db.GetCollection<LoginEvent>($"{tenantId}-users_events");
            await clientCollection.Indexes.CreateManyAsync(new[]
            {
                new CreateIndexModel<LoginEvent>(Builders<LoginEvent>.IndexKeys.Ascending(f => f.Id)),
                new CreateIndexModel<LoginEvent>(Builders<LoginEvent>.IndexKeys.Ascending(f => f.Timestamp)),
            }, cancellationToken);

            await clientCollection.InsertOneAsync(authEvent, cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> AddEventAsync(string tenantId, PermissionGroupEvent authEvent, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<PermissionGroupEvent> clientCollection = db.GetCollection<PermissionGroupEvent>($"{tenantId}-permissionGroups_events");
            await clientCollection.Indexes.CreateManyAsync(new[]
            {
                new CreateIndexModel<PermissionGroupEvent>(Builders<PermissionGroupEvent>.IndexKeys.Ascending(f => f.Id)),
                new CreateIndexModel<PermissionGroupEvent>(Builders<PermissionGroupEvent>.IndexKeys.Ascending(f => f.Timestamp)),
            }, cancellationToken);

            BsonDocument bsonValues = null;
            if (authEvent.Values != null)
            {
                string jsonValues = JsonConvert.SerializeObject(authEvent.Values, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, ContractResolver = new CamelCasePropertyNamesContractResolver() });
                bsonValues = MongoDB.Bson.Serialization.BsonSerializer.Deserialize<BsonDocument>(jsonValues);
            }

            await clientCollection.InsertOneAsync(authEvent, cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> AddEventAsync(string tenantId, TargetGroupEvent authEvent, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<TargetGroupEvent> clientCollection = db.GetCollection<TargetGroupEvent>($"{tenantId}-targetGroups_events");
            await clientCollection.Indexes.CreateManyAsync(new[]
            {
                new CreateIndexModel<TargetGroupEvent>(Builders<TargetGroupEvent>.IndexKeys.Ascending(f => f.Id)),
                new CreateIndexModel<TargetGroupEvent>(Builders<TargetGroupEvent>.IndexKeys.Ascending(f => f.Timestamp)),
            }, cancellationToken);

            BsonDocument bsonValues = null;
            if (authEvent.Values != null)
            {
                string jsonValues = JsonConvert.SerializeObject(authEvent.Values, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, ContractResolver = new CamelCasePropertyNamesContractResolver() });
                bsonValues = MongoDB.Bson.Serialization.BsonSerializer.Deserialize<BsonDocument>(jsonValues);
            }

            await clientCollection.InsertOneAsync(authEvent, cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<IEnumerable<AppEvent>> GetAppEventsAsync(string tenantId, IEnumerable<AppEventType> types, IEnumerable<string> appIds, CancellationToken cancellationToken, DateTime? fromDate = null, DateTime? toDate = null)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<AppEvent> clientCollection = db.GetCollection<AppEvent>($"{tenantId}-clients_events");

            FilterDefinition<AppEvent> filter = Builders<AppEvent>.Filter.Empty;
            if (types != null)
                filter &= Builders<AppEvent>.Filter.In(e => e.Type, types);

            if (appIds != null)
                filter &= Builders<AppEvent>.Filter.In(e => e.AppId, appIds);
            if (fromDate.HasValue)
                filter &= Builders<AppEvent>.Filter.Gte(c => c.Timestamp, fromDate.Value);
            if (toDate.HasValue)
                filter &= Builders<AppEvent>.Filter.Lte(c => c.Timestamp, toDate.Value);

            IEnumerable<AppEvent> events = await clientCollection.Find(filter).ToListAsync(cancellationToken);

            return events;
        }

        public async Task<IEnumerable<LoginEvent>> GetLoginEventsAsync(string tenantId, IEnumerable<LoginEventType> types, IEnumerable<string> loginIds, CancellationToken cancellationToken, DateTime? fromDate = null, DateTime? toDate = null)
        {
            IMongoCollection<LoginEvent> clientCollection = GetLoginEventsCollection(tenantId);
            FilterDefinition<LoginEvent> filter = GetLoginFilterDefinition(types, loginIds, fromDate, toDate);

            IEnumerable<LoginEvent> events = await clientCollection.Find(filter).ToListAsync(cancellationToken);

            return events;
        }

        public async Task<IEnumerable<LoginEvent>> GetLoginEventsV2Async(string tenantId,
            QueryArguments<LoginEventWhere> query,
            CancellationToken cancellationToken)
        {
            IMongoCollection<LoginEvent> clientCollection = GetLoginEventsCollection(tenantId);
            FilterDefinition<LoginEvent> filter = GetLoginFilterDefinition(query.Where.Types,
                query.Where.LoginIds, query.Where.FromDate, query.Where.ToDate);

            IEnumerable<LoginEvent> events;
            IAggregateFluent<LoginEvent> eventQuery = clientCollection.Aggregate().Match(filter);

            if (query.OrderBy is not null)
                eventQuery = eventQuery.Sort(query.OrderBy.Type == OrderByType.ASC
                    ? Builders<LoginEvent>.Sort.Ascending(query.OrderBy.FieldName)
                    : Builders<LoginEvent>.Sort.Descending(query.OrderBy.FieldName));

            if (query.Skip is not null)
                eventQuery = eventQuery.Skip(query.Skip.Value);

            if (query.First is not null)
                eventQuery = eventQuery.Limit(query.First.Value);

            if (query.GroupBy is not null)
            {
                var groupEventQuery = eventQuery
                    .Group(new BsonDocument {
                        { "_id", $"${query.GroupBy.FieldName}" },
                        { "first", new BsonDocument { { "$first", "$$ROOT" } } }
                    })
                    .Project(d => new
                    {
                        Id = d["first"]["_id"],
                        LoginId = d["first"]["loginId"],
                        Timestamp = d["first"]["timestamp"],
                        Type = d["first"]["type"],
                        Values = d["first"]["values"]
                    });

                events = (await groupEventQuery.ToListAsync(cancellationToken))
                    .Select(e => BsonSerializer.Deserialize<LoginEvent>(e.ToJson()))
                    .ToList();
            }
            else
            {
                events = await eventQuery.ToListAsync(cancellationToken);
            }

            return events;
        }

        public async Task<IEnumerable<PermissionGroupEvent>> GetPermissionGroupEventsAsync(string tenantId, IEnumerable<PermissionGroupEventType> types, IEnumerable<string> permissionGroupIds, CancellationToken cancellationToken, DateTime? fromDate = null, DateTime? toDate = null)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<PermissionGroupEvent> clientCollection = db.GetCollection<PermissionGroupEvent>($"{tenantId}-permissionGroups_events");

            FilterDefinition<PermissionGroupEvent> filter = Builders<PermissionGroupEvent>.Filter.Empty;
            if (types != null)
                filter &= Builders<PermissionGroupEvent>.Filter.In(e => e.Type, types);

            if (permissionGroupIds != null)
                filter &= Builders<PermissionGroupEvent>.Filter.In(e => e.PermissionGroupId, permissionGroupIds);
            if (fromDate.HasValue)
                filter &= Builders<PermissionGroupEvent>.Filter.Gte(c => c.Timestamp, fromDate.Value);
            if (toDate.HasValue)
                filter &= Builders<PermissionGroupEvent>.Filter.Lte(c => c.Timestamp, toDate.Value);

            IEnumerable<PermissionGroupEvent> events = await clientCollection.Find(filter).ToListAsync(cancellationToken);

            return events.Select(e => new PermissionGroupEvent(e.PermissionGroupId, e.Type, e.Timestamp)
            {
                Id = e.Id.ToString(),
                Values = e.Values != null ? JObject.Parse(e.Values.ToString()) : null
            });
        }

        public async Task<IEnumerable<TargetGroupEvent>> GetTargetGroupEventsAsync(string tenantId, IEnumerable<TargetGroupEventType> types, IEnumerable<string> targetGroupIds, CancellationToken cancellationToken, DateTime? fromDate = null, DateTime? toDate = null)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<TargetGroupEvent> clientCollection = db.GetCollection<TargetGroupEvent>($"{tenantId}-targetGroups_events");

            FilterDefinition<TargetGroupEvent> filter = Builders<TargetGroupEvent>.Filter.Empty;
            if (types != null)
                filter &= Builders<TargetGroupEvent>.Filter.In(e => e.Type, types);

            if (targetGroupIds != null)
                filter &= Builders<TargetGroupEvent>.Filter.In(e => e.TargetGroupId, targetGroupIds);
            if (fromDate.HasValue)
                filter &= Builders<TargetGroupEvent>.Filter.Gte(c => c.Timestamp, fromDate.Value);
            if (toDate.HasValue)
                filter &= Builders<TargetGroupEvent>.Filter.Lte(c => c.Timestamp, toDate.Value);

            IEnumerable<TargetGroupEvent> events = await clientCollection.Find(filter).ToListAsync(cancellationToken);

            return events;
        }

        private IMongoCollection<LoginEvent> GetLoginEventsCollection(string tenantId)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(DbName);

            return db.GetCollection<LoginEvent>($"{tenantId}-users_events");
        }

        private FilterDefinition<LoginEvent> GetLoginFilterDefinition(IEnumerable<LoginEventType> types,
            IEnumerable<string> loginIds, DateTime? fromDate, DateTime? toDate)
        {
            FilterDefinition<LoginEvent> filter = Builders<LoginEvent>.Filter.Empty;
            if (types != null)
                filter &= Builders<LoginEvent>.Filter.In(e => e.Type, types);

            if (loginIds != null)
                filter &= Builders<LoginEvent>.Filter.In(e => e.LoginId, loginIds);
            if (fromDate.HasValue)
                filter &= Builders<LoginEvent>.Filter.Gte(c => c.Timestamp, fromDate.Value);
            if (toDate.HasValue)
                filter &= Builders<LoginEvent>.Filter.Lte(c => c.Timestamp, toDate.Value);

            return filter;
        }
    }
}
