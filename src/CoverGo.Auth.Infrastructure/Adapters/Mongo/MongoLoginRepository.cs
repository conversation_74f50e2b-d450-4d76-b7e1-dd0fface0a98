﻿using CoverGo.Auth.Domain;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using IdentityServer4.Models;
using MongoDB.Driver;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.Adapters.Mongo
{
    public class MongoLoginRepository
    {
        string DbName => "auth";

        string CollectionNameGet(string tenantId) => $"{tenantId}-users";

        public async Task<Result> DeactivateAsync(string tenantId, string loginId, CancellationToken cancellationToken)
        {
            IMongoCollection<MongoLoginDao> collection = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName).GetCollection<MongoLoginDao>(CollectionNameGet(tenantId));

            FilterDefinition<MongoLoginDao> filter = Builders<MongoLoginDao>.Filter.Eq(x => x.Id, loginId);
            UpdateDefinition<MongoLoginDao> updateDefinition = Builders<MongoLoginDao>.Update.Set(x => x.IsActive, false);
            updateDefinition = updateDefinition.Set(x => x.PasswordHash, null);

            try
            {
                UpdateResult result = await collection.UpdateOneAsync(
                    filter,
                    updateDefinition,
                    new UpdateOptions() { IsUpsert = true },
                    cancellationToken);

                return result.MatchedCount > 0
                    ? Result.Success()
                    : Result.Failure($"No login with id {loginId} to update.");
            }
            catch (Exception ex)
            {
                return Result.Failure($"Unable to update login with id {loginId} ||| {ex.Message}");
            }
        }

        public async Task<Result> ReactivateAsync(string tenantId, string loginId, CancellationToken cancellationToken)
        {
            IMongoCollection<MongoLoginDao> collection = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName).GetCollection<MongoLoginDao>(CollectionNameGet(tenantId));

            FilterDefinition<MongoLoginDao> filter = Builders<MongoLoginDao>.Filter.Eq(x => x.Id, loginId);
            UpdateDefinition<MongoLoginDao> updateDefinition = Builders<MongoLoginDao>.Update.Set(x => x.IsActive, true);

            try
            {
                UpdateResult result = await collection.UpdateOneAsync(
                    filter,
                    updateDefinition,
                    new UpdateOptions() { IsUpsert = true },
                    cancellationToken);

                return result.MatchedCount > 0
                    ? Result.Success()
                    : Result.Failure($"No login with id {loginId} to update.");
            }
            catch (Exception ex)
            {
                return Result.Failure($"Unable to update login with id {loginId} ||| {ex.Message}");
            }
        }

        public virtual async Task<bool> DoesActiveLoginExistByEmail(string tenantId, string email, CancellationToken cancellationToken)
        {
            IMongoCollection<MongoLoginDao> collection = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName).GetCollection<MongoLoginDao>(CollectionNameGet(tenantId));

            FilterDefinition<MongoLoginDao> filter = Builders<MongoLoginDao>.Filter.Eq(x => x.Email, email)
                & Builders<MongoLoginDao>.Filter.Ne(x => x.IsActive, false);

            bool activeLoginExists = await collection.Find(
                filter).AnyAsync(cancellationToken);

            return activeLoginExists;
        }

        public async Task RemovePersistentGrantsForLoginAsync(string tenantId, string subjectId)
        {
            IMongoCollection<PersistedGrant> collection = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName).PersistedGrantsCollection(tenantId);
            DeleteResult result = await collection.DeleteManyAsync(
                it => it.SubjectId == subjectId);
        }
    }
}
