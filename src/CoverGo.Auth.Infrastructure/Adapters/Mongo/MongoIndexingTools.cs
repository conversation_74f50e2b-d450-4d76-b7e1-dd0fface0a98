﻿using CoverGo.Configuration;
using CoverGo.MongoUtils;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.Adapters.Mongo
{
    public static class MongoIndexingTools
    {
        public static async Task IndexingAsync(DbConfig config, int intervalInSeconds = 10800)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(config);

            while (true) {
                IMongoDatabase db = client.GetDatabase("auth");

                List<string> clientCollectionNames = await (await db.ListCollectionNamesAsync(new ListCollectionNamesOptions {
                    Filter = Builders<BsonDocument>.Filter.Regex("name", $"/-clients$/")
                })).ToListAsync();

                foreach (string colName in clientCollectionNames) {
                    IMongoCollection<BsonDocument> clientCollection = db.GetCollection<BsonDocument>(colName);
                    await clientCollection.Indexes.CreateManyAsync(new List<CreateIndexModel<BsonDocument>> {
                        new(Builders<BsonDocument>.IndexKeys.Ascending("_id")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("clientId")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("createdById")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("createdAt")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedById")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedAt")),
                    });
                }

                List<string> loginCollectionNames = await (await db.ListCollectionNamesAsync(new ListCollectionNamesOptions {
                    Filter = Builders<BsonDocument>.Filter.Regex("name", $"/-users$/")
                })).ToListAsync();

                foreach (string colName in loginCollectionNames) {
                    IMongoCollection<BsonDocument> clientCollection = db.GetCollection<BsonDocument>(colName);
                    await clientCollection.Indexes.CreateManyAsync(new List<CreateIndexModel<BsonDocument>> {
                        new(Builders<BsonDocument>.IndexKeys.Ascending("_id")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("userName")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("normalizedUserName")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("entityId")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("entityType")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("email")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("normalizedEmail")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("emailConfirmed")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("claims")),
                        //new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("claims[-1]")),
                        //new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("claims[-1].type")),
                        //new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("claims[-1].value")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("claims.type")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("claims.value")),
                        new(Builders<BsonDocument>.IndexKeys.Ascending("claims.type").Ascending("claims.value")),
                        new(Builders<BsonDocument>.IndexKeys.Combine(
                            Builders<BsonDocument>.IndexKeys.Ascending("claims.type"),
                            Builders<BsonDocument>.IndexKeys.Ascending("claims.value"))),
                        new(Builders<BsonDocument>.IndexKeys.Descending("createdById")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("createdAt")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedById")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedAt")),
                    });
                }

                List<string> permissionGroupCollectionNames = await (await db.ListCollectionNamesAsync(new ListCollectionNamesOptions {
                    Filter = Builders<BsonDocument>.Filter.Regex("name", $"/-permissionGroups$/")
                })).ToListAsync();

                foreach (string colName in permissionGroupCollectionNames) {
                    IMongoCollection<BsonDocument> clientCollection = db.GetCollection<BsonDocument>(colName);
                    await clientCollection.Indexes.CreateManyAsync(new List<CreateIndexModel<BsonDocument>> {
                        new(Builders<BsonDocument>.IndexKeys.Ascending("_id")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("createdById")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("createdAt")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedById")),
                        new(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedAt")),
                    });
                }

                await Task.Delay(TimeSpan.FromSeconds(intervalInSeconds));
            }
        }
    }

    public class JTokenSerializer : SerializerBase<JToken>
    {
        public override JToken Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
        {
            BsonValue bsonValue = BsonValueSerializer.Instance.Deserialize(context);
            string jsonString = JsonConvert.SerializeObject(BsonTypeMapper.MapToDotNetValue(bsonValue));
            return JToken.Parse(jsonString);
        }

        public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, JToken value) =>
            BsonValueSerializer.Instance.Serialize(context, BsonValue.Create(value.ToObject()));
    }

    public static class JsonHelper
    {
        public static object ToObject(this JToken token)
        {
            switch (token.Type) {
                case JTokenType.Object:
                    return token
                        .Children<JProperty>()
                        .ToDictionary(
                            prop => prop.Name,
                            prop => ToObject(prop.Value));
                case JTokenType.TimeSpan:
                    return ((JValue) token).Value.ToString();

                case JTokenType.Array:
                    return token.Select(ToObject);

                default:
                    return ((JValue) token).Value;
            }
        }
    }
}