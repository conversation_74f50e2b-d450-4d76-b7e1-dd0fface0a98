﻿using CoverGo.Auth.Domain;
using CoverGo.Auth.Infrastructure.Adapters.Mongo.Repository;
using CoverGo.Auth.Infrastructure.Utils;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using IdentityServer4.Models;
using Microsoft.Extensions.Hosting;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace CoverGo.Auth.Infrastructure.Adapters.Mongo
{
    public class MongoTenantRepository : ITenantRepository
    {
        public string ProviderId { get; } = "mongoDb";
        private const string DbName = "auth";
        private readonly IMongoClientFactory _mongoClientFactory;
        private readonly IMongoCollectionFactory _mongoCollectionFactory;

        public MongoTenantRepository(IMongoClientFactory mongoClientFactory, IMongoCollectionFactory mongoCollectionFactory)
        {
            _mongoClientFactory = mongoClientFactory;
            _mongoCollectionFactory = mongoCollectionFactory;
        }

        public async Task<TenantSettings> GetTenantSettingsAsync(string tenantId, CancellationToken cancellationToken)
        {
            var client = _mongoClientFactory.GetOrAddMongoClient(tenantId);

            return await _mongoCollectionFactory
                .GetCollection<TenantSettings>(client, DbName, "_tenants")
                .SingleOrDefaultAsync(t => t.TenantId == tenantId, cancellationToken);
        }

        public async Task<TenantIdAndAppId> GetTenantIdAndAppIdFromUrl(string appUrl, CancellationToken cancellationToken)
        {
            (string decodedUrl, string host) = ValidateUrl(appUrl);
            var mongoClient = _mongoClientFactory.GetOrAddMongoClient("covergo");

            TenantSettings tenantSettings = await _mongoCollectionFactory
                .GetCollection<TenantSettings>(mongoClient, DbName, "_tenants")
                .SingleOrDefaultAsync(t => t.Hosts.Contains(host), cancellationToken);

            if (tenantSettings == null)
                throw new ArgumentOutOfRangeException(nameof(appUrl), "No tenant uses the provided application URL.");

            FilterDefinition<AppDao> filter = Builders<AppDao>.Filter.Ne(a => a.UrlRouting, null);
            ProjectionDefinition<AppDao> projection = Builders<AppDao>.Projection.Include(a => a.ClientId).Include(a => a.UrlRouting);
            List<AppDao> appDaos = await _mongoCollectionFactory
                .GetCollection<AppDao>(mongoClient, DbName, $"{tenantSettings.TenantId}-clients")
                .ToListAsync<AppDao>(filter, projection, cancellationToken);
            if (!appDaos.Any())
                throw new ArgumentOutOfRangeException(nameof(appUrl), "No application uses the provided application URL.");

            AppDao matchedApp = appDaos
                ?.Where(a => GetUrlRoutingRegEx(a.UrlRouting)?.IsMatch(decodedUrl) ?? false)
                ?.MinBy(a => a.UrlRouting.Order);
            if (matchedApp == null)
                throw new ArgumentOutOfRangeException(nameof(appUrl), "No application uses the provided application URL.");

            return new()
            {
                TenantId = tenantSettings.TenantId,
                AppId = matchedApp.ClientId
            };

            static (string decodedUrl, string host) ValidateUrl(string appUrl)
            {
                string decodedUrl = HttpUtility.UrlDecode(appUrl);
                bool uriCreateSuccess = Uri.TryCreate(decodedUrl, UriKind.RelativeOrAbsolute, out Uri uri);
                if (!uriCreateSuccess)
                    throw new ArgumentException("Invalid application URL", nameof(appUrl));

                return (decodedUrl, uri.Host);
            }

            static Regex GetUrlRoutingRegEx(UrlRouting urlRouting)
            {
                if (string.IsNullOrWhiteSpace(urlRouting.Url))
                {
                    return string.IsNullOrWhiteSpace(urlRouting.RegexPattern) ? null : new Regex(urlRouting.RegexPattern, RegexOptions.IgnoreCase);
                }
                else
                {
                    return new Regex($"^{urlRouting.Url.Replace(".", "\\.")}$", RegexOptions.IgnoreCase);
                }
            }
        }

        public async Task<Result> AddHostToTenantSettingsAsync(string tenantId, string host, CancellationToken cancellationToken)
        {
            var client = _mongoClientFactory.GetOrAddMongoClient(tenantId);

            var collection = _mongoCollectionFactory.GetCollection<TenantSettings>(client, DbName, $"_tenants");
            if (await OtherTenantContainsHost(collection, tenantId, host, cancellationToken))
            {
                return new Result { Status = "failure", Errors = new List<string> { $"Another tenant settings already contain the host '{host}'." } };
            }

            FilterDefinition<TenantSettings> filter = Builders<TenantSettings>.Filter.Eq(t => t.TenantId, tenantId);

            TenantSettings settings = await collection.SingleOrDefaultAsync(filter, cancellationToken);
            if (settings.Hosts?.Contains(host) ?? false)
                return new Result { Status = "failure", Errors = new List<string> { $"The tenant settings already contains the host '{host}'." } };

            UpdateDefinition<TenantSettings> update = Builders<TenantSettings>.Update
                .AddToSet(p => p.Hosts, host);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        private static async Task<bool> OtherTenantContainsHost(IMongoCollectionRepository<TenantSettings> collection, string tenantId, string host, CancellationToken cancellationToken)
        {

            FilterDefinitionBuilder<TenantSettings> filterBuilder = Builders<TenantSettings>.Filter;
            return await collection.AnyAsync(filterBuilder.Ne(t => t.TenantId, tenantId) & filterBuilder.AnyEq(t => t.Hosts, host), null, cancellationToken);
        }

        private static async Task<bool> TenantContainsHost(IMongoCollectionRepository<TenantSettings> collection, string tenantId, string host, CancellationToken cancellationToken)
        {
            FilterDefinitionBuilder<TenantSettings> filterBuilder = Builders<TenantSettings>.Filter;
            return await collection.AnyAsync(filterBuilder.Eq(t => t.TenantId, tenantId) & filterBuilder.AnyEq(t => t.Hosts, host), null, cancellationToken);
        }

        public async Task<Result> RemoveHostFromTenantSettingsAsync(string tenantId, string host, CancellationToken cancellationToken)
        {
            var client = _mongoClientFactory.GetOrAddMongoClient(tenantId);

            var collection = _mongoCollectionFactory.GetCollection<TenantSettings>(client, DbName, $"_tenants");

            FilterDefinition<TenantSettings> filter = Builders<TenantSettings>.Filter.Eq(t => t.TenantId, tenantId);

            TenantSettings settings = await collection.SingleOrDefaultAsync(filter, cancellationToken);
            if (!settings.Hosts?.Contains(host) ?? false)
                return new Result { Status = "failure", Errors = new List<string> { $"The tenant settings does not contain the host '{host}'." } };

            UpdateDefinition<TenantSettings> update = Builders<TenantSettings>.Update
                .Pull(p => p.Hosts, host);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<App> GetAppAsync(string tenantId, string clientId, CancellationToken cancellationToken)
        {

            var client = _mongoClientFactory.GetOrAddMongoClient(tenantId);
            IMongoDatabase db = client.GetDatabase(DbName);

            AppDao app = await _mongoCollectionFactory
                .GetCollection<AppDao>(client, DbName, $"{tenantId}-clients")
                .SingleOrDefaultAsync(c => c.ClientId == clientId, cancellationToken);
            return AppDao.ToApp(app);
        }

        public async Task<IEnumerable<App>> GetAppsAsync(string tenantId, AppWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken)
        {
            var client = _mongoClientFactory.GetOrAddMongoClient(tenantId);

            var clientCollection = _mongoCollectionFactory.GetCollection<AppDao>(client, DbName, $"{tenantId}-clients");

            // where
            FilterDefinition<AppDao> mongoFilter = FilterDefinition<AppDao>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            SortDefinition<AppDao> sort = null;
            // orderby
            if (orderBy != null)
                sort = orderBy.Type == OrderByType.ASC
                    ? Builders<AppDao>.Sort.Ascending(orderBy.FieldName)
                    : Builders<AppDao>.Sort.Descending(orderBy.FieldName);

            IEnumerable<AppDao> appDaos = await clientCollection.ToListAsync(mongoFilter, sort, skip, first, cancellationToken);
            return appDaos.Select(a => AppDao.ToApp(a));
        }

        public async Task<long> GetAppTotalCount(string tenantId, AppWhere where, CancellationToken cancellationToken)
        {
            var client = _mongoClientFactory.GetOrAddMongoClient(tenantId);

            var clientCollection = _mongoCollectionFactory.GetCollection<AppDao>(client, DbName, $"{tenantId}-clients");

            // where
            FilterDefinition<AppDao> mongoFilter = FilterDefinition<AppDao>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            long count = await clientCollection.CountDocumentsAsync(mongoFilter, cancellationToken);
            return count;
        }

        public async Task<Result> CreateAppAsync(string tenantId, CreateAppCommand command, CancellationToken cancellationToken)
        {
            var appDao = new AppDao
            {
                Enabled = true,
                ClientId = command.AppId,
                ProtocolType = "oidc",
                ClientSecrets = new List<Secret> { },
                RequireClientSecret = false,
                ClientName = command.AppName,
                RequireConsent = true,
                AllowRememberConsent = true,
                AllowedGrantTypes = new List<string> { "password" },
                RedirectUris = command.RedirectUris ?? new List<string> { },
                PostLogoutRedirectUris = new List<string> { },
                FrontChannelLogoutSessionRequired = true,
                BackChannelLogoutSessionRequired = true,
                AllowOfflineAccess = true,
                AllowedScopes = new List<string>
                {
                    "custom_profile",
                    "all_user_claims"
                },
                AlwaysIncludeUserClaimsInIdToken = true,
                IdentityTokenLifetime = 300,
                AccessTokenLifetime = command.AccessTokenLifetime ?? (command.AppId == "coverQuote" ? 31557600 : 600), //check coverQuote for initializeTenant
                Requires2FA = command.Requires2FA,
                AuthorizationCodeLifetime = 300,
                AbsoluteRefreshTokenLifetime = command.AbsoluteRefreshTokenLifetime ?? 28800,
                SlidingRefreshTokenLifetime = command.SlidingRefreshTokenLifetime ?? 28800,
                RefreshTokenUsage = TokenUsage.OneTimeOnly,
                AccessTokenType = AccessTokenType.Jwt,
                EnableLocalLogin = true,
                IdentityProviderRestrictions = new List<string> { },
                Claims = new List<Claim> { },
                AlwaysSendClientClaims = true,
                ClientClaimsPrefix = "client_",
                AllowedCorsOrigins = new List<string> { },
                Properties = new Dictionary<string, string>
                {
                    { "email", command.Email ?? "<EMAIL>"},
                    { "senderName", command.EmailSenderName ?? "CoverGo"}
                },
                DefaultTimeZone = command.DefaultTimeZone,
                UseNotificationConfig = command.UseNotificationConfig,
                UrlRouting = command.UrlRouting,
                EmailConfirmationTokenLifespan = command.EmailConfirmationTokenLifespan ?? TimeSpan.FromDays(1),
                ActivationTokenExpiryDisabled = command.ActivationTokenExpiryDisabled,
                RequiresEmail2FA = command.RequiresEmail2FA,
                AppConfig = command.AppConfig == null ? null : BsonDocument.Parse(command.AppConfig),
                ForgotPasswordEmailSettings = command.ForgotPasswordEmailSettings
            };

            appDao.CreatedAt = DateTime.UtcNow;
            appDao.LastModifiedAt = DateTime.UtcNow;
            appDao.CreatedById = command.CreatedById;
            appDao.LastModifiedById = command.CreatedById;

            var client = _mongoClientFactory.GetOrAddMongoClient(tenantId);
            Result validationResult = await ValidateUrlRoutingUrl(client, tenantId, appDao.UrlRouting?.Url, cancellationToken);
            if (validationResult != null)
            {
                return validationResult;
            }

            await _mongoCollectionFactory.GetCollection<AppDao>(client, DbName, $"{tenantId}-clients").InsertOneAsync(appDao, cancellationToken: cancellationToken);
            return new Result { Status = "success" };
        }

        private async Task<Result> ValidateUrlRoutingUrl(IMongoClient client, string tenantId, string url, CancellationToken cancellationToken)
        {
            if (!string.IsNullOrWhiteSpace(url) && Uri.TryCreate(url, new UriCreationOptions(), out Uri uri) &&
                !await TenantContainsHost(_mongoCollectionFactory.GetCollection<TenantSettings>(client, DbName, $"_tenants"), tenantId, uri.Host, cancellationToken))
            {
                return new Result()
                {
                    Status = "failure",
                    Errors = new List<string>() { $"URL routing host for URL {url} is not defined in the tenant {tenantId}. Applications can use only hosts that are also defined in the tenant." }
                };
            }
            return null;
        }

        public async Task<Result> UpdateAppAsync(string tenantId, string appId, UpdateAppCommand command, CancellationToken cancellationToken)
        {
            var client = _mongoClientFactory.GetOrAddMongoClient(tenantId);
            var updates = new List<UpdateDefinition<AppDao>>();
            if (command.IsAccessTokenLifetimeChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.AccessTokenLifetime, command.AccessTokenLifetime));
            if (command.IsEmailConfirmationTokenLifespanChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.EmailConfirmationTokenLifespan, command.EmailConfirmationTokenLifespan.Value));
            if (command.IsDataProtectionTokenLifespanChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.DataProtectionTokenLifespan, command.DataProtectionTokenLifespan.Value));
            if (command.IsRequires2FAChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.Requires2FA, command.Requires2FA));
            if (command.IsAppNameChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.ClientName, command.AppName));
            if (command.IsRedirectUrisChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.RedirectUris, command.RedirectUris));
            if (command.IsUrlRoutingChanged)
            {
                if (command.UrlRouting == null)
                    updates.Add(Builders<AppDao>.Update.Unset(c => c.UrlRouting));
                else
                {
                    if (command.UrlRouting.IsUrlChanged)
                    {
                        Result validationResult = await ValidateUrlRoutingUrl(client, tenantId, command.UrlRouting.Url, cancellationToken);
                        if (validationResult != null)
                        {
                            return validationResult;
                        }
                        updates.Add(Builders<AppDao>.Update.Set(c => c.UrlRouting.Url, command.UrlRouting.Url));
                    }
                    if (command.UrlRouting.IsRegexPatternChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.UrlRouting.RegexPattern, command.UrlRouting.RegexPattern));
                    if (command.UrlRouting.IsOrderChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.UrlRouting.Order, command.UrlRouting.Order));
                }
            }
            if (command.IsDefaultTimeZoneChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.DefaultTimeZone, command.DefaultTimeZone));
            if (command.IsUseNotificationConfigChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.UseNotificationConfig, command.UseNotificationConfig));
            if (command.IsAbsoluteRefreshTokenLifetimeChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.AbsoluteRefreshTokenLifetime, command.AbsoluteRefreshTokenLifetime));
            if (command.IsSlidingRefreshTokenLifetimeChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.SlidingRefreshTokenLifetime, command.SlidingRefreshTokenLifetime));
            if (command.IsEmailChanged) updates.Add(Builders<AppDao>.Update.Set("properties.email", command.Email));
            if (command.IsEmailSenderNameChanged) updates.Add(Builders<AppDao>.Update.Set("properties.senderName", command.EmailSenderName));
            if (command.IsActivationTokenExpiryDisabledChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.ActivationTokenExpiryDisabled, command.ActivationTokenExpiryDisabled));
            if (command.IsRequiresEmail2FAChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.RequiresEmail2FA, command.RequiresEmail2FA));
            if (command.IsAppConfigChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.AppConfig, command.AppConfig == null ? null : BsonDocument.Parse(command.AppConfig)));
            if (command.IsForgotPasswordEmailSettingsChanged)
            {
                if (command.ForgotPasswordEmailSettings == null)
                    updates.Add(Builders<AppDao>.Update.Unset(c => c.ForgotPasswordEmailSettings));
                else
                {
                    if (command.ForgotPasswordEmailSettings.IsLinkChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.ForgotPasswordEmailSettings.Link, command.ForgotPasswordEmailSettings.Link));
                    if (command.ForgotPasswordEmailSettings.IsFromChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.ForgotPasswordEmailSettings.From, command.ForgotPasswordEmailSettings.From));
                    if (command.ForgotPasswordEmailSettings.IsSubjectChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.ForgotPasswordEmailSettings.Subject, command.ForgotPasswordEmailSettings.Subject));
                    if (command.ForgotPasswordEmailSettings.IsFromNameChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.ForgotPasswordEmailSettings.FromName, command.ForgotPasswordEmailSettings.FromName));
                    if (command.ForgotPasswordEmailSettings.IsTemplateIdChanged) updates.Add(Builders<AppDao>.Update.Set(c => c.ForgotPasswordEmailSettings.TemplateId, command.ForgotPasswordEmailSettings.TemplateId));
                }
            }

            updates.Add(Builders<AppDao>.Update.Set(c => c.LastModifiedAt, DateTime.UtcNow));
            updates.Add(Builders<AppDao>.Update.Set(c => c.LastModifiedById, command.ModifiedById));

            UpdateResult result = await _mongoCollectionFactory
                .GetCollection<AppDao>(client, DbName, $"{tenantId}-clients")
                .UpdateOneAsync(c => c.ClientId == appId, Builders<AppDao>.Update.Combine(updates), cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> DeleteAppAsync(string tenantId, string appId, DeleteCommand command, CancellationToken cancellationToken)
        {
            var client = _mongoClientFactory.GetOrAddMongoClient(tenantId);

            DeleteResult result = await _mongoCollectionFactory
                .GetCollection<AppDao>(client, DbName, $"{tenantId}-clients")
                .DeleteOneAsync(c => c.ClientId == appId, cancellationToken);
            return result.DeletedCount != 0 ? new Result { Status = "success" } : new Result { Status = "failure" };
        }

        public async Task<IList<string>> GetTenantIdsAsync(DbConfig config, CancellationToken cancellationToken)
        {
            if (config.ProviderId != ProviderId) return new List<string>();

            MongoClient client = MongoTools.GetOrAddMongoClient(config);
            return await GetTenantsAsync(client.GetDatabase(DbName));
        }

        private static async Task<List<string>> GetTenantsAsync(IMongoDatabase db)
        {
            List<string> collectionNames = await (await db.ListCollectionNamesAsync(new ListCollectionNamesOptions
            { Filter = Builders<BsonDocument>.Filter.Regex("name", "-users$") })).ToListAsync();
            return collectionNames.ConvertAll(n => n.Split('-').First());
        }
    }
}
