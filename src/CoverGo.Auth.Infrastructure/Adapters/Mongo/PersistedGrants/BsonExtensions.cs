﻿using IdentityServer4.Models;

using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.IdGenerators;

namespace CoverGo.Auth.Infrastructure.Adapters.Mongo.PersistedGrants;

public class MongoPersistedGrantsBsonExtensions
{
    public static void RegisterPersistedGrantClassMap()
    {
        if (BsonClassMap.IsClassMapRegistered(typeof(PersistedGrant)))
        {
            return;
        }

        BsonClassMap.RegisterClassMap<PersistedGrant>(cm =>
        {
            cm.AutoMap();
            cm.MapIdProperty(it => it.Key).SetIdGenerator(new NullIdChecker());
        });
    }
}
