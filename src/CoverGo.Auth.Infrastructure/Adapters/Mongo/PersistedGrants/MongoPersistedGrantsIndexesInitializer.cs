﻿using IdentityServer4.Models;

using MongoDB.Driver;

using System;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.Adapters.Mongo.PersistedGrants;

public class MongoPersistedGrantsIndexesInitializer
{
    private readonly string tenantId;

    public MongoPersistedGrantsIndexesInitializer(string tenantId)
    {
        this.tenantId = tenantId;
    }

    public async Task CreateIndexes(IMongoDatabase db)
    {
        var collection = db.PersistedGrantsCollection(tenantId);
        var indexes = collection.Indexes;

        var indexBuilder = Builders<PersistedGrant>.IndexKeys;

        await indexes.CreateOneAsync(new CreateIndexModel<PersistedGrant>(
            indexBuilder.Ascending(it => it.SubjectId),
            new()
            {
                Background = true,
            }));

        await indexes.CreateOneAsync(new CreateIndexModel<PersistedGrant>(
            indexBuilder
                .Ascending(it => it.SubjectId)
                .Ascending(it => it.ClientId),
            new()
            {
                Background = true,
            }));

        await indexes.CreateOneAsync(new CreateIndexModel<PersistedGrant>(
            indexBuilder
                .Ascending(it => it.SubjectId)
                .Ascending(it => it.ClientId)
                .Ascending(it => it.Type),
            new()
            {
                Background = true,
            }));

        await indexes.CreateOneAsync(new CreateIndexModel<PersistedGrant>(
            indexBuilder
                .Ascending(it => it.Expiration),
            new()
            {
                Background = true,
                ExpireAfter = TimeSpan.Zero,
            }));
    }
}
