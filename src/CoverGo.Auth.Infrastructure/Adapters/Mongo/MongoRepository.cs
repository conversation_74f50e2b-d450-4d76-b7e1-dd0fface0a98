﻿using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.Utils;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Auth.Domain.PermissionSchemas;
using CoverGo.Configuration;
using CoverGo.MongoUtils;
using CoverGo.Auth.Domain.TokenProviders;
using System.Text.RegularExpressions;

namespace CoverGo.Auth.Infrastructure.Adapters.Mongo
{
    public class MongoRepository : IRepository
    {
        private readonly UserManager<MongoLoginDao> _mongoUserManager;
        public MongoRepository(UserManager<MongoLoginDao> mongoUserManager)
        {
            _mongoUserManager = mongoUserManager;
        }

        public string ProviderId { get; } = "mongoDb";
        private const string DbName = "auth";

        public UserManager<MongoLoginDao> GetMongoUserManager() => _mongoUserManager;

        public async Task<MongoLoginDao> FindLoginByIdAsync(string loginId, CancellationToken cancellationToken) =>
            await _mongoUserManager.FindByIdAsync(loginId).ToCancellable(cancellationToken);

        public async Task<IdentityResult> UpdateLoginAsync(MongoLoginDao loginDao, CancellationToken cancellationToken) =>
            await _mongoUserManager.UpdateAsync(loginDao).ToCancellable(cancellationToken);

        public async Task<IdentityResult> AddClaimToLoginAsync(MongoLoginDao loginDao, Claim claim, CancellationToken cancellationToken) =>
            await _mongoUserManager.AddClaimAsync(loginDao, claim).ToCancellable(cancellationToken);

        public async Task<IdentityResult> RemoveClaimFromLoginAsync(MongoLoginDao loginDao, Claim claim, CancellationToken cancellationToken) =>
            await _mongoUserManager.RemoveClaimAsync(loginDao, claim).ToCancellable(cancellationToken);

        public async Task<IEnumerable<string>> GetLoginIdsAsync(string tenantId, LoginWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            IMongoCollection<MongoLoginDao> loginCollection = client.GetDatabase(DbName).GetCollection<MongoLoginDao>($"{tenantId}-users");

            // where
            FilterDefinition<MongoLoginDao> mongoFilter = FilterDefinition<MongoLoginDao>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IFindFluent<MongoLoginDao, MongoLoginDao> find = loginCollection.Find(mongoFilter);

            // orderby
            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<MongoLoginDao>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<MongoLoginDao>.Sort.Descending(orderBy.FieldName));

            List<MongoLoginDao> logins = await find
                .Skip(skip)
                .Limit(first)
                .ToListAsync(cancellationToken);

            return logins.Select(x => x.Id);
        }

        public async Task<MongoLoginDao> GetLoginAsync(string tenantId, string id, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            IMongoCollection<MongoLoginDao> loginCollection = client.GetDatabase(DbName).GetCollection<MongoLoginDao>($"{tenantId}-users");

            MongoLoginDao loginDao = await loginCollection.Find(Builders<MongoLoginDao>.Filter.Eq(u => u.Id, id)).FirstOrDefaultAsync(cancellationToken);
            return loginDao;
        }

        public async Task<IEnumerable<MongoLoginDao>> GetLoginsAsync(string tenantId, LoginWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            IMongoCollection<MongoLoginDao> loginCollection = client.GetDatabase(DbName).GetCollection<MongoLoginDao>($"{tenantId}-users");

            // where
            FilterDefinition<MongoLoginDao> mongoFilter = FilterDefinition<MongoLoginDao>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IFindFluent<MongoLoginDao, MongoLoginDao> find = loginCollection.Find(mongoFilter);

            // orderby
            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<MongoLoginDao>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<MongoLoginDao>.Sort.Descending(orderBy.FieldName));

            IEnumerable<MongoLoginDao> loginDao = await find.Skip(skip).Limit(first).ToListAsync(cancellationToken);
            return loginDao;
        }

        public async Task<long> GetLoginTotalCount(string tenantId, LoginWhere where, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            IMongoCollection<MongoLoginDao> loginCollection = client.GetDatabase(DbName).GetCollection<MongoLoginDao>($"{tenantId}-users");

            // where
            FilterDefinition<MongoLoginDao> mongoFilter = FilterDefinition<MongoLoginDao>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IFindFluent<MongoLoginDao, MongoLoginDao> find = loginCollection.Find(mongoFilter);

            long count = await find.CountDocumentsAsync(cancellationToken);
            return count;
        }

        public async Task<Result> DeleteTargetGroupAsync(string tenantId, string id, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<TargetGroupDao>($"{tenantId}-targetGroups").DeleteOneAsync(p => p.Id == ObjectId.Parse(id), cancellationToken);

            return new Result { Status = "success" };

        }
        public async Task<IEnumerable<TargetGroup>> GetAllTargetGroups(string tenantId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            return (await client.GetDatabase(DbName).GetCollection<TargetGroupDao>($"{tenantId}-targetGroups").Find(f => true).ToListAsync(cancellationToken))
            .Select(tg => tg.ToDomain());
        }

        public async Task<TargetGroup> GetTargetGroupAsync(string tenantId, string id, CancellationToken cancellationToken) =>
            (await MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName)
                .GetCollection<TargetGroupDao>($"{tenantId}-targetGroups").Find(f => f.Id == ObjectId.Parse(id)).SingleOrDefaultAsync(cancellationToken))?.ToDomain();

        public async Task<Result<string>> CreateTargetGroupAsync(string tenantId, CreateTargetGroupCommand command, CancellationToken cancellationToken)
        {
            var targetGroupId = ObjectId.GenerateNewId();
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<TargetGroupDao>($"{tenantId}-targetGroups")
                .InsertOneAsync(new TargetGroupDao { Id = targetGroupId, Name = command.Name, Description = command.Description }, cancellationToken: cancellationToken);

            return new Result<string> { Status = "success", Value = targetGroupId.ToString() };
        }

        public async Task<Result> UpdateTargetGroupAsync(string tenantId, string id, UpdateTargetGroupCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<TargetGroupDao>($"{tenantId}-targetGroups")
            .UpdateOneAsync(
                Builders<TargetGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<TargetGroupDao>.Update
                    .Set(t => t.Name, command.Name)
                    .Set(t => t.Description, command.Description),
                        cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> AddUserToTargetGroupAsync(string tenantId, string id, string targetId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<TargetGroupDao>($"{tenantId}-targetGroups").UpdateOneAsync(
                      Builders<TargetGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                      Builders<TargetGroupDao>.Update.Push(e => e.TargetIds, targetId),
                      cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> AddTargetGroupToTargetGroupAsync(string tenantId, string id, string targetGroupId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));


            await client.GetDatabase(DbName).GetCollection<TargetGroupDao>($"{tenantId}-targetGroups").UpdateOneAsync(
                Builders<TargetGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<TargetGroupDao>.Update.Push(e => e.TargetGroupIds, targetGroupId),
                cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> RemoveUserFromTargetGroupAsync(string tenantId, string id, string targetId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));


            await client.GetDatabase(DbName).GetCollection<TargetGroupDao>($"{tenantId}-targetGroups").UpdateOneAsync(
                    Builders<TargetGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                    Builders<TargetGroupDao>.Update.Pull(e => e.TargetIds, targetId),
                    cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> RemoveTargetGroupFromTargetGroupAsync(string tenantId, string id, string targetGroupId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<TargetGroupDao>($"{tenantId}-targetGroups").UpdateOneAsync(
                Builders<TargetGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<TargetGroupDao>.Update.Pull(e => e.TargetGroupIds, targetGroupId),
                cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> DeletePermissionGroupAsync(string tenantId, string id, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").DeleteOneAsync(p => p.Id == ObjectId.Parse(id), cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<PermissionGroup> GetPermissionGroupAsync(string tenantId, string id, CancellationToken cancellationToken)
        {
            if (ObjectId.TryParse(id, out ObjectId objectId))
            {
                MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

                return (await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups")
                    .Find(f => f.Id == objectId).SingleOrDefaultAsync(cancellationToken))?.ToDomain();
            }
            return null;
        }

        public async Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId, IEnumerable<string> ids, CancellationToken cancellationToken) =>
            (await MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName)
                .GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups")
                .Find(Builders<PermissionGroupDao>.Filter.In(f => f.Id, ids.Select(id => ObjectId.Parse(id))))?.ToListAsync(cancellationToken))?.Select(pg => pg?.ToDomain());

        public async Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId, PermissionGroupWhere where, CancellationToken cancellationToken)
        {
            FilterDefinition<PermissionGroupDao> mongoFilter = FilterDefinition<PermissionGroupDao>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            List<PermissionGroupDao> permissionGroups = await client
                .GetDatabase(DbName)
                .GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups")
                .Find(mongoFilter)
                .ToListAsync(cancellationToken);

            return permissionGroups.Select(pg => pg.ToDomain());
        }

        public async Task<IEnumerable<PermissionGroup>> GetPermissionGroupsByCreatorIdAsync(string tenantId, string creatorId, CancellationToken cancellationToken) => (
            await MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName)
                .GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups")
                .Find(Builders<PermissionGroupDao>.Filter.Eq(f => f.CreatedById, creatorId)).ToListAsync(cancellationToken))?.Select(pg => pg?.ToDomain());

        public async Task<PermissionGroup> GetDefaultPermissionGroupAsync(string tenantId, CancellationToken cancellationToken) =>
            (await MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName)
                .GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").Find(f => f.Name == "default").SingleOrDefaultAsync(cancellationToken))?.ToDomain();

        public async Task<IEnumerable<PermissionGroup>> GetAllPermissionGroupsAsync(string tenantId, CancellationToken cancellationToken) =>
            (await MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName)
                .GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").Find(f => true).ToListAsync(cancellationToken))
                .Select(tg => tg.ToDomain());

        public async Task<Result<string>> CreatePermissionGroupAsync(string tenantId, CreatePermissionGroupCommand command, CancellationToken cancellationToken)
        {
            var id = ObjectId.GenerateNewId();
            await MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName)
                .GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups")
                .InsertOneAsync(new PermissionGroupDao
                {
                    Id = id,
                    Name = command.Name,
                    Description = command.Description,
                    CreatedById = command.CreatedById,
                    LastModifiedById = command.CreatedById,
                    ProductTypes = command.ProductTypes,
                    CreatedAt = DateTime.UtcNow,
                    LastModifiedAt = DateTime.UtcNow
                },
                cancellationToken: cancellationToken);

            return new Result<string> { Status = "success", Value = id.ToString() };
        }

        public async Task<Result> UpdatePermissionGroupAsync(string tenantId, string id, UpdatePermissionGroupCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups")
                .UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update
                .Set(t => t.Name, command.Name)
                .Set(t => t.Description, command.Description)
                .Set(t => t.ProductTypes, command.ProductTypes)
                .Set(t => t.LastModifiedAt, DateTime.UtcNow)
                .Set(t => t.LastModifiedById, command.ModifiedById),
                cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> AddPermissionToPermissionGroupAsync(string tenantId, string id, AddPermissionToPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update.Push(e => e.TargettedPermissions, new TargettedPermissionDao { PermissionId = command.PermissionId, TargetId = command.TargetId }),
                cancellationToken: cancellationToken);

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update
                    .Set(t => t.LastModifiedAt, DateTime.UtcNow)
                    .Set(t => t.LastModifiedById, command.AddedById),
                        cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> AddPermissionGroupToPermissionGroupAsync(string tenantId, string id, AddPermissionGroupToPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update.Push(e => e.PermissionGroupIds, command.PermissionGroupId),
                cancellationToken: cancellationToken);

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update
                    .Set(t => t.LastModifiedAt, DateTime.UtcNow)
                    .Set(t => t.LastModifiedById, command.AddedById),
                        cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> RemovePermissionFromPermissionGroupAsync(string tenantId, string id, RemovePermissionFromPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update.Pull(e => e.TargettedPermissions, new TargettedPermissionDao { PermissionId = command.PermissionId, TargetId = command.TargetId }),
                cancellationToken: cancellationToken);

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
            Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
            Builders<PermissionGroupDao>.Update
                .Set(t => t.LastModifiedAt, DateTime.UtcNow)
                .Set(t => t.LastModifiedById, command.RemovedById),
                    cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> RemovePemissionGroupFromPermissionGroupAsync(string tenantId, string id, RemovePermissionGroupFromPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update.Pull(e => e.PermissionGroupIds, command.PermissionGroupId),
                cancellationToken: cancellationToken);

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update
                    .Set(t => t.LastModifiedAt, DateTime.UtcNow)
                    .Set(t => t.LastModifiedById, command.RemovedById),
                        cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> AddLoginPermissionsToPermissionGroupAsync(string tenantId, string id, AddLoginPermissionsToPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update.Push(e => e.InheritedLoginIds, command.LoginId),
                cancellationToken: cancellationToken);

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update
                    .Set(t => t.LastModifiedAt, DateTime.UtcNow)
                    .Set(t => t.LastModifiedById, command.AddedById),
                        cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> RemoveLoginPermissionsFromPermissionGroupAsync(string tenantId, string id, RemoveLoginPermissionsFromPermissionGroupCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update.Pull(e => e.InheritedLoginIds, command.LoginId),
                cancellationToken: cancellationToken);

            await client.GetDatabase(DbName).GetCollection<PermissionGroupDao>($"{tenantId}-permissionGroups").UpdateOneAsync(
                Builders<PermissionGroupDao>.Filter.Eq(t => t.Id, ObjectId.Parse(id)),
                Builders<PermissionGroupDao>.Update
                    .Set(t => t.LastModifiedAt, DateTime.UtcNow)
                    .Set(t => t.LastModifiedById, command.RemovedById),
                        cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<PasswordValidators> GetPasswordValidatorsAsync(string tenantId, string clientId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            IMongoCollection<PasswordValidators> collection = client.GetDatabase(DbName).GetCollection<PasswordValidators>($"{tenantId}-passwordValidators");
            return await collection.Find(Builders<PasswordValidators>.Filter.Eq(e => e.ClientId, clientId)).SingleOrDefaultAsync(cancellationToken);
        }

        public async Task<IEnumerable<JToken>> GetIndexStats(string tenantId, string colName, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            List<MongoIndexStat> results = await (await client.GetDatabase(DbName)
                .GetCollection<MongoIndexStat>($"{tenantId}-{colName}")
                .AggregateAsync<MongoIndexStat>(new[] { new BsonDocument(new BsonElement("$indexStats", new BsonDocument())) },
                    cancellationToken: cancellationToken)).ToListAsync(cancellationToken);

            IEnumerable<JToken> indices = results.Select(r => JToken.FromObject(r));
            return indices;
        }

        public async Task<Result> UpdateLoginLockoutEndDate(string tenantId, string id, DateTime lockoutEndDate, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            IMongoCollection<MongoLoginDao> loginCollection = client.GetDatabase(DbName).GetCollection<MongoLoginDao>($"{tenantId}-users");

            await client.GetDatabase(DbName)
            .GetCollection<MongoLoginDao>($"{tenantId}-users").UpdateOneAsync(
                Builders<MongoLoginDao>.Filter.Eq(u => u.Id, id),
                Builders<MongoLoginDao>.Update.Set(u => u.LockoutEndDateUtc, lockoutEndDate),
                cancellationToken: cancellationToken
            );

            return new Result { Status = "success" };
        }

        public async Task<Result> AddTargetedPermissionSchemaToLoginForIds(string tenantId, AddTargetedPermissionSchemaToLoginCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            IMongoCollection<MongoLoginDao> loginCollection = client.GetDatabase(DbName).GetCollection<MongoLoginDao>($"{tenantId}-users");

            FilterDefinition<MongoLoginDao> loginFilter = Builders<MongoLoginDao>.Filter.Eq(u => u.Id, command.LoginId);

            MongoLoginDao login = await loginCollection.Find(loginFilter).FirstOrDefaultAsync(cancellationToken);

            FilterDefinition<TargetedPermissionSchema> filter =
                Builders<TargetedPermissionSchema>.Filter.Eq(t => t.PermissionSchemaId, command.PermissionSchemaId)
                & Builders<TargetedPermissionSchema>.Filter.In(t => t.Id, login.TargetedPermissionSchemaIds ?? new());

            IMongoCollection<TargetedPermissionSchema> targetedPermissionSchemaCollection = client.GetDatabase(DbName).GetCollection<TargetedPermissionSchema>($"{tenantId}-targetedPermissionSchemas");

            if (await targetedPermissionSchemaCollection.Find(filter).AnyAsync(cancellationToken))
                await targetedPermissionSchemaCollection.UpdateOneAsync(filter, Builders<TargetedPermissionSchema>.Update
                    .Set(e => e.LastModifiedAt, DateTime.Now)
                    .Set(e => e.LastModifiedById, command.AddedById)
                    .PushEach(e => e.TargetIds, command.TargetIds),
                        cancellationToken: cancellationToken);
            else
            {
                string id = Guid.NewGuid().ToString();
                await targetedPermissionSchemaCollection.InsertOneAsync(new()
                {
                    Id = id,
                    TargetIds = command.TargetIds,
                    PermissionSchemaId = command.PermissionSchemaId,
                    CreatedAt = DateTime.Now,
                    LastModifiedAt = DateTime.Now,
                    CreatedById = command.AddedById,
                    LastModifiedById = command.AddedById
                }, cancellationToken: cancellationToken);

                await loginCollection.UpdateOneAsync(loginFilter, Builders<MongoLoginDao>.Update
                    .Set(e => e.LastModifiedAt, DateTime.Now)
                    .Set(e => e.LastModifiedById, command.AddedById)
                    .Push(e => e.TargetedPermissionSchemaIds, id),
                        cancellationToken: cancellationToken);
            }

            return new() { Status = "success" };
        }

        public async Task<Result> RemoveTargetedPermissionSchemaFromLoginForIds(string tenantId, RemoveTargetedPermissionSchemaFromLoginCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            IMongoCollection<MongoLoginDao> loginCollection = client.GetDatabase(DbName).GetCollection<MongoLoginDao>($"{tenantId}-users");

            FilterDefinition<MongoLoginDao> loginFilter = Builders<MongoLoginDao>.Filter.Eq(u => u.Id, command.LoginId);

            MongoLoginDao login = await loginCollection.Find(loginFilter).FirstOrDefaultAsync(cancellationToken);

            FilterDefinition<TargetedPermissionSchema> filter =
                Builders<TargetedPermissionSchema>.Filter.Eq(t => t.PermissionSchemaId, command.PermissionSchemaId)
                & Builders<TargetedPermissionSchema>.Filter.In(t => t.Id, login.TargetedPermissionSchemaIds ?? new());

            IMongoCollection<TargetedPermissionSchema> targetedPermissionSchemaCollection = client.GetDatabase(DbName).GetCollection<TargetedPermissionSchema>($"{tenantId}-targetedPermissionSchemas");

            if (await targetedPermissionSchemaCollection.Find(filter).AnyAsync(cancellationToken))
                await targetedPermissionSchemaCollection.UpdateOneAsync(filter, Builders<TargetedPermissionSchema>.Update
                    .Set(e => e.LastModifiedAt, DateTime.Now)
                    .Set(e => e.LastModifiedById, command.RemovedById)
                    .PullAll(e => e.TargetIds, command.TargetIds), cancellationToken: cancellationToken);
            else
                return new() { Status = "failure", Errors = new List<string> { $"The login {command.LoginId} does not have targeted permission with permission schema id {command.PermissionSchemaId}" } };

            return new() { Status = "success" };
        }

        public async Task<IReadOnlyCollection<TargetedPermissionSchema>> GetTargetedPermissionSchemas(string tenantId, IReadOnlyCollection<string> targetedPermissionSchemaIds, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));

            IMongoCollection<TargetedPermissionSchema> targetedPermissionSchemaCollection = client.GetDatabase(DbName).GetCollection<TargetedPermissionSchema>($"{tenantId}-targetedPermissionSchemas");

            FilterDefinition<TargetedPermissionSchema> filter = Builders<TargetedPermissionSchema>.Filter.In(t => t.Id, targetedPermissionSchemaIds);

            return await targetedPermissionSchemaCollection.Find(filter).ToListAsync(cancellationToken);
        }

        public async Task<Result> UpdateLatestOtpAsync(string tenantId, string loginId, string latestOtp, CancellationToken cancellationToken)
        {
            IMongoCollection<MongoLoginDao> collection = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId)).GetDatabase(DbName).GetCollection<MongoLoginDao>($"{tenantId}-users");

            FilterDefinition<MongoLoginDao> filter = Builders<MongoLoginDao>.Filter.Eq(x => x.Id, loginId);
            UpdateDefinition<MongoLoginDao> updateDefinition = Builders<MongoLoginDao>.Update.Set(x => x.LatestOtp, latestOtp);

            try
            {
                UpdateResult result = await collection.UpdateOneAsync(
                    filter,
                    updateDefinition,
                    new UpdateOptions() { IsUpsert = true },
                    cancellationToken);

                return result.MatchedCount > 0
                    ? Result.Success()
                    : Result.Failure($"No login with id {loginId} to update.");
            }
            catch (Exception ex)
            {
                return Result.Failure($"Unable to update login with id {loginId} ||| {ex.Message}");
            }
        }

        public Task<Result> UpdatePasswordValidatorsAsync(string tenantId, string clientId, UpdatePasswordValidatorsCommand command, CancellationToken cancellationToken) => throw new NotImplementedException();

        public class MongoIndexStat
        {
            public string Name { get; set; }
            public string Host { get; set; }
            public MongoAccesses Accesses { get; set; }
        }
        public class MongoAccesses
        {
            public long Ops { get; set; }
            public DateTime Since { get; set; }
        }
    }

    [BsonIgnoreExtraElements]
    public class TargetGroupDao
    {
        public ObjectId Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        [BsonIgnoreIfDefault]
        public IEnumerable<string> TargetIds { get; set; }
        [BsonIgnoreIfDefault]
        public IEnumerable<string> TargetGroupIds { get; set; }
    }

    public static class TargetGroupExtensions
    {
        public static TargetGroup ToDomain(this TargetGroupDao dao) =>
            new TargetGroup
            {
                Id = dao.Id.ToString(),
                Name = dao.Name,
                Description = dao.Description,
                TargetIds = dao.TargetIds,
                TargetGroupIds = dao.TargetGroupIds
            };
    }

    [BsonIgnoreExtraElements]
    public class PermissionGroupDao
    {
        public ObjectId Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastModifiedAt { get; set; }
        public string CreatedById { get; set; }
        public string LastModifiedById { get; set; }
        public IEnumerable<string> ProductTypes { get; set; }
        [BsonIgnoreIfDefault]
        public IEnumerable<TargettedPermissionDao> TargettedPermissions { get; set; }
        [BsonIgnoreIfDefault]
        public IEnumerable<string> PermissionGroupIds { get; set; }
        [BsonIgnoreIfDefault]
        public IEnumerable<string> InheritedLoginIds { get; set; }
    }

    public static class PermissionGroupExtensions
    {
        public static PermissionGroup ToDomain(this PermissionGroupDao dao) =>
            new PermissionGroup
            {
                Id = dao.Id.ToString(),
                Name = dao.Name,
                Description = dao.Description,
                ProductTypes = dao.ProductTypes,
                TargettedPermissions = dao.TargettedPermissions?.GroupBy(tp => tp.PermissionId).ToDictionary(g => g.Key, g => g.Select(x => x.TargetId)),
                PermissionGroupIds = dao.PermissionGroupIds,
                InheritedLoginIds = dao.InheritedLoginIds,
                CreatedById = dao.CreatedById,
                LastModifiedAt = dao.LastModifiedAt,
                CreatedAt = dao.CreatedAt,
                LastModifiedById = dao.LastModifiedById
            };
    }

    [BsonIgnoreExtraElements]
    public class TargettedPermissionDao
    {
        public string PermissionId { get; set; }
        public string TargetId { get; set; }
    }

    public static class MongoIdentityServerBuilderExtensions
    {
        public static IIdentityServerBuilder AddMongoForAspIdentity<TIdentity, TRole>(
            this IIdentityServerBuilder builder,
            IMongoDatabase database,
            string tenantId,
            bool requireConfirmedEmail = false,
            bool requireConfirmPhoneNumber = false,
            bool requireDigit = false,
            int requireLength = 0,
            int requireUniqueChars = 1,
            bool requireLowercase = false,
            bool requireUppercase = false,
            bool requireNonAlphanumeric = false,
            bool enableLockout = false,
            int lockoutTimespanInSeconds = 300,
            int lockoutMaxFailedAccessAttempts = 5,
            bool requireLetter = false,
            bool notContainUsername = false,
            int? email2faLifespanInSeconds = null)
            where TIdentity : MongoLoginDao
            where TRole : Microsoft.AspNetCore.Identity.MongoDB.IdentityRole
        {
            //var conventionPack = new ConventionPack { new CamelCaseElementNameConvention(), new IgnoreExtraElementsConvention(true) };
            //ConventionRegistry.Register("auth conventions", conventionPack, t => true);

            IMongoCollection<TIdentity> userCollection = database.GetCollection<TIdentity>($"{tenantId}-users");
            IMongoCollection<TRole> roleCollection = database.GetCollection<TRole>($"{tenantId}-roles");

            IdentityBuilder identityBuilder = builder.Services.AddIdentity<TIdentity, TRole>(config =>
            {
                config.SignIn.RequireConfirmedEmail = requireConfirmedEmail;
                config.SignIn.RequireConfirmedPhoneNumber = requireConfirmPhoneNumber;

                config.Password.RequireDigit = requireDigit;
                config.Password.RequiredLength = requireLength;

                config.Password.RequiredUniqueChars = requireUniqueChars;
                config.Password.RequireLowercase = requireLowercase;

                config.Password.RequireUppercase = requireUppercase;
                config.Password.RequireNonAlphanumeric = requireNonAlphanumeric;

                config.Lockout.AllowedForNewUsers = enableLockout;
                config.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromSeconds(lockoutTimespanInSeconds);
                config.Lockout.MaxFailedAccessAttempts = lockoutMaxFailedAccessAttempts;

                config.Tokens.ProviderMap[nameof(CustomEmailConfirmationTokenProvider)]
                    = new TokenProviderDescriptor(typeof(CustomEmailConfirmationTokenProvider));
                config.Tokens.EmailConfirmationTokenProvider = nameof(CustomEmailConfirmationTokenProvider);

                config.Tokens.ProviderMap[nameof(CustomPasswordResetTokenProvider)]
                    = new TokenProviderDescriptor(typeof(CustomPasswordResetTokenProvider));
                config.Tokens.PasswordResetTokenProvider = nameof(CustomPasswordResetTokenProvider);
            });

            if (requireLetter)
                identityBuilder.AddPasswordValidator<UsernameAsPasswordValidator<TIdentity>>();
            if (notContainUsername)
                identityBuilder.AddPasswordValidator<NotContainUsernamePasswordValidator<TIdentity>>();

            identityBuilder
                .AddDefaultTokenProviders()
                .AddTokenProvider(TokenOptions.DefaultEmailProvider, typeof(CustomEmailTokenProvider<>).MakeGenericType(identityBuilder.UserType))
                .RegisterMongoStores(p => userCollection, p => roleCollection);

            return builder;
        }
    }

    public static class FilterDefinitionExtensions
    {
        public static FilterDefinition<MongoLoginDao> GetFilterDefinition(this LoginWhere where)
        {
            FilterDefinition<MongoLoginDao> mongoFilter = FilterDefinition<MongoLoginDao>.Empty;

            if (where.Or != null)
                foreach (LoginWhere orFilter in where.Or)
                    mongoFilter = mongoFilter != FilterDefinition<MongoLoginDao>.Empty
                        ? mongoFilter | GetFilterDefinition(orFilter)
                        : GetFilterDefinition(orFilter);

            else if (where.And != null)
                foreach (LoginWhere andFilter in where.And)
                    mongoFilter &= GetFilterDefinition(andFilter);

            else
                mongoFilter = mongoFilter.AddToFilterDefinition(where);

            return mongoFilter;
        }

        public static FilterDefinition<MongoLoginDao> AddToFilterDefinition(this FilterDefinition<MongoLoginDao> mongoFilter, LoginWhere where)
        {
            FilterDefinitionBuilder<MongoLoginDao> defBuilder = Builders<MongoLoginDao>.Filter;

            //is a bit cleaner to be if else rather than all ifs and using `OR` and `AND` logical operators but that will require FE to migrate
            if (where.Ids != null)
            {
                where.Ids = where.Ids?.Where(i => ObjectId.TryParse(i, out ObjectId objectId));
                mongoFilter &= defBuilder.In(p => p.Id, where.Ids);
            }

            if (where.Usernames != null)
                mongoFilter &= defBuilder.In(p => p.UserName, where.Usernames);

            if (where.Username_contains != null)
                mongoFilter &= defBuilder.Regex(p => p.UserName, new BsonRegularExpression(Regex.Escape(where.Username_contains)));

            if (where.EntityIds != null)
                mongoFilter &= defBuilder.In(p => p.EntityId, where.EntityIds);

            if (where.EntityTypes != null)
                mongoFilter &= defBuilder.In(p => p.EntityType, where.EntityTypes);

            if (where.Email_in != null)
            {
                var emailFilter = defBuilder.In(p => p.Email, where.Email_in);
                emailFilter |= defBuilder.In(p => p.NormalizedEmail, where.Email_in);

                mongoFilter &= emailFilter;
            }
            if (where.IsEmailConfirmed != null)
                mongoFilter &= defBuilder.Eq(p => p.EmailConfirmed, where.IsEmailConfirmed);

            if (where.IsTelephoneNumberConfirmed != null)
                mongoFilter &= defBuilder.Eq(p => p.PhoneNumberConfirmed, where.IsTelephoneNumberConfirmed);

            if (where.IsActive != null)
                mongoFilter &= defBuilder.Eq(p => p.IsActive, where.IsActive);

            if (where.PermissionGroupIds != null)
                mongoFilter &= defBuilder.Where(l => l.Claims.Any(c => c.Type == "groups" && where.PermissionGroupIds.Contains(c.Value)));

            if (where.LastModifiedAt_gt != null)
                mongoFilter &= defBuilder.Gt(p => p.LastModifiedAt, where.LastModifiedAt_gt);
            if (where.LastModifiedAt_lt != null)
                mongoFilter &= defBuilder.Lt(p => p.LastModifiedAt, where.LastModifiedAt_lt);

            if (where.CreatedAt_gt != null)
                mongoFilter &= defBuilder.Gt(p => p.CreatedAt, where.CreatedAt_gt);
            if (where.CreatedAt_lt != null)
                mongoFilter &= defBuilder.Lt(p => p.CreatedAt, where.CreatedAt_lt);

            if (where.LastModifiedById != null)
                mongoFilter &= defBuilder.Eq(p => p.LastModifiedById, where.LastModifiedById);
            if (where.LastModifiedById_in != null)
                mongoFilter &= defBuilder.In(p => p.LastModifiedById, where.LastModifiedById_in);

            if (where.CreatedById != null)
                mongoFilter &= defBuilder.Eq(p => p.CreatedById, where.CreatedById);
            if (where.CreatedById_in != null)
                mongoFilter &= defBuilder.In(p => p.CreatedById, where.CreatedById_in);

            if (where.PasswordLastUpdated_gt != null)
                mongoFilter &= defBuilder.Gt(p => p.PasswordLastUpdated, where.PasswordLastUpdated_gt);
            if (where.PasswordLastUpdated_lt != null)
                mongoFilter &= defBuilder.Lt(p => p.PasswordLastUpdated, where.PasswordLastUpdated_lt);

            return mongoFilter;
        }

        public static FilterDefinition<AppDao> GetFilterDefinition(this AppWhere where)
        {
            FilterDefinition<AppDao> mongoFilter = FilterDefinition<AppDao>.Empty;

            if (where.Or != null)
                foreach (AppWhere orFilter in where.Or)
                    mongoFilter = mongoFilter != FilterDefinition<AppDao>.Empty
                        ? mongoFilter | GetFilterDefinition(orFilter)
                        : GetFilterDefinition(orFilter);

            else if (where.And != null)
                foreach (AppWhere andFilter in where.And)
                    mongoFilter &= GetFilterDefinition(andFilter);

            else
                mongoFilter = mongoFilter.AddToFilterDefinition(where);

            return mongoFilter;
        }

        public static FilterDefinition<AppDao> AddToFilterDefinition(this FilterDefinition<AppDao> mongoFilter, AppWhere where)
        {
            FilterDefinitionBuilder<AppDao> defBuilder = Builders<AppDao>.Filter;
            if (where.AppId != null)
                return mongoFilter & defBuilder.Eq(p => p.ClientId, where.AppId);
            if (where.AppId_in != null)
                return mongoFilter & defBuilder.In(p => p.ClientId, where.AppId_in);

            if (where.LastModifiedAt_gt != null)
                return mongoFilter & defBuilder.Gt(p => p.LastModifiedAt, where.LastModifiedAt_gt);
            if (where.LastModifiedAt_lt != null)
                return mongoFilter & defBuilder.Lt(p => p.LastModifiedAt, where.LastModifiedAt_lt);

            if (where.CreatedAt_gt != null)
                return mongoFilter & defBuilder.Gt(p => p.CreatedAt, where.CreatedAt_gt);
            if (where.CreatedAt_lt != null)
                return mongoFilter & defBuilder.Lt(p => p.CreatedAt, where.CreatedAt_lt);

            if (where.LastModifiedById != null)
                return mongoFilter & defBuilder.Eq(p => p.LastModifiedById, where.LastModifiedById);
            if (where.LastModifiedById_in != null)
                return mongoFilter & defBuilder.In(p => p.LastModifiedById, where.LastModifiedById_in);

            if (where.CreatedById != null)
                return mongoFilter & defBuilder.Eq(p => p.CreatedById, where.CreatedById);
            if (where.CreatedById_in != null)
                return mongoFilter & defBuilder.In(p => p.CreatedById, where.CreatedById_in);

            return mongoFilter;
        }

        public static FilterDefinition<PermissionGroupDao> GetFilterDefinition(this PermissionGroupWhere where)
        {
            FilterDefinition<PermissionGroupDao> mongoFilter = FilterDefinition<PermissionGroupDao>.Empty;

            if (where.Or != null)
                foreach (PermissionGroupWhere orFilter in where.Or)
                    mongoFilter = mongoFilter != FilterDefinition<PermissionGroupDao>.Empty
                        ? mongoFilter | GetFilterDefinition(orFilter)
                        : GetFilterDefinition(orFilter);

            else if (where.And != null)
                foreach (PermissionGroupWhere andFilter in where.And)
                    mongoFilter &= GetFilterDefinition(andFilter);

            else
                mongoFilter = mongoFilter.AddToFilterDefinition(where);

            return mongoFilter;
        }

        public static FilterDefinition<PermissionGroupDao> AddToFilterDefinition(this FilterDefinition<PermissionGroupDao> mongoFilter, PermissionGroupWhere where)
        {
            FilterDefinitionBuilder<PermissionGroupDao> defBuilder = Builders<PermissionGroupDao>.Filter;
            if (where.Id != null)
                return mongoFilter & defBuilder.Eq(p => p.Id, ObjectId.Parse(where.Id));
            if (where.Id_in != null)
            {
                IEnumerable<ObjectId> objectIds = where.Id_in.Select(id => ObjectId.TryParse(id, out ObjectId objId) ? objId : new ObjectId());
                return mongoFilter &= defBuilder.In(c => c.Id, objectIds);
            }
            if (where.Name != null)
                return mongoFilter & defBuilder.Eq(p => p.Name, where.Name);
            if (where.Name_in != null)
                return mongoFilter & defBuilder.In(p => p.Name, where.Name_in);

            if (where.LastModifiedAt_gt != null)
                return mongoFilter & defBuilder.Gt(p => p.LastModifiedAt, where.LastModifiedAt_gt);
            if (where.LastModifiedAt_lt != null)
                return mongoFilter & defBuilder.Lt(p => p.LastModifiedAt, where.LastModifiedAt_lt);

            if (where.CreatedAt_gt != null)
                return mongoFilter & defBuilder.Gt(p => p.CreatedAt, where.CreatedAt_gt);
            if (where.CreatedAt_lt != null)
                return mongoFilter & defBuilder.Lt(p => p.CreatedAt, where.CreatedAt_lt);

            if (where.LastModifiedById != null)
                return mongoFilter & defBuilder.Eq(p => p.LastModifiedById, where.LastModifiedById);
            if (where.LastModifiedById_in != null)
                return mongoFilter & defBuilder.In(p => p.LastModifiedById, where.LastModifiedById_in);

            if (where.CreatedById != null)
                return mongoFilter & defBuilder.Eq(p => p.CreatedById, where.CreatedById);
            if (where.CreatedById_in != null)
                return mongoFilter & defBuilder.In(p => p.CreatedById, where.CreatedById_in);

            return mongoFilter;
        }
    }
}
