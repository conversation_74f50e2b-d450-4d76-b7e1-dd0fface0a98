﻿#nullable enable
using CoverGo.Auth.Domain;
using IdentityServer4.Models;
using IdentityServer4.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace CoverGo.Auth.Infrastructure.Services
{
    public class CustomProfileService : DefaultProfileService
    {
        readonly IAuthService? _authService;
        readonly ILogger<CustomProfileService> _logger;

        const string GroupsClaimType = "groups";
        const string RoleClaimType = "role";
        const string TenantIdClaimType = "tenantId";
        public const string PermissionGroupsClaimType = "permissionGroups";

        public CustomProfileService(ILogger<CustomProfileService> logger, IAuthService authService)
            : base(logger)
        {
            _authService = authService;
            _logger = logger;
        }

        public override async Task GetProfileDataAsync(ProfileDataRequestContext context)
        {
            await base.GetProfileDataAsync(context);

            _logger.LogInformation("Processing GetProfileDataAsync in CustomProfileService.");

            try
            {
                string? tenantId = GetTenantId(context);
                if (string.IsNullOrWhiteSpace(tenantId))
                {
                    _logger.LogWarning("Tenant ID is null or empty.");
                    return;
                }

                Claim? roleClaim = GetRoleClaim(context);
                IEnumerable<PermissionGroup> groups;
                bool isAdmin = roleClaim is { Value: "admin" };
                if (isAdmin)
                {
                    groups = await GetPermissionGroupsAsync(tenantId, Enumerable.Empty<string>(), true);
                }
                else
                {
                    IEnumerable<string>? groupsClaim = GetGroupsClaim(context);
                    if (groupsClaim == null || !groupsClaim.Any())
                    {
                        _logger.LogInformation("No group claims found for the subject.");
                        return;
                    }

                    groups = await GetPermissionGroupsAsync(tenantId, groupsClaim);
                    if (!groups.Any())
                    {
                        _logger.LogInformation("No permission groups found for the tenant ID {TenantId}.", tenantId);
                        return;
                    }
                }

                AddPermissionGroupsClaim(context, groups);
                _logger.LogInformation(
                    "Permission groups successfully added to the issued claims for Tenant ID {TenantId}.", tenantId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing GetProfileDataAsync in CustomProfileService.");
            }
        }

        static IList<string>? GetGroupsClaim(ProfileDataRequestContext context) =>
            context.Subject?.Claims
                .Where(c => c.Type == GroupsClaimType)
                .Select(c => c.Value)
                .ToList();

        static Claim? GetRoleClaim(ProfileDataRequestContext context) =>
            context.Subject?.Claims
                .FirstOrDefault(c => c.Type == RoleClaimType);

        static string? GetTenantId(ProfileDataRequestContext context) =>
            context.Subject?.Claims
                .FirstOrDefault(c => c.Type == TenantIdClaimType)?.Value;

        async Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId,
            IEnumerable<string> groupsClaim, bool isAdmin = false)
        {
            if (_authService != null)
            {
                if (isAdmin)
                {
                    return await _authService.GetPermissionGroupsAsync(tenantId, default);
                }

                return await _authService.GetPermissionGroupsAsync(tenantId, groupsClaim, default);
            }

            _logger.LogError("AuthService is null when attempting to get permission groups.");
            return Enumerable.Empty<PermissionGroup>();
        }

        static void AddPermissionGroupsClaim(ProfileDataRequestContext context, IEnumerable<PermissionGroup> groups) =>
            context.IssuedClaims.Add(new Claim(PermissionGroupsClaimType,
                string.Join(',', groups.Select(g => g.Name))));
    }
}
