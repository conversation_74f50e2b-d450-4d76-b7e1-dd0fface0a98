﻿using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;

namespace CoverGo.Auth.LDAP
{
    public class LDAPConfig
    {
        private static readonly ConcurrentDictionary<string, LDAPConfig> _configs = new();
        public string Url { get; set; }
        public string Port { get; set; }
        public string BindDn { get; set; }
        public string BindCredentials { get; set; }
        public string SearchBase { get; set; }
        public string SearchFilter { get; set; }
        public bool Ssl { get; set; } = true;
        public static void AddConfig(string tenantId, LDAPConfig config) => _configs.TryAdd(tenantId, config);
        public static LDAPConfig GetConfig(string tenantId)
        {
            _configs.TryGetValue(tenantId, out LDAPConfig ldapConfig);
            return ldapConfig;
        }
    }

    public static class LDAPConfigExtensions
    {
        public static LDAPConfig Load<T>(this LDAPConfig cfg, string prefix = "LDAPCONFIG_") where T : class
        {
            EnvironmentVariablesExtensions.AddEnvironmentVariables(
                new ConfigurationBuilder().AddUserSecrets<T>(),
                s => s.Prefix = prefix)
                .Build()
                .Bind(cfg);

            return cfg;
        }

        public static void LoadIntoConfigurationSection(this LDAPConfig cfg, IConfiguration configuration)
        {
            configuration.GetSection("LDAPConnection")["Url"] = cfg.Url;
            configuration.GetSection("LDAPConnection")["Port"] = cfg.Port;
            configuration.GetSection("LDAPConnection")["BindDn"] = cfg.BindDn;
            configuration.GetSection("LDAPConnection")["BindCredentials"] = cfg.BindCredentials;
            configuration.GetSection("LDAPConnection")["SearchBase"] = cfg.SearchBase;
            configuration.GetSection("LDAPConnection")["SearchFilter"] = cfg.SearchFilter;
            configuration.GetSection("LDAPConnection")["Ssl"] = cfg.Ssl.ToString();
        }
    }
}
