﻿using IdentityServer.LdapExtension;
using IdentityServer.LdapExtension.Extensions;
using IdentityServer.LdapExtension.UserModel;
using IdentityServer.LdapExtension.UserStore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Auth.LDAP
{
    public static class CustomAddLdapUsersExtension
    {
        public static IIdentityServerBuilder CustomAddLdapUsers<TUserDetails>(
          this IIdentityServerBuilder builder,
          IConfiguration configuration,
          UserStore userStore)
          where TUserDetails : IAppUser, new()
        {
            RegisterLdapConfigurations(builder, configuration);
            builder.Services.AddSingleton<ILdapService<TUserDetails>, CustomLdapService<TUserDetails>>();
            if (userStore == UserStore.InMemory)
                builder.Services.AddSingleton<ILdapUserStore, InMemoryUserStore<TUserDetails>>();
            else
                builder.Services.AddSingleton<ILdapUserStore, RedisUserStore<TUserDetails>>();
            builder.AddProfileService<LdapUserProfileService<TUserDetails>>();
            builder.AddResourceOwnerValidator<LdapUserResourceOwnerPasswordValidator<TUserDetails>>();
            return builder;
        }

        public static IIdentityServerBuilder CustomAddLdapUsers<TUserDetails, TCustomUserStore>(
          this IIdentityServerBuilder builder,
          IConfiguration configuration)
          where TUserDetails : IAppUser, new()
          where TCustomUserStore : ILdapUserStore
        {
            RegisterLdapConfigurations(builder, configuration);
            builder.Services.AddSingleton<ILdapService<TUserDetails>, CustomLdapService<TUserDetails>>();
            builder.Services.AddSingleton(typeof(TCustomUserStore));
            builder.Services.AddSingleton(serviceProvider => (ILdapUserStore)serviceProvider.GetService(typeof(TCustomUserStore)));
            builder.AddProfileService<LdapUserProfileService<TUserDetails>>();
            builder.AddResourceOwnerValidator<LdapUserResourceOwnerPasswordValidator<TUserDetails>>();
            return builder;
        }

        private static void RegisterLdapConfigurations(
          IIdentityServerBuilder builder,
          IConfiguration configuration)
        {
            var implementationInstance = (ExtensionConfig)configuration.Get(typeof(ExtensionConfig));
            ICollection<LdapConfig> connections = implementationInstance.Connections;
            int num;
            if (connections == null)
            {
                num = 1;
            }
            else
            {
                int count = connections.Count;
                num = 0;
            }
            if (num != 0)
            {
                var ldapConfig = (LdapConfig)configuration.Get(typeof(LdapConfig));
                implementationInstance.Redis = ldapConfig.Redis;
                implementationInstance.RefreshClaimsInSeconds = ldapConfig.RefreshClaimsInSeconds;
                implementationInstance.Connections = new List<LdapConfig>
                {
                    ldapConfig
                };
            }
            int configIndex = 0;
            implementationInstance.Connections.ToList().ForEach(f =>
            {
                ++configIndex;
                f.FriendlyName = !string.IsNullOrEmpty(f.FriendlyName) ? f.FriendlyName : $"Config #{configIndex}";
            });
            builder.Services.AddSingleton(implementationInstance);
        }

    }
}
