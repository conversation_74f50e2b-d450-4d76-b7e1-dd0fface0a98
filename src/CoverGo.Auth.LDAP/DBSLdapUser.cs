﻿using IdentityServer.LdapExtension.UserModel;
using Novell.Directory.Ldap;
using System;
using System.Collections.Generic;
using System.Security.Claims;

namespace CoverGo.Auth.LDAP
{
    public class DBSLdapUser : IAppUser
    {
        public void FillClaims(LdapEntry user)
        {
            Claims = new List<Claim>
            {
                GetClaimFromLdapAttributes(user, "memberOf", OpenLdapAttributes.MemberOf),
            };
            try
            {
                IEnumerator<string> stringValues = user.GetAttribute("memberOf").StringValues;
                while (stringValues.MoveNext())
                    Claims.Add(new Claim("groups", stringValues.Current));

                string stringValue = user.GetAttribute("memberOf").StringValue;
                if (stringValue != null)
                    Claims.Add(new Claim("groups", stringValue));
            }
            catch (Exception ex)
            {
            }
        }

        internal Claim GetClaimFromLdapAttributes(
            LdapEntry user,
            string claim,
            OpenLdapAttributes ldapAttribute)
        {
            string str = string.Empty;
            try
            {
                str = user.GetAttribute(ldapAttribute.ToDescriptionString()).StringValue;
                return new Claim(claim, str);
            }
            catch (Exception ex)
            {
            }
            return new Claim(claim, str);
        }

        public void SetBaseDetails(LdapEntry ldapEntry, string providerName)
        {
            Username = ldapEntry.GetAttribute("cn").StringValue;
            ProviderName = providerName;
            SubjectId = Username;
            ProviderSubjectId = Username;
            FillClaims(ldapEntry);
        }

        private string _subjectId;

        public string SubjectId
        {
            get => _subjectId ?? Username;
            set => _subjectId = value;
        }
        public string Username { get; set; }
        public string ProviderSubjectId { get; set; }
        public string ProviderName { get; set; }
        public bool IsActive
        {
            get => true;
            set
            {
            }
        }

        public string DisplayName { get; set; }
        public ICollection<Claim> Claims { get; set; }
        public string[] LdapAttributes => new[]
        {
            "cn",
            "memberOf",
            "displayName",
            "givenName",
            "sn",
            "description",
            "uid",
            "sAMAccountName",
            "groups"
        };
    }
}
