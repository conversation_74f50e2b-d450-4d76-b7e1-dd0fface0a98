﻿using IdentityModel;
using IdentityServer.LdapExtension.UserModel;
using IdentityServer.LdapExtension.UserStore;
using IdentityServer4.Validation;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Novell.Directory.Ldap;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace CoverGo.Auth.LDAP
{
    public class CustomLdapResourceOwnerPasswordValidator<TUser> : IResourceOwnerPasswordValidator
        where TUser : IAppUser, new()
    {
        private readonly ILogger _logger;
        private readonly ILdapUserStore _users;
        private readonly IHttpContextAccessor _accessor;

        public CustomLdapResourceOwnerPasswordValidator(
            ILdapUserStore users,
            IHttpContextAccessor accessor,
            ILogger<CustomLdapResourceOwnerPasswordValidator<DBSLdapUser>> logger)
        {
            _accessor = accessor;
            _users = users;
            _logger = logger;
        }

        public Task ValidateAsync(ResourceOwnerPasswordValidationContext context)
        {
            IAppUser user = _users.ValidateCredentials(context.UserName, context.Password);
            if (user != null)
                PopulateGrantValidationResult(context);

            return Task.CompletedTask;
        }

        private void PopulateGrantValidationResult(ResourceOwnerPasswordValidationContext context)
        {
            string tenantId = _accessor.HttpContext.Request.PathBase.Value.Remove(0, 1);
            var claims = new List<Claim>
            {
                new("tenantId", tenantId),
                new("appId", context.Request.ClientId)
            };

            context.Result = new GrantValidationResult(
                   context.UserName,
                   OidcConstants.AuthenticationMethods.Password,
                   claims);
        }

        private (LdapSearchResults Results, LdapConnection LdapConnection) DirectSearchUser(
            string username)
        {
            var config = LDAPConfig.GetConfig("dbs_uat");

            using (LdapConnection ldapConnection = new LdapConnection()
            {
                SecureSocketLayer = true
            })
            {
                ldapConnection.UserDefinedServerCertValidationDelegate += (sender, certificate, chain, errors) => true;
                ldapConnection.Connect(config.Url, int.Parse(config.Port));
                ldapConnection.Bind(config.BindDn, config.BindCredentials);
                string[] ldapAttributes = new TUser().LdapAttributes;
                string filter = string.Format(config.SearchFilter, (object)username);
                ILdapSearchResults ldapSearchResults = ldapConnection.Search(config.SearchBase, 2, filter, ldapAttributes, false);

                if (ldapSearchResults.HasMore())
                    return (ldapSearchResults as LdapSearchResults, ldapConnection);
            }
            throw new Exception("Login failed from custom validator.", (Exception)new Exception("User not found in any LDAP from custom validator."));
        }
    }
}