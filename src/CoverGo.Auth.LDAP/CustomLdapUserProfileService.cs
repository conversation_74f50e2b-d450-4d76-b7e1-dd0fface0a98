﻿using IdentityServer.LdapExtension.Extensions;
using IdentityServer.LdapExtension.UserModel;
using IdentityServer.LdapExtension.UserStore;
using IdentityServer4.Extensions;
using IdentityServer4.Models;
using IdentityServer4.Services;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Auth.LDAP
{
    public class CustomLdapUserProfileService<TUser> : IProfileService where TUser : IAppUser, new()
    {
        protected readonly ILogger Logger;
        protected readonly ILdapUserStore Users;

        public CustomLdapUserProfileService(
            ILdapUserStore users,
            ILogger<LdapUserProfileService<TUser>> logger)
        {
            Users = users;
            Logger = logger;
        }

        //implementation same as identity server default profile service
        public virtual Task GetProfileDataAsync(ProfileDataRequestContext context)
        {
            context.LogProfileRequest(Logger);
            context.AddRequestedClaims(context.Subject.Claims);
            context.LogIssuedClaims(Logger);
            Logger.LogInformation("Issued claims from custom profile service: {claims}", context.IssuedClaims.Select(c => c.Type));
            return Task.CompletedTask;
        }

        //implementation extracted from ldap extension
        public Task IsActiveAsync(IsActiveContext context)
        {
            IAppUser bySubjectId = Users.FindBySubjectId(context.Subject.GetSubjectId());
            context.IsActive = bySubjectId != null && bySubjectId.IsActive;

            return Task.CompletedTask;
        }
    }
}
