﻿using IdentityServer.LdapExtension.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Auth.LDAP
{
    public static class LDAPUserSourceExtensions
    {
        public static IIdentityServerBuilder UseLDAPForIdentityServer(this IIdentityServerBuilder builder, string tenantId)
        {
            var ldapConfig = LDAPConfig.GetConfig(tenantId);
            IConfigurationRoot configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();
            ldapConfig.LoadIntoConfigurationSection(configuration);

            builder
                .AddResourceOwnerValidator<CustomLdapResourceOwnerPasswordValidator<DBSLdapUser>>()
                .CustomAddLdapUsers<DBSLdapUser>(configuration.GetSection("LDAPConnection"), UserStore.InMemory)
                .Services
                .AddIdentity<DBSLdapUser, IdentityRole>()
                .AddDefaultTokenProviders();

            return builder;
        }
    }
}
