﻿using IdentityServer.LdapExtension;
using IdentityServer.LdapExtension.UserModel;
using Microsoft.Extensions.Logging;
using Novell.Directory.Ldap;
using System;
using System.Linq;
using System.Text.RegularExpressions;

namespace CoverGo.Auth.LDAP
{
    public class CustomLdapService<TUser> : ILdapService<TUser> where TUser : IAppUser, new()
    {
        private readonly ILogger<LdapService<TUser>> _logger;
        private readonly LdapConfig[] _config;

        public CustomLdapService(ExtensionConfig config, ILogger<LdapService<TUser>> logger)
        {
            _logger = logger;
            _config = config.Connections.ToArray();
        }

        public TUser Login(string username, string password) => Login(username, password, null);

        public TUser Login(string username, string password, string domain)
        {
            (LdapSearchResults results, LdapConnection ldapConnection) = SearchUser(username, domain);
            if (results.HasMore())
            {
                try
                {
                    LdapEntry ldapEntry = results.Next();
                    if (ldapEntry != null)
                    {
                        BindUser(ldapConnection, ldapEntry.Dn, password);
                        if (ldapConnection.Bound)
                        {
                            string providerName = !string.IsNullOrEmpty(domain) ? domain : "local";
                            var user = new TUser();
                            user.SetBaseDetails(ldapEntry, providerName);
                            ldapConnection.Disconnect();
                            return user;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogTrace(ex.Message);
                    _logger.LogTrace(ex.StackTrace);
                    throw new Exception("Login failed.", ex);
                }
            }
            ldapConnection.Disconnect();
            return default;
        }

        public TUser FindUser(string username) => FindUser(username, null);

        public TUser FindUser(string username, string domain)
        {
            (LdapSearchResults Results, LdapConnection LdapConnection) tuple = SearchUser(username, domain);
            try
            {
                LdapEntry ldapEntry = tuple.Results.Next();
                if (ldapEntry != null)
                {
                    string providerName = !string.IsNullOrEmpty(domain) ? domain : "local";
                    var user = new TUser();
                    user.SetBaseDetails(ldapEntry, providerName);
                    tuple.LdapConnection.Disconnect();
                    return user;
                }
            }
            catch (Exception ex)
            {
                _logger.LogTrace(new EventId(), ex, ex.Message);
            }
            tuple.LdapConnection.Disconnect();
            return default;
        }

        private (LdapSearchResults Results, LdapConnection LdapConnection) SearchUser(
          string username,
          string domain)
        {
            _logger.LogInformation($"[CustomLdapService] Search user #{username} on domain #{domain}", username, domain);
            var list = _config.Where(f => f.PreFilterRegex == null || new Regex(f.PreFilterRegex, RegexOptions.Compiled).IsMatch(username)).ToList();
            if (!string.IsNullOrEmpty(domain))
                list = list.Where(e => e.FriendlyName.Equals(domain)).ToList();
            if (list == null || !list.Any())
                throw new Exception("Login failed.", new Exception("No searchable LDAP"));
            foreach (LdapConfig ldapConfig in list)
            {
                using var ldapConnection = new LdapConnection
                {
                    SecureSocketLayer = ldapConfig.Ssl
                };
                if (ldapConnection.SecureSocketLayer)
                    ldapConnection.UserDefinedServerCertValidationDelegate += (sender, certificate, chain, errors) => true;
                ldapConnection.Connect(ldapConfig.Url, ldapConfig.Port);
                BindUser(ldapConnection, ldapConfig.BindDn, ldapConfig.BindCredentials);
                string[] ldapAttributes = new TUser().LdapAttributes;
                string filter = string.Format(ldapConfig.SearchFilter, username);
                ILdapSearchResults ldapSearchResults = ldapConnection.Search(ldapConfig.SearchBase, 2, filter, ldapAttributes, false, new LdapSearchConstraints { 
                    ReferralFollowing = true
                });
                if (ldapSearchResults.HasMore())
                    return (ldapSearchResults as LdapSearchResults, ldapConnection);
            }
            throw new Exception("Login failed.", new Exception("User not found in any LDAP."));
        }

        private void BindUser(LdapConnection ldapConnection, string dn, string passwd)
        {
            ldapConnection.Bind(dn, passwd, new LdapConstraints
            {
                ReferralFollowing = true
            });
        }
    }
}
