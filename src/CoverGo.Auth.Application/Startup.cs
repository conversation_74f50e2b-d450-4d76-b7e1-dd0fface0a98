using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Monitoring;
using CoverGo.Applications.Startup;
using CoverGo.Auth.Application;
using CoverGo.Auth.Application.Controllers;
using CoverGo.Auth.Application.Extensions;
using CoverGo.Auth.Application.GraphQl;
using CoverGo.Auth.Application.GraphQl.App;
using CoverGo.Auth.Application.GraphQl.SSO;
using CoverGo.Auth.Application.Middlewares;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Domain.Otp;
using CoverGo.Auth.Domain.PermissionSchemas;
using CoverGo.Auth.Domain.TokenProviders;
using CoverGo.Auth.Domain.UserStorage;
using CoverGo.Auth.Domain.Utils;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using CoverGo.Auth.Infrastructure.Adapters.Mongo.Otp;
using CoverGo.Auth.Infrastructure.Adapters.Mongo.PersistedGrants;
using CoverGo.Auth.Infrastructure.Adapters.Mongo.Repository;
using CoverGo.Auth.Infrastructure.OtherServices;
using CoverGo.Auth.Infrastructure.Services;
using CoverGo.Auth.LDAP;
using CoverGo.Configuration;
using CoverGo.MongoUtils;
using CoverGo.Policies.Client;
using CoverGo.Proxies.Gateway;

using HotChocolate;

using IdentityModel.Client;

using IdentityServer4.Models;
using IdentityServer4.Validation;

using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Microsoft.FeatureManagement;
using Microsoft.IdentityModel.Tokens;

using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Bson.Serialization.IdGenerators;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;

using Polly;
using Polly.Extensions.Http;

using Sentry;

using Serilog;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

using HttpClientExtensions = CoverGo.Auth.Infrastructure.Utils.HttpClientExtensions;
using IHostingEnvironment = Microsoft.AspNetCore.Hosting.IHostingEnvironment;
using JTokenSerializer = CoverGo.Auth.Infrastructure.Adapters.Mongo.JTokenSerializer;

namespace CoverGo.Auth;

public class Startup
{
    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;

        RSAParameters rsaParams = new()
        {
            D = Base64UrlEncoder.DecodeBytes(Environment.GetEnvironmentVariable("RSA_ENCODEDCONFIG_D")
                ?? "hEzT0LfDquBJqh8C8mZkuM94yhb484kKquVuLbQuj8RKiXPZ20ayV9GzIE3ybKthuGEqCsQk2Rn0StA7YoH0k2/KCayPnOXcRjkk7TUmJxAdPMBkYpI9bofK9pC+l7tipp5nc6MjTfPODo7WMLmaX40x9vWmBaWa0L2fGtF2enfvM1ZPXPVYRyohtkPiz1z81mOHefXDVY/ZmdsTDhEoOwP9cwJMeqI0km+SShLXKlX0TO8DwEC7gKfE+Us2yo9Owrkt0LP3Bxmh255LyHicgPX15qAkI9Q8K0AUqolMLFawm7USdwi9M5mYvrUw1wNATpiA+tJJDS64hvxPvZEywQ=="),
            DP = Base64UrlEncoder.DecodeBytes(Environment.GetEnvironmentVariable("RSA_ENCODEDCONFIG_DP")
                ?? "OUmB8wS5B8G2cAYMprjltGQpOR4BJmoMJhsf1m7WkZFYWe6OFRNrRm6sOZtn+zp3hUhqP4xnD457aU5f798y7yFcL3yyXrlYKb1D6j086KP5vjY7LU94xVezURHFxeaH+0/T3uxo9Sek8eCBZr82O84Jch4afTv46Qi4nqknSEc="),
            DQ = Base64UrlEncoder.DecodeBytes(Environment.GetEnvironmentVariable("RSA_ENCODEDCONFIG_DQ")
                ?? "S7ilfUU206IgeSX/rp4qjeZ0dVm5U+TKHQLTcEoTX46rfXU+LZYHgH6uIz4BIOcdgObUmZuJ9Y42oAS4QBCxk4munHGzNiThuzucNTAx5gnEh9UlGuhern5a4PGb5feO1IPpKI27G1yB7DTkc3bjWSeGplf1eRoarMt0smK88B0="),
            Exponent = Base64UrlEncoder.DecodeBytes(Environment.GetEnvironmentVariable("RSA_ENCODEDCONFIG_EXPONENT") ?? "AQAB"),
            InverseQ = Base64UrlEncoder.DecodeBytes(Environment.GetEnvironmentVariable("RSA_ENCODEDCONFIG_INVERSEQ")
                ?? "GSn8yK5NzJgqOaa+R+npm1lQKmZJikRI2p9sbRkBx3cXduiws1MIbF7+R11b6RtqeADK2WQhwzgSs58tS7Qu7K6ZCiEYBunLYTU5RCxnwCskli+VT48/dUVmJeUNiU2a8Ox7RJ1u21To8NkubrJwcsDTnKbpfSXVOZ31DPY8LOM="),
            Modulus = Base64UrlEncoder.DecodeBytes(Environment.GetEnvironmentVariable("RSA_ENCODEDCONFIG_MODULUS")
                ?? "yzDPguave15JltllLgjHEwfhb+urNatT6smIg4SJen9wGbcRqBQMFejWKUgB4zDAkxIoAKk5evQU9Dh6ZrDyyTeP2MfOhuVe0AnbkArd5srxxmSaLKyWIT5B1oHnGA6OdU29G83XBQi2AeawChPXEWtS3NFtfnxnOaS26GPuaOmOHeuhyEvXF9G2yQaFGneAcRg+FDc2l5BFVDkyisvRNU10MJc2/+uZE1EPPV4NAWRChJtX6iPORSR1Fd1asJd7BoSKMWij9NgH97v5md/SYXZG0DhRyrH+hpAAgKu1RasKfunmyuE71Gi8J69Sd1VYG7QyGQ0wa5WOiq3mN+BZoQ=="),
            P = Base64UrlEncoder.DecodeBytes(Environment.GetEnvironmentVariable("RSA_ENCODEDCONFIG_P")
                ?? "3/9wQNycpyJx4CWVT42Tuu5Slp2wL9MS5U5WUcEeanSRBs+Btm9ZfEe0P1lb+MRmFagrf57Wxqvfbv1Ymws9BdqxCI0n7nYrdFxHC4TCjXomAOKuQ6QlZu+Hp/rZtEdDvF2lGnFbxaoKvsnvlbSYHAvfEpS+PLwUqktijQCAzvc="),
            Q = Base64UrlEncoder.DecodeBytes(Environment.GetEnvironmentVariable("RSA_ENCODEDCONFIG_Q")
                ?? "6DhdmypupuiX8Cs7TjMkZO04d/bIss5bfxCb3ZC7L5UuH0yLSFWLyDO8E2iJaioZu2LM2mkHwHaZK+iekaINnIObiswTvX3OaBfbNXT64CecWbjOPPgKbrPBaUHG7M1utu85lB/D7KL7uE0PN9cQaYyQZfO7G22DWl/OI8mnPic="),
        };

        RsaSecurityKey = new(rsaParams);
    }

    public static IConfiguration Configuration { get; private set; }
    public static RsaSecurityKey RsaSecurityKey { get; private set; }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddHttpContextAccessor();
        services.AddHeaderPropagation(options => {
            options.Headers.Add(HttpHeaders.Authorization);
            options.Headers.Add(HttpHeaders.Tenant);
        });
        services.AddMemoryCache();
        services.AddSingleton<ISchedulerService, SchedulerService>();
        services.AddHttpClient<ISchedulerService, SchedulerService>(c => c.BaseAddress = new($"{Configuration["serviceUrls:scheduler"]}"));

        services.AddSingleton<MongoPasswordValidatorsRepository>();
        services.AddSingleton<MongoSSOConfigsRepository>();
        services.AddSingleton<MongoLoginRepository>();
        services.AddSingleton<MongoEventStore>();
        services.AddSingleton<PermissionValidator>();
        services.AddTransient(typeof(IMongoCollectionRepository<>), typeof(MongoCollectionRepository<>));
        services.AddTransient<IMongoCollectionFactory, MongoCollectionFactory>();
        services.AddSingleton<IMongoClientFactory, MongoClientFactory>();
        services.AddSingleton<ITokenService, TokenService>();
        services.AddSingleton<ITenantRepository, MongoTenantRepository>();
        services.AddSingleton<IBranchRequestDelegateProvider, BranchRequestDelegateProvider>();
        services.AddSingleton<AuthService>();
        services.AddSingleton<ISendNotificationScheduleRepository, MongoSendNotificationScheduleRepository>();

        if (Environment.GetEnvironmentVariable("appName") == null)
            Environment.SetEnvironmentVariable("appName", "covergo-auth");

        services
            .AddIdentityServer()
            .AddInMemoryClients(Enumerable.Empty<IdentityServer4.Models.Client>())
            .AddInMemoryIdentityResources(Enumerable.Empty<IdentityResource>())
            .AddSigningCredential(RsaSecurityKey)
            .AddInMemoryApiResources(Enumerable.Empty<ApiResource>());

        services
            .AddMvcCore()
            .AddApiExplorer()
            .SetCompatibilityVersion(CompatibilityVersion.Version_3_0)
            .AddNewtonsoftJson(
                options =>
                {
                    options.SerializerSettings.Converters.Add(new StringEnumConverter());
                    options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                    options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                    options.SerializerSettings.TypeNameHandling = TypeNameHandling.Auto;
                });

        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new() { Title = "CoverGo Auth", Version = "v1" });
            c.DescribeAllEnumsAsStrings();
        });
        services.AddSwaggerGenNewtonsoftSupport();

        services.AddCoverGoOpenTelemetryTracingIfEnabled();

        services.AddHealthChecks(checks => checks.AddValueTaskCheck("HTTP Endpoint", () => new(HealthCheckResult.Healthy("Ok"))));

        services.Configure<HostOptions>(opts => opts.ShutdownTimeout = TimeSpan.FromSeconds(45));
        services.AddCoverGoApplicationMonitoring();
        services.AddCoverGoAuthorization<LocalSSOAuthClient>(Configuration);
        services.AddCoverGoGraphQl<CoverGoLocalGraphQlRequestInterceptor>("auth", "CoverGo.Auth")
            .AddType<AppUrlType>()
            .AddErrorFilter(FilterErrors());

        static Func<IError, IError> FilterErrors() =>
            error =>
            {
                return error.Exception switch
                {
                    SecurityTokenException =>
                        error.WithCode("TOKEN_REQUEST_FAILUIRE")
                            .WithMessage(error.Exception.Message),
                    _ => error
                };
            };
    }

    public void Configure(IApplicationBuilder app, IHostingEnvironment env, ILoggerFactory loggerFactory, IEnumerable<ILoggerProvider> loggerProviders)
    {
        if (env.IsDevelopment())
            app.UseDeveloperExceptionPage();
        else
        {
            foreach (ILoggerProvider loggerProvider in loggerProviders)
                loggerFactory.AddProvider(loggerProvider);
            loggerFactory.AddSentry();
        }

        app.UseLogs();
        app.UseSentryTracing();
        app.UseCoverGoMetrics();

        app.UseAuthentication();
        // Don't remove this! It's needed or the gateway can't contact anymore.
        app.UseIdentityServer();
        // Don't remove this! It's needed or the gateway can't contact anymore.

        app.UseSwagger();
        app.UseSwaggerUI(c => c.SwaggerEndpoint($"v1/swagger.json", "CoverGo Auth v1"));
        app.UseRouting();
        app.UseAuthorization();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapDefaultControllerRoute();
            endpoints.MapGraphQL();
            endpoints.MapVersionEndpoint();
        });
        app.UseHeaderPropagation();
        // Mongo stuff
        //// Convention stuff
        ConventionRegistry.Remove("all");
        ConventionPack conventionPack = new() {
            new CamelCaseElementNameConvention(),
            new EnumRepresentationConvention(BsonType.String),
            new IgnoreExtraElementsConvention(true),
            new IgnoreIfNullConvention(true)
        };
        ConventionRegistry.Register("all", conventionPack, t => true);

        //// Class maps stuff
        if (!BsonClassMap.IsClassMapRegistered(typeof(AuthEvent)))
            BsonClassMap.RegisterClassMap<AuthEvent>(cm =>
            {
                cm.AutoMap();
                cm.MapIdProperty(c => c.Id)
                    .SetIdGenerator(StringObjectIdGenerator.Instance)
                    .SetSerializer(new StringSerializer(BsonType.ObjectId));
            });

        MongoPersistedGrantsBsonExtensions.RegisterPersistedGrantClassMap();

        app.UseMiddleware<ParallelApplicationPipelinesMiddleware>();

        // Really tried to find a way to it better but couldn't find:
        // BsonSerializer.RegisterSerializer(new JTokenSerializer()) will break if you do it twice in a row
        // Moreover, the other methods do not support serializing with JTokenSerializer like:
        // BsonSerializer.GetSerializer<JToken>(); will register not custom JToken serializer
        // BsonSerializer.LookupSerializer<JToken>(); will register not custom JToken serializer
        FieldInfo field = BsonSerializer.SerializerRegistry.GetType().GetField("_cache", BindingFlags.NonPublic | BindingFlags.Instance);
        ConcurrentDictionary<Type, IBsonSerializer> bsonSerializerCache = (ConcurrentDictionary<Type, IBsonSerializer>) field.GetValue(BsonSerializer.SerializerRegistry);
        if (!bsonSerializerCache.TryGetValue(typeof(JToken), out IBsonSerializer _))
            BsonSerializer.RegisterSerializer(new JTokenSerializer());

        app.UseCoverGoApplicationMonitoring();
    }

    internal static void GenericAppBuilderConfig(IApplicationBuilder app, string tenantId)
        => app.Map($"/{tenantId}", appBuilder =>
        {
            appBuilder.UseSwagger();
            appBuilder.UseSwaggerUI(c => c.SwaggerEndpoint("v1/swagger.json", "CoverGo Auth v1"));
            appBuilder.UseIdentityServer();
            appBuilder.UseRouting();
            appBuilder.UseEndpoints(endpoints => endpoints.MapDefaultControllerRoute());
            appBuilder.UseHeaderPropagation();

            HttpClientExtensions.HttpContextAccessor = appBuilder.ApplicationServices.GetService<IHttpContextAccessor>();
            HttpClientExtensions.LogBodyRequestEnabled = Configuration.GetValue<bool?>("LogRequestBodyEnabled") ?? false;
        });

    internal static void GenericServicesConfig(
        IServiceCollection services,
        string tenantId)
    {
        services.TryAddSingleton(new TenantId(tenantId));

        services.AddHttpContextAccessor();

        services.AddHeaderPropagation(options =>
        {
            options.Headers.Add(HttpHeaders.Authorization);
            options.Headers.Add(HttpHeaders.Tenant);
        });

        services.AddLogging(loggingBuilder => loggingBuilder.AddSerilog(dispose: true));

        PasswordValidators passwordValidators = new();
        DbConfig dbConfig = DbConfig.GetConfig(tenantId);

        if (dbConfig.ProviderId == KnownDbProviderIds.Mongo)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(dbConfig);
            IMongoDatabase db = client.GetDatabase("auth");
            ProjectionDefinition<AppDao> projection = Builders<AppDao>.Projection
                .Include(a => a.ClientId)
                .Include(a => a.EmailConfirmationTokenLifespan)
                .Include(a => a.DataProtectionTokenLifespan)
                .Include(a => a.ActivationTokenExpiryDisabled);
            IReadOnlyCollection<AppDao> appDaos = db.GetCollection<AppDao>($"{tenantId}-clients")
                .Find(Builders<AppDao>.Filter.Empty)
                .Project<AppDao>(projection)
                .ToList();

            services.AddDataProtection().PersistKeysToMongoDb(() => db, $"{tenantId}-dataProtectionKeys");
            IMongoCollection<PasswordValidators> collection = db.GetCollection<PasswordValidators>($"{tenantId}-passwordValidators");
            PasswordValidators defaultPasswordValidators
                = collection.Find(Builders<PasswordValidators>.Filter.Eq(e => e.ClientId, "default")).SingleOrDefault();
            if (defaultPasswordValidators != null)
                passwordValidators = defaultPasswordValidators;

            services.Configure<CustomEmailConfirmationTokenProviderOptions>(o =>
                o.AppNameToEmailTokenLifespan = appDaos.ToDictionary(kvp => kvp.ClientId, kvp => kvp.EmailConfirmationTokenLifespan ?? TimeSpan.FromDays(1))
            );

            services.Configure<CustomPasswordResetTokenProviderOptions>(o =>
            {
                o.AppNameToAppSettings = appDaos.ToDictionary(kvp => kvp.ClientId,
                    kvp => kvp);
            }
            );
            services.AddSingleton<CustomPasswordResetTokenProviderContext>();
            services.AddSingleton<CustomEmailConfirmationTokenProvider>();
            services.AddSingleton<CustomPasswordResetTokenProvider>();

            var persistedGrantIndexesInitializer = new MongoPersistedGrantsIndexesInitializer(tenantId);
            Task.Run(async () => await persistedGrantIndexesInitializer.CreateIndexes(db)).Wait();
        }

        //NOTE: need to insert documents into their DB
        if (tenantId == "aag" || tenantId == "aag_uat")
        {
            passwordValidators.RequireDigit = true;
            passwordValidators.RequireNonAlphanumeric = true;
            passwordValidators.RequireUppercase = true;
            passwordValidators.RequireLowercase = true;
        }

        services.Configure<DataProtectionTokenProviderOptions>(x => x.TokenLifespan = TimeSpan.FromDays(14));
        services
            .AddMvcCore()
            .AddApiExplorer()
            .SetCompatibilityVersion(CompatibilityVersion.Version_3_0)
            .AddNewtonsoftJson(
                options =>
                {
                    options.SerializerSettings.Converters.Add(new StringEnumConverter());
                    options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                    options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                })
            .AddApplicationPart(typeof(AuthController).Assembly);

        UriBuilder uriBuilder = new($"{Configuration["serviceUrls:auth"]}");
        if (uriBuilder.Uri.IsDefaultPort)
            uriBuilder.Port = -1;

        var identityServerBuilder = services
            .AddIdentityServer(options => options.IssuerUri = DiscoveryClient.ParseUrl(uriBuilder.ToString()).Authority)
            .AddSigningCredential(RsaSecurityKey)
            .AddIdentityApiResources()
            .AddProfileService<CustomProfileService>()
            .AddClients();

        if (dbConfig.ProviderId == KnownDbProviderIds.Mongo)
        {
            identityServerBuilder.AddPersistedGrantStore<MongoPersistedGrantsRepository>();
        }

        if (UserSourceConfig.Source == UserSource.MongoDb)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(dbConfig);
            IMongoDatabase db = client.GetDatabase("auth");

            identityServerBuilder
                .AddMongoForAspIdentity<MongoLoginDao, Microsoft.AspNetCore.Identity.MongoDB.IdentityRole>(db, tenantId,
                    passwordValidators.RequireConfirmedEmail,
                    passwordValidators.RequireConfirmPhoneNumber,
                    passwordValidators.RequireDigit,
                    passwordValidators.RequireLength,
                    passwordValidators.RequireUniqueChars,
                    passwordValidators.RequireLowercase,
                    passwordValidators.RequireUppercase,
                    passwordValidators.RequireNonAlphanumeric,
                    passwordValidators.EnableLockout,
                    passwordValidators.LockoutTimespanInSeconds,
                    passwordValidators.LockoutMaxFailedAccessAttempts,
                    passwordValidators.RequireLetter,
                    passwordValidators.NotContainUsername);

            services.AddTransient(typeof(IMongoCollectionRepository<>), typeof(MongoCollectionRepository<>));
            services.AddTransient<IMongoCollectionFactory, MongoCollectionFactory>();
            services.AddSingleton<IMongoClientFactory, MongoClientFactory>();
            services.AddSingleton<ITenantRepository, MongoTenantRepository>();
            services.AddSingleton<IRepository, MongoRepository>();
            services.AddSingleton<IEventStore, MongoEventStore>();
            services.AddSingleton<MongoSSOConfigsRepository>();
            services.AddSingleton<IPermissionSchemaRepository, MongoPermissionSchemaRepository>();
            services.AddSingleton<IUserStorageRepository, UserStorageMongoRepository>();

            services.AddSingleton<IResourceOwnerPasswordValidator, ResourceOwnerPasswordValidator>();
            services.AddSingleton(new CustomEmailTokenProviderOptions
            {
                TokenLifespanInSeconds = passwordValidators.Email2faLifespanInSeconds,
                TimeStepInSeconds = passwordValidators.Email2faTimeStepInSeconds
            });
        }

        if (UserSourceConfig.Source == UserSource.LDAP)
        {
            identityServerBuilder
                .UseLDAPForIdentityServer(tenantId)
                .AddProfileService<CustomLdapUserProfileService<DBSLdapUser>>();

            services.AddSingleton<IResourceOwnerPasswordValidator, CustomLdapResourceOwnerPasswordValidator<DBSLdapUser>>();
        }

        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new() { Title = "CoverGo Auth", Version = "v1" });
            c.DescribeAllEnumsAsStrings();
        });
        services.AddSwaggerGenNewtonsoftSupport();

        services.AddSingleton<AuthService>();
        services.AddSingleton<IAuthService, AuthService>();
        services.AddSingleton<PermissionSchemaService>();
        services.AddSingleton<MongoEventStore>();
        services.AddSingleton<LoginMutation>();
        services.AddSingleton<MongoLoginRepository>();
        services.AddSingleton<PermissionValidator>();
        services.AddSingleton<Proxies.Auth.IAuthService, Proxies.Auth.CoverGoAuthService>();
        services.AddSingleton<ISchedulerService, SchedulerService>();
        services.AddHttpClient<ISchedulerService, SchedulerService>(c => c.BaseAddress = new($"{Configuration["serviceUrls:scheduler"]}"));

        services.AddSingleton(t => new Tenant { Id = tenantId });

        string otpLoginCipherKey = Environment.GetEnvironmentVariable("OTP_LOGIN_CIPHER_KEY") ?? "zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=";
        string otpLoginCipherIV = Environment.GetEnvironmentVariable("OTP_LOGIN_CIPHER_IV") ?? "94jCf53NO1acZ3pO7UE+gA==";
        string otpLoginHasherKey = Environment.GetEnvironmentVariable("OTP_LOGIN_HASHER_KEY") ?? "1aJtP4OoGnmHvZXstjay";
        services.AddSingleton<Aes>(OtpHelpers.CreateAesCipher(otpLoginCipherKey, otpLoginCipherIV));
        services.AddSingleton<HMAC>(new HMACSHA256(Encoding.UTF8.GetBytes(otpLoginHasherKey)));
        services.AddSingleton<IOtpRepository, MongoDbOtpRepository>();
        services.AddSingleton<ISendNotificationScheduleRepository, MongoSendNotificationScheduleRepository>();
        
        RegisterDomainServices(services);

        services.AddMemoryCache();
        services.RegisterPermissionTypes(Configuration);
        services.AddFeatureManagement(Configuration);
        services.AddMultiTenantFeatureManagement(Configuration);
    }

    static void RegisterDomainServices(IServiceCollection services)
    {
        services.AddTransient<SentryHttpMessageHandler>();

        services.AddSingleton<IOtpService, OtpService>();
        services.AddHttpClient<IOtpService, OtpService>(c => 
            c.BaseAddress = new($"{Configuration["serviceUrls:auth"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>();

        services.AddSingleton<IPolicyService, CoverGoPolicyService>();
        services.AddHttpClient<IPolicyService, CoverGoPolicyService>(c =>
            c.BaseAddress = new($"{Configuration["serviceUrls:policies"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>();

        services.AddSingleton<ICaseService, CoverGoCaseService>();
        services.AddHttpClient<ICaseService, CoverGoCaseService>(c =>
            c.BaseAddress = new($"{Configuration["serviceUrls:cases"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>();

        services.AddSingleton<IEntityService, CoverGoEntityService>();
        services.AddHttpClient<IEntityService, CoverGoEntityService>(c =>
            c.BaseAddress = new($"{Configuration["serviceUrls:users"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>();

        services.AddSingleton<ITransactionService, CoverGoTransactionService>();
        services.AddHttpClient<ITransactionService, CoverGoTransactionService>(c =>
            c.BaseAddress = new($"{Configuration["serviceUrls:transactions"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>();

        services.AddSingleton<IAdvisorService, CoverGoAdvisorService>();
        services.AddHttpClient<IAdvisorService, CoverGoAdvisorService>(c =>
            c.BaseAddress = new($"{Configuration["serviceUrls:advisor"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>();

        services.AddSingleton<IClaimsService, CoverGoClaimsService>();
        services.AddHttpClient<IClaimsService, CoverGoClaimsService>(c =>
            c.BaseAddress = new($"{Configuration["serviceUrls:claims"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>();

        services.AddSingleton<IGuaranteeOfPaymentService, GuaranteeOfPaymentService>();
        services.AddHttpClient<IGuaranteeOfPaymentService, GuaranteeOfPaymentService>(
            c => c.BaseAddress = new($"{Configuration["serviceUrls:claims"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>();

        services.AddSingleton<INotificationService, CoverGoNotificationService>();
        services.AddHttpClient<INotificationService, CoverGoNotificationService>(c =>
                c.BaseAddress = new($"{Configuration["serviceUrls:notifications"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>()
            .SetHandlerLifetime(TimeSpan.FromMinutes(5))
            .AddPolicyHandler(GetRetryPolicy());

        services.Configure<CoverGoClaims3ServiceSettings>(Configuration.GetSection("CoverGoClaims3ServiceSettings"));
        services.AddSingleton<IClaims3Service, CoverGoClaims3Service>();
        string claims3ServiceUrl = $"{Configuration["serviceUrls:claims3"] ?? throw new InvalidOperationException("Claims3 service URL is not specified in the configuration.")}internal/graphql";
        services.AddClaims3Client()
            .ConfigureHttpClient(client => client.BaseAddress = new Uri(claims3ServiceUrl), clientBuilder => clientBuilder.AddHeaderPropagation())
            .ConfigureWebSocketClient(client => client.Uri = new Uri(claims3ServiceUrl.Replace("http", "ws")));

        services.AddSingleton<IPoliciesClient, PoliciesClient>();
        services.AddHttpClient<IPoliciesClient, PoliciesClient>(c =>
            c.BaseAddress = new Uri($"{Configuration["serviceUrls:policies"]}"));
        services.AddSingleton<IGatewayService, CoverGoGatewayService>();
        services.AddHttpClient<IGatewayService, CoverGoGatewayService>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:gateway"]}"))
            .AddHttpMessageHandler<SentryHttpMessageHandler>();

        services.AddChannelManagementServices(Configuration);
    }

    private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .WaitAndRetryAsync(6, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2,
                                                                        retryAttempt)));
    }
}
