﻿using CoverGo.FeatureManagement;
using Microsoft.FeatureManagement;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.Extensions;

public static class FeatureManagerExtensions
{
    public static Task<bool> IsPermissionV2EnabledAsync(this IFeatureManager _featureManager)
    {
        return _featureManager.IsEnabledAsync("PermissionV2");
    }

    public static Task<bool> IsDisableGenericForgotPasswordEmailEnabledAsync(this IMultiTenantFeatureManager featureManager, string tenantId) => featureManager.IsEnabled("DisableGenericForgotPasswordEmail", tenantId);
}
