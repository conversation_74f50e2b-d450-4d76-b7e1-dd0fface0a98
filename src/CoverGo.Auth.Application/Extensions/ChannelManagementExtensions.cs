﻿using Microsoft.Extensions.Configuration;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.OtherServices;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace CoverGo.Auth.Application.Extensions
{
    public static class ChannelManagementExtensions
    {
        public static IServiceCollection AddChannelManagementServices(this IServiceCollection services, IConfiguration configuration)
        {
            var serviceUrls = new ServiceUrls();
            configuration.GetSection("serviceUrls").Bind(serviceUrls);
            services.AddSingleton<ICoverGoChannelManagementService, CoverGoChannelManagementService>();
            string channelManagementServiceUrl = $"{serviceUrls.ChannelManagement ?? throw new InvalidOperationException("Channel Management service URL is not specified in the configuration.")}graphql";
            services.AddChannelManagementClient()
                .ConfigureHttpClient(client => client.BaseAddress = new Uri(channelManagementServiceUrl), clientBuilder => clientBuilder.AddHeaderPropagation())
                .ConfigureWebSocketClient(client => client.Uri = new Uri(channelManagementServiceUrl.Replace("http", "ws")));
            return services;
        }

        public class ServiceUrls
        {
            public string ChannelManagement { get; set; }
        }
    }
}
