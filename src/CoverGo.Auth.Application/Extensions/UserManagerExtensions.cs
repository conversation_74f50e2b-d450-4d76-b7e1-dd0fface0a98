﻿using CoverGo.Auth.Domain;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Identity;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.Extensions
{
    public static class UserManagerExtensions
    {
        public static async Task<(string, Result)> GenerateSingleUsePasswordResetTokenAsync(this UserManager<MongoLoginDao> user<PERSON><PERSON><PERSON>, MongoLoginDao user)
        {
            string code = await userManager.GeneratePasswordResetTokenAsync(user);
            user.PasswordResetToken = code;
            IdentityResult identityResult = await userManager.UpdateAsync(user);
            if (!identityResult.Succeeded) {
                return (null, new Result { Status = "failure", Errors = identityResult.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() });
            }
            return (code , null);
        }
    }
}
