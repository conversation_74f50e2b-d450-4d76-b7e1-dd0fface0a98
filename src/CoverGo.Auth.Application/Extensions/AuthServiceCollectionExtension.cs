﻿using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.Abstraction;
using CoverGo.Auth.Domain.Permission;
using CoverGo.Auth.Domain.Permission.Queries;
using CoverGo.Auth.Infrastructure.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;

namespace CoverGo.Auth.Application.Extensions;

public static class AuthServiceCollectionExtension
{
    public static IServiceCollection RegisterPermissionTypes(
        this IServiceCollection services, IConfiguration configuration)
    {
        var warningThresholdMs = GetPerfWarningThresholdInMs(configuration);

        services.AddSingleton<IPermissionService, PermissionService>();
        services.AddQueryHandler<
            GetPermissionTargetIdsQuery, PermissionTargetIds, PermissionTargetIdQueryHandler>();
        services.AddQueryHandler<GetLoginQuery, MongoLoginDao, LoginQueryHandler>();
        services.AddQueryHandler<GetLoginClaimsQuery, UserClaimCollection, LoginClaimsQueryHandler>();
        services.AddQueryHandler<
            GetAllPermissionTargetIdsQuery, PermissionTargetIds, GetAllPermissionTargetIdsQueryHandler>();
        services.AddQueryHandler<
            GetMultipleLoginPermissionsQuery, LoginPermission[], GetMultipleLoginPermissionsQueryHandler>();

        services.AddSingleton<PermissionContextBuilder>();
        services.AddSingleton<ConcurrentPermissionVariableResolver>();

        var assembly = typeof(CompositePermissionVariableResolver).Assembly;
        var permissionResolverTypes = (from type in assembly.GetTypes()
                where !type.IsAbstract
                where type != typeof(CompositePermissionVariableResolver)
                where type.IsPublic
                where typeof(IPermissionVariableResolver).IsAssignableFrom(type)
                select type)
            .ToArray();

        foreach (var type in permissionResolverTypes)
        {
            services.AddSingleton(type);
        }

        services.AddSingleton<IPermissionVariableResolver>(sp =>
            new CompositePermissionVariableResolver(
                sp.GetResolvers(permissionResolverTypes),
                sp.GetRequiredService<ILogger<CompositePermissionVariableResolver>>(),
                warningThresholdMs));

        return services;
    }

    public static IServiceCollection AddQueryHandler<T, TResult, TQueryHandler>(
        this IServiceCollection services
    ) where TQueryHandler : class, IQueryHandler<T, TResult>
    {
        services
            .AddTransient<IQueryHandler<T, TResult>, TQueryHandler>();

        return services;
    }

    private static IPermissionVariableResolver[] GetResolvers(
        this IServiceProvider sp, Type[] types)
    {
        return types.Select(sp.GetResolver).ToArray();
    }

    private static IPermissionVariableResolver GetResolver(
        this IServiceProvider sp,
        Type t)
    {
        return (IPermissionVariableResolver) sp.GetRequiredService(t);
    }

    private static int GetPerfWarningThresholdInMs(
        this IConfiguration configuration)
    {
        const int defaultPerfWarningThresholdInMilliseconds = 500;

        return int.TryParse(
            configuration["PerfWarningThresholdInMilliseconds"],
            out var parsedThreshold) ?
            parsedThreshold : defaultPerfWarningThresholdInMilliseconds;
    }
}