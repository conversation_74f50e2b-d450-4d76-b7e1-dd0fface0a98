﻿using Microsoft.Extensions.Configuration;
using System;

namespace CoverGo.Auth.Application
{
    public static class UserSourceConfig
    {
        public static UserSource Source { get; set; }

        public static void Load<T>()
            where T : class
        {
            IConfigurationRoot config = new ConfigurationBuilder().AddUserSecrets<T>().AddEnvironmentVariables()
                .Build();
            Source = Enum.TryParse(config.GetValue<string>("USERSOURCE"), true, out UserSource source)
                ? source
                : UserSource.MongoDb;
        }
    }

    public enum UserSource
    {
        MongoDb,
        EF,
        LDAP
    }
}
