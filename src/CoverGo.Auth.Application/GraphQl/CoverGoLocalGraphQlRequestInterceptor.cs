﻿using CoverGo.Proxies.Auth;
using HotChocolate.AspNetCore;
using HotChocolate.Execution;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.GraphQl;

public class CoverGoLocalGraphQlRequestInterceptor : DefaultHttpRequestInterceptor
{
    readonly IAuthService _authService;

    public CoverGoLocalGraphQlRequestInterceptor(IAuthService authService) =>
        _authService = authService;

    public override async ValueTask OnCreateAsync(
        HttpContext context,
        IRequestExecutor requestExecutor,
        IQueryRequestBuilder requestBuilder,
        CancellationToken cancellationToken)
    {
        AuthenticateResult authenticateResult =
            await context.AuthenticateAsync("Bearer");
        if (authenticateResult.Succeeded)
            context.User = authenticateResult.Principal;

        if (!context.User?.Identity?.IsAuthenticated ?? false) {
            await base.OnCreateAsync(context, requestExecutor, requestBuilder, cancellationToken);
            return;
        }

        ClaimsIdentity identity = (ClaimsIdentity) context.User.Identity;
        requestBuilder.AddProperty("identity", identity);

        string tenantId = GetClaim(identity, "tenantId");
        string loginId = GetClaim(identity, "sub");
        string clientId = GetClaim(identity, "appId") ?? GetClaim(identity, "client_id");

        await AddTargetedPermissionsClaims(identity, tenantId, loginId, cancellationToken);

        requestBuilder.AddProperty("tenantId", tenantId);
        requestBuilder.AddProperty("loginId", loginId);
        requestBuilder.AddProperty("clientId", clientId);

        await base.OnCreateAsync(context, requestExecutor, requestBuilder, cancellationToken);
    }

    async Task AddTargetedPermissionsClaims(ClaimsIdentity identity, string tenantId, string loginId, CancellationToken cancellationToken)
    {
        Proxies.Auth.Login login = await _authService.GetLoginById(tenantId, loginId);
        if (login.TargettedPermissions?.Any() ?? false) {
            foreach (KeyValuePair<string, IEnumerable<string>> permission in login.TargettedPermissions) {
                foreach (string value in permission.Value) {
                    identity.AddClaim(new Claim(permission.Key, value));
                }
            }
        }
    }

    static string GetClaim(ClaimsIdentity identity, string claimType) => identity.Claims.FirstOrDefault(x => x.Type == claimType)?.Value;
}
