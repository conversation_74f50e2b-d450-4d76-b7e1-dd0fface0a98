﻿using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.AspNetCore.Authorization;
using HotChocolate.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.GraphQl.SSO;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class LoginMutation
{
    readonly MongoLoginRepository _repo;
    readonly MongoEventStore _mongoEventStore;
    readonly PermissionValidator _permissionValidator;
    readonly JsonSerializer _jsonSerializer;
    readonly ISchedulerService _schedulerService;
    readonly AuthService _authService;

    public LoginMutation(
        [Service] MongoLoginRepository repo,
        MongoEventStore mongoEventStore,
        PermissionValidator permissionValidator,
        ISchedulerService schedulerService,
        AuthService authService)
    {
        _mongoEventStore = mongoEventStore;
        _repo = repo;
        _permissionValidator = permissionValidator;
        _schedulerService = schedulerService;
        _authService = authService;

        var settings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            TypeNameHandling = TypeNameHandling.Auto,
            NullValueHandling = NullValueHandling.Ignore,
        };
        settings.Converters.Add(new StringEnumConverter());

        _jsonSerializer = JsonSerializer.Create(settings);
    }

    [Authorize]
    public async Task<Result> Deactivate(
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        string loginId,
        DateTime? scheduleFor,
        CancellationToken cancellationToken)
    {
        Result result = await ScheduleOrExecuteAsync(loginId, ActionName.Deactivate, scheduleFor, identity, tenantId, async () =>
        {
            string deactivatedById = identity.FindFirst("sub")?.Value;

            LoginEvent @event = new LoginEvent(loginId, LoginEventType.deactivate, DateTime.UtcNow)
            {
                Values = JObject.FromObject(new { loginId, deactivatedById }, _jsonSerializer)
            };

            await _mongoEventStore.AddEventAsync(tenantId, @event, cancellationToken);

            Result deactivateResult = await _repo.DeactivateAsync(tenantId, loginId, cancellationToken);

            await _repo.RemovePersistentGrantsForLoginAsync(tenantId, loginId);

            return deactivateResult;

        }, cancellationToken);

        return result;
    }

    [Authorize]
    public async Task<Result> Reactivate(
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        string loginId,
        DateTime? scheduleFor,
        SendReactivateNotificationCommand reactivateNotificationCommand,
        CancellationToken cancellationToken)
    {
        try
        {
            string reactivatedById = identity.FindFirst("sub")?.Value;

            Result result = await ScheduleOrExecuteAsync(loginId, ActionName.Reactivate, scheduleFor, identity, tenantId, async () =>
            {
                LoginEvent @event = new LoginEvent(loginId, LoginEventType.reactivate, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(new { loginId, reactivatedById }, _jsonSerializer)
                };
                await _mongoEventStore.AddEventAsync(tenantId, @event, cancellationToken);

                return await _repo.ReactivateAsync(tenantId, loginId, cancellationToken);

            }, cancellationToken);

            if (reactivateNotificationCommand != null)
            {
                var notificationResult = await SendReactivateEmail(tenantId, loginId, reactivatedById, reactivateNotificationCommand, cancellationToken);
                if (!notificationResult.IsSuccess)
                {
                    return notificationResult;
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            return Result.Failure(ex.Message);
        }
    }

    #region Helper methods

    private async Task<Result> ScheduleOrExecuteAsync(string loginId, ActionName actionName, DateTime? scheduleFor, ClaimsIdentity identity, string tenantId, Func<Task<Result>> action, CancellationToken cancellationToken)
    {
        await _permissionValidator.AuthorizeAsync(identity, "writeLogins", loginId);

        string scheduleName = $"{actionName}Login{loginId}InTenant{tenantId}";

        if (scheduleFor.HasValue && scheduleFor.Value > DateTime.UtcNow)
            return await CreateOrUpdateScheduleAsync(actionName, identity, scheduleName, tenantId, loginId, scheduleFor, cancellationToken);

        Result result = await action();

        if (scheduleFor.HasValue)
            await _schedulerService.DeleteJobScheduleAsync(tenantId, scheduleName, cancellationToken);

        return result;
    }

    private async Task<Result> CreateOrUpdateScheduleAsync(ActionName actionName, ClaimsIdentity identity, string scheduleName, string tenantId, string loginId, DateTime? scheduleFor, CancellationToken cancellationToken)
    {
        string asLoginId = identity.FindFirst("sub")?.Value;
        if (string.IsNullOrWhiteSpace(asLoginId))
            asLoginId = identity.FindFirst("loginId")?.Value;

        string appId = identity.FindFirst("appId")?.Value;
        DateTime scheduleForValue = scheduleFor!.Value.ToLocalTime();

        JobSchedule jobSchedule = new()
        {
            Name = scheduleName,
            Description = $"{actionName} login {loginId} in tenant {tenantId} at {scheduleFor.Value}",
            CronExpression =
                $"0 {scheduleForValue.Minute}/10 {scheduleForValue.Hour}/1 {scheduleForValue.Day}/1 {scheduleForValue.Month}/1 ? {scheduleForValue.Year}/1",
            JobDetail = new JobDetail
            {
                Type = "customJob",
                JobId = "DeactivateOrReactivateLogin",
                CustomJobVariables = $$"""
                    {
                       "loginId": "{{loginId}}",
                       "scheduleFor": "{{scheduleFor.Value:o}}",
                       "action": "{{actionName}}",
                       "appId": "{{appId}}",
                       "asLoginId": "{{asLoginId}}"
                    }
                    """
            }
        };

        await _schedulerService.DeleteJobScheduleAsync(tenantId, scheduleName, cancellationToken);

        Result scheduleResult = await _schedulerService.CreateJobScheduleAsync(tenantId, appId, jobSchedule, cancellationToken);

        return scheduleResult;
    }

    enum ActionName
    {
        Deactivate = 0,
        Reactivate = 1
    }

    #endregion

    #region SendReactivateNotificationCommand
    public class SendReactivateNotificationCommand
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string FromEntityId { get; set; }
        public string ToEntityId { get; set; }
        public string PolicyId { get; set; }
        public string OfferId { get; set; }
        public SendReactivateEmailMessage EmailMessage { get; set; }
        public bool? UseConfig { get; set; }
        public string SentById { get; set; }
        public string AppId { get; set; }
        public string LoginId { get; set; }
        public string CallbackUrl { get; set; }
        public string RedirectQueryString { get; set; }
        public string Language { get; set; }
        public DateTime? ScheduleToSendAt { get; set; }
    }

    public class SendReactivateEmailMessage
    {
        public string From { get; set; }
        public string FromName { get; set; }
        public string To { get; set; }
        public List<string> Tos { get; set; }
        public List<string> Ccs { get; set; }
        public List<string> Bccs { get; set; }
        public string Subject { get; set; }
        public string HtmlContent { get; set; }
        public SendReactivateTemplateRendering TemplateRendering { get; set; }
    }

    public class SendReactivateTemplateRendering
    {
        public string TemplateId { get; set; }
        public SendReactivateRenderParameters Input { get; set; }
    }

    public class SendReactivateRenderParameters
    {
        public string Name { get; set; }
        public string ContentJsonString { get; set; }
        public string Url { get; set; }
        public string AccessToken { get; set; }
        public string VariablesJsonString { get; set; }
    }

    private async Task<Result> SendReactivateEmail(string tenantId, string loginId, string reactivatedById, SendReactivateNotificationCommand command, CancellationToken cancellationToken)
    {
        string clientId = command.AppId;
        await CreateScheduleJobIfNotExists(tenantId, clientId, "SendAuthNotificationJob", cancellationToken);

        Domain.App app = (await _authService.GetAppsAsync(tenantId, new AppWhere { AppId = clientId }, null, null, null, cancellationToken))?.FirstOrDefault();

        if (app == null)
        {
            return new Result { Status = "failure", Errors = new List<string> { $"App '{clientId}' doesn't exist for tenant '{tenantId}'." } };
        }

        string clientEmail = app?.Email;
        string senderName = app?.EmailSenderName;

        SendNotificationCommand sendNotificationCommand = new SendNotificationCommand
        {
            Id = command.Id,
            Type = command.Type,
            FromEntityId = command.FromEntityId,
            ToEntityId = command.ToEntityId,
            PolicyId = command.PolicyId,
            OfferId = command.OfferId,
            AppId = app.AppId,
            LoginId = loginId,
            SentById = reactivatedById,
            UseConfig = command.UseConfig ?? app.UseNotificationConfig,
            CallbackUrl = command.CallbackUrl,
            RedirectQueryString = command.RedirectQueryString,
            Language = command.Language,
            ScheduleToSendAt = command.ScheduleToSendAt,
            EmailMessage = new EmailMessage
            {
                From = clientEmail ?? command.EmailMessage.From ?? "<EMAIL>",
                FromName = senderName ?? command.EmailMessage.FromName ?? "CoverGo",
                To = command.EmailMessage.To,
                Subject = command.EmailMessage.Subject,
                HtmlContent = command.EmailMessage.HtmlContent,
                TemplateRendering = new TemplateRendering
                {
                    TemplateId = command.EmailMessage.TemplateRendering?.TemplateId,
                    Input = new RenderParameters
                    {
                        Name = command.EmailMessage.TemplateRendering?.Input?.Name,
                        ContentJsonString = command.EmailMessage.TemplateRendering?.Input?.ContentJsonString,
                        Url = command.EmailMessage.TemplateRendering?.Input?.Url,
                        AccessToken = command.EmailMessage.TemplateRendering?.Input?.AccessToken,
                        VariablesJsonString = command.EmailMessage.TemplateRendering?.Input?.VariablesJsonString
                    }
                }
            }
        };

        if (!sendNotificationCommand.ScheduleToSendAt.HasValue)
        {
            sendNotificationCommand.ScheduleToSendAt = DateTime.UtcNow;
        }

        try
        {
            var scheduledNotifications = await _authService.QueryScheduledNotifications(tenantId, new QueryArguments<Filter<SendNotificationScheduleFilter>>
            {
                Where = new Filter<SendNotificationScheduleFilter>
                {
                    And = new List<Filter<SendNotificationScheduleFilter>>
                        {
                            new Filter<SendNotificationScheduleFilter>
                            {
                                Where = new SendNotificationScheduleFilter
                                {
                                    Status = "Scheduled"
                                }
                            },
                            new Filter<SendNotificationScheduleFilter>
                            {
                                Where = new SendNotificationScheduleFilter
                                {
                                    LoginId = command.LoginId
                                }
                            },
                            new Filter<SendNotificationScheduleFilter>
                            {
                                Where = new SendNotificationScheduleFilter
                                {
                                    Type = command.Type
                                }
                            }
                        }
                }
            }, cancellationToken);

            if (scheduledNotifications.Count() != 0)
            {
                return await _authService.UpdateSendNotificationScheduleAsync(tenantId, new UpsertSendNotificationScheduleCommand
                {
                    Id = scheduledNotifications.First().Id,
                    ScheduleToSendAt = sendNotificationCommand.ScheduleToSendAt.Value.AddMinutes(3)
                }, cancellationToken);
            }

            var result = await _authService.CreateSendNotificationScheduleAsync(tenantId, new UpsertSendNotificationScheduleCommand
            {
                Type = sendNotificationCommand.Type,
                FromEntityId = sendNotificationCommand.FromEntityId,
                ToEntityId = sendNotificationCommand.ToEntityId,
                PolicyId = sendNotificationCommand.PolicyId,
                OfferId = sendNotificationCommand.OfferId,
                EmailMessage = sendNotificationCommand.EmailMessage,
                UseConfig = sendNotificationCommand.UseConfig,
                SentById = sendNotificationCommand.SentById,
                AppId = app.AppId,
                LoginId = sendNotificationCommand.LoginId,
                CallbackUrl = sendNotificationCommand.CallbackUrl,
                RedirectQueryString = sendNotificationCommand.RedirectQueryString,
                Language = sendNotificationCommand.Language,
                ScheduleToSendAt = sendNotificationCommand.ScheduleToSendAt.Value.AddMinutes(3),
                Status = "Scheduled"
            }, cancellationToken);

            if (!result.IsSuccess)
                return Result.Failure(result.Errors);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure(ex.Message);
        }
    }

    private async Task CreateScheduleJobIfNotExists(string tenantId, string appId, string scheduleName, CancellationToken cancellationToken)
    {
        var scheduledNotifications = await _schedulerService.GetJobSchedulesAsync(tenantId, new QueryArguments<JobScheduleWhere>
        {
            Where = new JobScheduleWhere
            {
                Name = scheduleName
            }
        }, cancellationToken);

        if (scheduledNotifications.Count() == 0)
        {
            await _schedulerService.CreateJobScheduleAsync(tenantId, appId, new JobSchedule
            {
                Name = scheduleName,
                Description = scheduleName,
                CronExpression = "0 */10 * ? * *",
                JobDetail = new JobDetail
                {
                    Type = "customJob",
                    JobId = scheduleName,
                }
            }, cancellationToken);
        }
    }

    #endregion
}
