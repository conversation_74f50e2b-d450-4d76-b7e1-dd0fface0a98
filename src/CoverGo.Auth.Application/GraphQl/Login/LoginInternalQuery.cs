﻿using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using HotChocolate;
using HotChocolate.Types;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.GraphQl.Login;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class LoginInternalQuery
{
    readonly MongoLoginRepository _repo;

    public LoginInternalQuery([Service] MongoLoginRepository repo) =>
        _repo = repo;

    public async Task<bool> DoesActiveLoginExistByEmail(
        string tenantId,
        string email,
        CancellationToken cancellationToken) =>
        await _repo.DoesActiveLoginExistByEmail(tenantId, email, cancellationToken);
}
