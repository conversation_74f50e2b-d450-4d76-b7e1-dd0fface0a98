﻿using IdentityModel.Client;
using System.Diagnostics.CodeAnalysis;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.GraphQl.SSO;

[ExcludeFromCodeCoverage]
public class TokenService : ITokenService
{
    public async Task<TokenResponse> RequestAuthorizationCodeTokenAsync(AuthorizationCodeTokenRequest request,
        CancellationToken cancellationToken = default)
    {
        using HttpClient httpClient = new();
        TokenResponse tokenResponse = await httpClient.RequestAuthorizationCodeTokenAsync(request, cancellationToken);
        return tokenResponse;
    }

    public async Task<TokenResponse> RequestRefreshTokenAsync(RefreshTokenRequest request,
        CancellationToken cancellationToken = default)
    {
        using HttpClient httpClient = new();
        TokenResponse tokenResponse = await httpClient.RequestRefreshTokenAsync(request, cancellationToken);
        return tokenResponse;
    }
}
