﻿using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using HotChocolate;
using HotChocolate.AspNetCore.Authorization;
using HotChocolate.Types;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Result = CoverGo.DomainUtils.Result;
using SSOConfig = CoverGo.Auth.Domain.SSOConfig;

namespace CoverGo.Auth.Application.GraphQl.SSO;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class SSOConfigsMutation
{
    readonly MongoSSOConfigsRepository _repo;
    readonly PermissionValidator _permissionValidator;

    public SSOConfigsMutation([Service] MongoSSOConfigsRepository repo, PermissionValidator permissionValidator)
    {
        _repo = repo;
        _permissionValidator = permissionValidator;
    }

    [Authorize]
    public async Task<Result> AddSSOConfig([GlobalState] string tenantId, [GlobalState] ClaimsIdentity identity,
        SSOConfig input,
        CancellationToken cancellationToken)
    {
        await _permissionValidator.AuthorizeWithAsync(identity, UserClaim.CreateSSOConfig.ToString());

        return await _repo.AddSSOConfigToTenantSettingsAsync(tenantId, input, cancellationToken);
    }

    [Authorize]
    public async Task<Result> RemoveSSOConfig([GlobalState] string tenantId, [GlobalState] ClaimsIdentity identity,
        string id,
        CancellationToken cancellationToken)
    {
        await _permissionValidator.AuthorizeWithAsync(identity, UserClaim.DeleteSSOConfig.ToString());

        return await _repo.RemoveSSOConfigFromTenantSettingsAsync(tenantId, id, cancellationToken);
    }
}
