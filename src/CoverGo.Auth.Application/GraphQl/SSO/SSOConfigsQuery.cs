﻿using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using HotChocolate;
using HotChocolate.Types;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace CoverGo.Auth.Application.GraphQl.SSO;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class SSOConfigsQuery
{
    readonly MongoSSOConfigsRepository _repo;
    readonly ILogger<SSOConfigsQuery> _logger;

    public SSOConfigsQuery(
        [Service] MongoSSOConfigsRepository repo,
        [Service] ILogger<SSOConfigsQuery> logger)
    {
        _repo = repo;
        _logger = logger;
    }

    public async Task<SSOConfig> GetSSOConfigById(string id,
        CancellationToken cancellationToken)
    {
        SSOConfig config = await _repo.GetConfigByIdAsync(id, true, cancellationToken);
        _logger.LogInformation("[GetSSOConfigById] SSO config found for issuerId:{issuerId} is : {config}", id, JsonConvert.SerializeObject(config));
        return config;
    }
    public async Task<SSOConfig> GetConfigByIdAndClientId(string id, string clientId,
        CancellationToken cancellationToken)
    {
        SSOConfig config = await _repo.GetConfigByIdAndClientIdAsync(id, clientId, true, cancellationToken);
        _logger.LogInformation("[GetConfigByIdAndClientId] SSO config found for issuerId:{issuerId} is : {config}", id, JsonConvert.SerializeObject(config));
        return config;
    }
}
