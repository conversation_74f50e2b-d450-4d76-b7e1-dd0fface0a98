﻿using IdentityModel.Client;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.GraphQl.SSO;

public interface ITokenService
{
    Task<TokenResponse> RequestAuthorizationCodeTokenAsync(AuthorizationCodeTokenRequest request, CancellationToken cancellationToken = default);

    Task<TokenResponse> RequestRefreshTokenAsync(RefreshTokenRequest request, CancellationToken cancellationToken = default);
}
