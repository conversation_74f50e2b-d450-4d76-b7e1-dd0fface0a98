﻿using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.SSO;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using HotChocolate;
using HotChocolate.Types;
using IdentityModel.Client;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.GraphQl.SSO;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class SSOAccessTokenQuery
{
    readonly MongoSSOConfigsRepository _repo;
    readonly ILogger<SSOAccessTokenQuery> _logger;
    readonly MongoLoginRepository _loginRepo;
    readonly ITokenService _tokenService;

    public SSOAccessTokenQuery(
        [Service] MongoSSOConfigsRepository repo,
        [Service] ILogger<SSOAccessTokenQuery> logger,
        [Service] MongoLoginRepository loginRepo,
        [Service] ITokenService tokenService)
    {
        _repo = repo;
        _logger = logger;
        _loginRepo = loginRepo;
        _tokenService = tokenService;
    }

    public async Task<SSOTokenReponse> GetSSOAccessToken(SSOAuthCodeInput ssoAuthCodeinput,
        CancellationToken cancellationToken)
    {
        SSOConfig ssoConfig = await GetSsoConfigAsync(ssoAuthCodeinput.Id, cancellationToken);

        TokenResponse tokenResponse = await RequestAuthorizationCodeTokenAsync(ssoAuthCodeinput, cancellationToken, ssoConfig);

        return await HandleTokenResponseAsync(cancellationToken, tokenResponse, ssoConfig, () => "Failed to exchange authorization code for token.");
    }

    public async Task<SSOTokenReponse> GetSSORefreshToken(SSORefreshTokenInput ssoRefreshTokeninput,
        CancellationToken cancellationToken)
    {
        SSOConfig ssoConfig = await GetSsoConfigAsync(ssoRefreshTokeninput.Id, cancellationToken);

        TokenResponse tokenResponse = await RequestRefreshTokenAsync(ssoRefreshTokeninput, cancellationToken, ssoConfig);

        return await HandleTokenResponseAsync(cancellationToken, tokenResponse, ssoConfig, () => "Failed to refresh tokens.");
    }

    async Task<TokenResponse> RequestAuthorizationCodeTokenAsync(SSOAuthCodeInput ssoAuthCodeInput,
        CancellationToken cancellationToken, SSOConfig ssoConfig)
    {
        TokenResponse tokenResponse = await _tokenService.RequestAuthorizationCodeTokenAsync(new()
        {
            Address = ssoAuthCodeInput.TokenEndpointUrl,
            ClientId = ssoConfig.ClientId,
            ClientSecret = ssoConfig.ClientSecret,
            Code = ssoAuthCodeInput.AuthorizationCode,
            RedirectUri = ssoAuthCodeInput.RedirectUrl
        }, cancellationToken: cancellationToken);
        return tokenResponse;
    }

    async Task<TokenResponse> RequestRefreshTokenAsync(SSORefreshTokenInput ssoRefreshTokenInput,
        CancellationToken cancellationToken, SSOConfig ssoConfig)
    {
        TokenResponse tokenResponse = await _tokenService.RequestRefreshTokenAsync(new()
        {
            Address = ssoRefreshTokenInput.TokenEndpointUrl,
            ClientId = ssoConfig.ClientId,
            ClientSecret = ssoConfig.ClientSecret,
            RefreshToken = ssoRefreshTokenInput.RefreshToken
        }, cancellationToken: cancellationToken);
        return tokenResponse;
    }

    async Task<SSOConfig> GetSsoConfigAsync(string id, CancellationToken cancellationToken)
    {
        SSOConfig ssoConfig = await _repo.GetConfigByIdAsync(id, false, cancellationToken);
        if (ssoConfig == null)
        {
            throw new SecurityTokenException($"Could not find SSO configuration for id {id}");
        }

        return ssoConfig;
    }

    async Task ValidateExistingLoginByEmail(SSOConfig ssoConfig, string token, CancellationToken cancellationToken)
    {
        JwtSecurityToken jwtSecurityToken = new JwtSecurityTokenHandler().ReadJwtToken(token);
        string email = jwtSecurityToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Email)?.Value
                ?? jwtSecurityToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Sub)?.Value;
        bool loginExists = await _loginRepo.DoesActiveLoginExistByEmail(ssoConfig.TenantId, email, cancellationToken);

        if (!loginExists)
            throw new SecurityTokenException($"Email validation is required but no login with a matching email '{email}' was found.");
    }

    async Task<SSOTokenReponse> HandleTokenResponseAsync(CancellationToken cancellationToken, TokenResponse tokenResponse, SSOConfig ssoConfig, Func<string> getErrorMessage)
    {
        if (tokenResponse.IsError)
        {
            _logger.LogError(tokenResponse.Exception, "{ErrorType} {Error} {ErrorDescription}", tokenResponse.ErrorType,
                tokenResponse.Error, tokenResponse.ErrorDescription);
            throw new SecurityTokenException(getErrorMessage());
        }

        string token = ssoConfig.UseIdentityToken.HasValue && ssoConfig.UseIdentityToken.Value
            ? tokenResponse.IdentityToken
            : tokenResponse.AccessToken;
        if (ssoConfig.ValidateExistingLoginByEmail ?? false)
            await ValidateExistingLoginByEmail(ssoConfig, token, cancellationToken);

        return new()
        {
            AccessToken = token,
            RefreshToken = tokenResponse.RefreshToken,
            ExpiresIn = tokenResponse.ExpiresIn,
        };
    }
}
