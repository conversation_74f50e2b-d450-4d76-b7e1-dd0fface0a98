﻿using HotChocolate.Types;

namespace CoverGo.Auth.Application.GraphQl.App
{
    public class AppUrlType : ObjectType<Domain.App>
    {
        protected override void Configure(IObjectTypeDescriptor<Domain.App> descriptor)
        {
            descriptor.BindFieldsExplicitly();
            descriptor.Name(new HotChocolate.NameString("AppUrl"));
            descriptor.Field((o) => o.AppId);
            descriptor.Field((o) => o.UrlRouting);
        }
    }
}
