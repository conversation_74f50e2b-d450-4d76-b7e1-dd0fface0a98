﻿using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Domain;
using HotChocolate;
using HotChocolate.Types;
using MoreLinq;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.GraphQl.App
{
    [ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
    public class AppConfigurationQuery
    {
        private const string _appConfig = @"{
	emails: {
		emailTemplateId: ""d1716ec0-1b97-4b74-8aab-cf3a8cd2eb41"",
		emailFrom: ""<EMAIL>"",
		emailSubject: ""Set your password"",
		forgotPasswordEmailTemplateId: ""a0b598ed-de3a-41a5-b211-2a59285a5121"",
		forgotPasswordEmailFrom: ""<EMAIL>"",
		forgotPasswordEmailSubject: ""Forgot password"",
	},

	dateFormat: ""DD/MM/YYYY"",
	dateTimeFormat: ""DD/MM/YYYY HH:mm:ss"",
	name: ""Company ABC"",
	logoUrl: ""/logo-blue.svg"",
	fileSystem: {
		bucketName: ""coverlibrary--uat"",
	},

	scripts: {
		pricingScriptSourceUrl: ""scripts/pricing_usage_based_group_0-0-1_bundled.txt"",
		underwritingScriptSourceUrl: ""scripts/underwriting_usage_based_group_0-0-1_bundled.txt"",
		cancellationScriptSourceUrl: ""scripts/cancellation_usage_based_group_0-0-1_bundled.txt"",
		policyYearBasedIndividualPricing: ""scripts/pricing_years_based_individual_bundled.txt"",
	},

	statuses: {
		initialCaseStatus: ""In Progress"",
		case: [
			{ name: ""In Progress"", value: ""In Progress"" },
			{ name: ""Accepted"", value: ""Accepted"" },
			{ name: ""Closed"", value: ""Closed"" },
		],

		initialProposalStatus: ""In Progress"",
		proposal: [
			{ name: ""In Progress"", value: ""In Progress"" },
			{ name: ""Accepted"", value: ""Accepted"" },
			{ name: ""Closed"", value: ""Closed"" },
		],
	},

	schemas: {
		arrayFields: {
			type: ""object"",
			properties: {
				beneficiaries: {
					type: ""array"",
					items: {
						type: ""object"",
						properties: {
							englishFirstName: {
								type: ""string"",
							},
							englishLastName: {
								type: ""string"",
							},
							relationship: {
								type: ""string"",
							},
							share: {
								type: ""number"",
							},
						},
					},
				},
			},
		},
		otherFields: {
			type: ""object"",
			properties: {
				hkid: {
					type: ""string"",
				},
			},
		},
	},

	menu: [
		{
			id: ""clients"",
			label: ""Clients"",
			description: """",
            icon: """",

            type: ""headline"",
		},
		{
			id: ""individuals"",
			label: ""Individuals"",
			description: ""Individuals"",
			icon: ""smile"",
			to: { name: ""ViewIndividualList"" },
		},
		{
            id: ""companies"",
			label: ""Companies"",
			description: ""Companies"",
			icon: ""shop"",
			to: { name: ""ViewCompanyList"" },
		},
		{
            id: ""lifecycle"",
			label: ""Contracts"",
			description: """",

            icon: """",

            type: ""headline"",
		},
		{
            id: ""products"",
			label: ""Products"",
			description: ""Products"",
			icon: ""tag"",
			to: { name: ""ViewProductList"" },
		},
		{
            id: ""quotes"",
			label: ""Quotes"",
			description: ""Quotes"",
			icon: ""fileText"",
			to: { name: ""ViewCaseList"" },
		},
		{
            id: ""policies"",
			label: ""Policies"",
			description: ""Policies"",
			icon: ""fileDone"",
			to: { name: ""ViewPolicyList"" },
		},
		{
            id: ""renewals"",
			label: ""Renewals"",
			description: ""Renewals"",
			icon: ""fileSync"",
			to: { name: ""ViewRenewalList"" },
		},
		{
            id: ""claims"",
			label: ""Claims"",
			description: ""Claims"",
			icon: ""safety"",
			to: { name: ""ViewClaimsList"" },
		},
		{
            id: ""lifecycle"",
			label: ""Transactions"",
			description: """",

            icon: """",

            type: ""headline"",
		},
		{
            id: ""transactions"",
			label: ""Transactions"",
			description: ""Transactions"",
			icon: ""creditCard"",
			to: { name: ""ViewTransactionList"" },
		},
		{
            id: ""stakeholders"",
			label: ""Stakeholders"",
			description: """",

            icon: """",

            type: ""headline"",
		},
		{
            id: ""users"",
			label: ""Internal Users"",
			description: ""Internal Users"",
			icon: ""user"",
			to: { name: ""ViewInternalUser"" },
		},
		{
            id: ""agents"",
			label: ""Agents"",
			description: ""Agents"",
			icon: ""team"",
			to: { name: ""View403"" },
		},
		{
            id: ""hierarchy"",
			label: ""Hierarchy"",
			description: ""Hierarchy"",
			icon: ""Apartment"",
			to: { name: ""View403"" },
		},
		{
            id: ""others"",
			label: ""Others"",
			description: """",

            icon: """",

            type: ""headline"",
		},
		{
            id: ""settings"",
			label: ""Settings"",
			description: ""Settings"",
			icon: ""setting"",
			to: { name: ""ViewSettings"" },
		},
	],
}";
        readonly ITenantRepository _repo;

        public AppConfigurationQuery([Service] ITenantRepository repo) =>
            _repo = repo;

        public async Task<AppConfiguration> GetAppConfigurationFromUrl(string appUrl, CancellationToken cancellationToken)
        {
            TenantIdAndAppId tenantIdAndAppId;
            try
            {
                tenantIdAndAppId = await _repo.GetTenantIdAndAppIdFromUrl(appUrl, cancellationToken);
            }
            catch (ArgumentOutOfRangeException)
            {
                return GenerateRandomConfiguration(appUrl);
            }
            IEnumerable<Domain.App> apps = await _repo.GetAppsAsync(tenantIdAndAppId.TenantId, null, null, null, null, cancellationToken);
            Domain.App app = apps.Single((application) => application.AppId == tenantIdAndAppId.AppId);
            return new AppConfiguration()
            {
                TenantId = tenantIdAndAppId.TenantId,
                AppId = tenantIdAndAppId.AppId,
                AppConfig = app.AppConfig,
                OtherApps = apps.Where((application) => application.AppId != tenantIdAndAppId.AppId)
            };
        }

        private AppConfiguration GenerateRandomConfiguration(string appUrl)
        {
            Random random = new Random(appUrl.GetHashCode());
            string[] clientIds = { "cover_app", "covergo_crm", "admin", "covergo_studio" };
            string[] tenantIds = {
                "alianz", "axa", "prudential", "covergo", "nippon", "bh", "metlife", "japan_post", "L&G", "pin_an",
                "generali", "china_life", "manulife", "aviva", "kyosai", "daiichi", "aig", "cnp", "aegon", "lic",
                "amundi", "zurich", "meiji_yasouda", "sumitomo", "nylic" };

            string tenantId = tenantIds[random.Next(tenantIds.Length)];
            return new AppConfiguration()
            {
                AppId = clientIds[random.Next(clientIds.Length)],
                TenantId = tenantId,
                AppConfig = GetRandomAppConfig(random, tenantId),
                OtherApps = new Domain.App[0]
            };
        }

        private string GetRandomAppConfig(Random random, string tenantId)
        {
            byte[] tenantBytes = Encoding.UTF32.GetBytes(tenantId);
            if (tenantBytes.Length > 16)
            {
                tenantBytes = tenantBytes.Take(16).ToArray();
            }
            else
            {
                tenantBytes = tenantBytes.Pad(16).ToArray();
            }
            JObject configObject = JObject.Parse(_appConfig);
            JObject emailsObject = configObject.Value<JObject>("emails");
            emailsObject["emailTemplateId"] = new Guid(tenantBytes).ToString("B");
            emailsObject["forgotPasswordEmailTemplateId"] = new Guid(tenantBytes.Reverse().ToArray()).ToString("B");
            configObject["name"] = tenantId.ToUpper().Replace('_', ' ');
            JObject fileSystemObject = configObject.Value<JObject>("fileSystem");
            fileSystemObject["bucketName"] = $"{tenantId}_files";
            JArray menu = configObject.Value<JArray>("menu");
            int numberOfItemsToRemove = random.Next(menu.Count);
            for (int i = 0; i < numberOfItemsToRemove; i++)
            {
                menu.RemoveAt(random.Next(menu.Count));
            }
            return configObject.ToString(Newtonsoft.Json.Formatting.None);
        }
    }
}
