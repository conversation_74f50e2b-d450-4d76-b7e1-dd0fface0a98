﻿using CoverGo.Applications.Http.GraphQl.Services.SSO;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.GraphQl;

public class LocalSSOAuthClient : IAuthServiceGraphQLClient
{
    readonly MongoSSOConfigsRepository _ssoConfigRepo;
    readonly MongoLoginRepository _loginRepo;
    public LocalSSOAuthClient(MongoSSOConfigsRepository ssoConfigRepo, MongoLoginRepository loginRepo)
    {
        _ssoConfigRepo = ssoConfigRepo;
        _loginRepo = loginRepo;
    }

    public SSOConfiguration MapSSOConfig(SSOConfig config)
        => config != null
        ? new SSOConfiguration
        {
            Id = config.Id,
            IdClaim = config.IdClaim,
            KeyUrl = config.KeyUrl,
            KeyUrlClaim = config.KeyUrlClaim,
            ClaimsMap = config.ClaimsMap,
            AdditionalClaims = config.AdditionalClaims,
            TenantId = config.TenantId,
            ClientId = config.ClientId,
            ValidateExistingLoginByEmail = config.ValidateExistingLoginByEmail ?? false
        }
        : null;

    public async Task<SSOConfiguration> GetSSOConfiguration(string id)
    {
        SSOConfig config = await _ssoConfigRepo.GetConfigByIdAsync(id, true);
        return MapSSOConfig(config);
    }

    public async Task<bool> DoesActiveLoginExistByEmail(string tenantId, string email) =>
        await _loginRepo.DoesActiveLoginExistByEmail(tenantId, email, new CancellationToken());
}
