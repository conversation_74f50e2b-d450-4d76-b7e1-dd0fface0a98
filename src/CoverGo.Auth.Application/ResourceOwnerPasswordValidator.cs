﻿using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.FeatureManagement;
using IdentityModel;
using IdentityServer4.Models;
using IdentityServer4.Validation;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application
{
    public class ResourceOwnerPasswordValidator : IResourceOwnerPasswordValidator
    {
        private readonly UserManager<MongoLoginDao> _userManager;
        private readonly IHttpContextAccessor _accessor;
        private readonly INotificationService _notificationService;
        private readonly AuthService _authService;
        private readonly IMultiTenantFeatureManager _multiTenantFeatureManager;

        public ResourceOwnerPasswordValidator(
            UserManager<MongoLoginDao> userManager,
            IHttpContextAccessor accessor,
            INotificationService notificationService,
            AuthService authService,
            IMultiTenantFeatureManager multiTenantFeatureManager)
        {
            _userManager = userManager;
            _accessor = accessor;
            _notificationService = notificationService;
            _authService = authService;
            _multiTenantFeatureManager = multiTenantFeatureManager;
        }

        public async Task ValidateAsync(ResourceOwnerPasswordValidationContext context)
        {
            //NOTE: refactor tenantId extraction to some kind of a generic way
            string tenantId = _accessor.HttpContext.Request.PathBase.Value.Remove(0, 1);
            bool enableFindLoginByNameOrEmail = await _multiTenantFeatureManager.IsEnabled("EnableFindLoginByNameOrEmail", tenantId);

            MongoLoginDao user = enableFindLoginByNameOrEmail && MailAddress.TryCreate(context.UserName, out _)
                ? await _userManager.FindByEmailAsync(context.UserName)
                : await _userManager.FindByNameAsync(context.UserName);

            if (user == null)
            {
                context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "invalid_username_or_password");
                return;
            }
            if (!user.IsActive)
            {
                context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "User is not active.");
                return;
            }

            PasswordValidators passwordValidators = await _authService.GetPasswordValidatorsAsync(tenantId, context.Request.ClientId, default)
                ?? (await _authService.GetPasswordValidatorsAsync(tenantId, "default", default)) ?? new PasswordValidators();

            bool isLockedOut = await IsUserLockedOut(user);
            if (isLockedOut)
            {
                //only for tahoe, it is not a good practice to return the exact error, it should be a generic error
                context.Result = new GrantValidationResult { Error = "locked_out", ErrorDescription = "Too many unsuccessful attempts, the account is suspended. Please reset password.", IsError = true };

                return;
            }

            if (!await _userManager.IsEmailConfirmedAsync(user))
            {
                return;
            }

            if (!await _userManager.CheckPasswordAsync(user, context.Password))
            {
                if (user.IgnoreAccountLockout) return;

                await _userManager.AccessFailedAsync(user);

                bool isLockedOut2 = await IsUserLockedOut(user);
                if (isLockedOut2)
                {
                    await _userManager.SetLockoutEnabledAsync(user, true);

                    App app = (await _authService.GetAppsAsync(tenantId, new AppWhere { AppId = context.Request.ClientId }, null, null, null, default))?.FirstOrDefault();

                    string clientEmail = app?.Email;
                    string senderName = app?.EmailSenderName;

                    var renderContent = JToken.FromObject(new
                    {
                        email = user.Email,
                        loginId = user.Id,
                        //code = encodedCode,
                    });

                    await _notificationService.SendAsync(tenantId, new SendNotificationCommand
                    {
                        EmailMessage = new EmailMessage
                        {
                            To = user.Email,
                            From = clientEmail ?? "<EMAIL>",
                            FromName = senderName ?? "CoverGo",
                            TemplateRendering = passwordValidators?.LockoutEmailTemplateId != null ? new TemplateRendering
                            {
                                TemplateId = passwordValidators?.LockoutEmailTemplateId,
                                Input = new RenderParameters
                                {
                                    Content = renderContent
                                }
                            } : null,
                            HtmlContent = passwordValidators?.LockoutEmailTemplateId == null ? "Your account has been locked out. You can try passwords again later." : null
                        },
                        UseConfig = app.UseNotificationConfig
                    }, default);

                    //only for tahoe, it is not a good practice to return the exact error, it should be a generic error
                    context.Result = new GrantValidationResult { Error = "locked_out", ErrorDescription = "Too many unsuccessful attempts, the account is suspended. Please reset password.", IsError = true };
                }

                return;
            }

            if (!user.IgnorePasswordLifespan)
            {
                int? passwordLifetime = user.IsTemporaryPassword ? passwordValidators?.TempPasswordLifespanInSeonds : passwordValidators?.PasswordLifespanInSeconds;
                if (passwordLifetime != null && user.PasswordLastUpdated != null && (DateTime.UtcNow - user.PasswordLastUpdated).Value.TotalSeconds > passwordLifetime)
                {
                    context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "Password expired.");
                    return;
                }
            }

            if (user.AccessFailedCount != 0)
                await _userManager.ResetAccessFailedCountAsync(user);

            if (!user.Claims.Exists(c => c.Type == "role" && c.Value == "admin"))
            {
                IEnumerable<PermissionGroup> groups = await _authService
                    .GetPermissionGroupsAsync(tenantId, user.Claims.Where(c => c.Type == "groups").Select(c => c.Value), default);

                IReadOnlyCollection<string> groupsClientIds = groups
                    .SelectMany(g =>
                    (
                        g.TargettedPermissions?.Any(p => p.Key == "clientId") == true
                            ? g.TargettedPermissions.Where(p => p.Key == "clientId")
                            : new Dictionary<string, IEnumerable<string>>()
                    ).SelectMany(p => p.Value))
                    .ToArray();

                IEnumerable<string> authorizedClientIds = user.Claims
                    .Where(c => c.Type == "clientId")
                    .Select(c => c.Value)
                    .Concat(groupsClientIds)
                    .Distinct();
                if (!authorizedClientIds.Any(i => i == context.Request.Client.ClientId || i == "all"))
                    return;
            }

            IEnumerable<Claim> distinctClaims = ClaimExtensions.GetClaims(tenantId, context.Request.Client.ClientId, user);

            App client = (await _authService.GetAppsAsync(tenantId, new AppWhere { AppId = context.Request.ClientId },
                null, null, null, default))?.FirstOrDefault();

            bool isPasswordExpired = IsPasswordExpired(tenantId, user, client);
            if (isPasswordExpired)
            {
                context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "Password expired.");
                return;
            }

            bool isTwoFactorRequired = RequiresTwoFactorAsync(tenantId, user, client);

            if (isTwoFactorRequired)
            {
                string twoFactorToken = context.Request.Raw["twoFactorToken"];
                string twoFactorTemplateId = context.Request.Raw["twoFactorTemplateId"];

                if (string.IsNullOrWhiteSpace(twoFactorToken))
                {
                    string token = await _userManager.GenerateTwoFactorTokenAsync(user, "Email");

                    context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "Two factor required.", new Dictionary<string, object> { { "requiresTwoFactor", true } });

                    if (!string.IsNullOrWhiteSpace(twoFactorTemplateId))
                    {
                        await SendTwoFactorEmailAsync(tenantId, token, twoFactorTemplateId, client, user);
                        await _authService.UpdateLatestOtpAsync(tenantId, user.Id, token, default);
                    }

                    return;
                }

                bool verified = await _userManager.VerifyTwoFactorTokenAsync(user, "Email", twoFactorToken);

                if (!verified)
                {
                    context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "Otp is invalid. Try again.");
                    return;
                }

                if (!string.IsNullOrWhiteSpace(user.LatestOtp) && user.LatestOtp != twoFactorToken)
                {
                    context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "Otp is expired. Try again.");
                    return;
                }
            }

            await _authService.AddEventAsync(tenantId, new LoginEvent(user.Id, LoginEventType.requestAccessTokenSuccess, DateTime.UtcNow) { Values = JToken.FromObject(new Dictionary<string, string> { { "requestedById", user.Id } }) }, default);

            context.Result = new GrantValidationResult(user.Id, OidcConstants.AuthenticationMethods.Password, distinctClaims);
        }

        private async Task SendTwoFactorEmailAsync(string tenantId, string token, string templateId, App app, MongoLoginDao user)
        {
            string clientEmail = app?.Email ?? "<EMAIL>";
            string senderName = app?.EmailSenderName ?? "CoverGo";

            SendNotificationCommand sendNotificationCommand = new();
            if (!string.IsNullOrWhiteSpace(templateId))
            {
                sendNotificationCommand.EmailMessage = new EmailMessage
                {
                    From = clientEmail,
                    FromName = senderName,
                    To = user.Email,
                    Subject = app?.OneTimePasswordEmailSubject ?? "One time password",
                    TemplateRendering = new TemplateRendering { TemplateId = templateId }
                };
                sendNotificationCommand.AddOtpToEmailMessage(token);
            }

            await _notificationService.SendAsync(tenantId, sendNotificationCommand, new CancellationToken());
        }


        private bool RequiresTwoFactorAsync(string tenantId, MongoLoginDao user, App app)
        {
            if (user.Claims.Exists(c => c.Type == "role" && (c.Value == "admin" || c.Value == "guest")))
                return false;

            bool applicationRequired = app?.RequiresEmail2FA ?? false;

            return applicationRequired;
        }

        private bool IsPasswordExpired(string tenantId, MongoLoginDao user, App app)
        {
            if (user.Claims.Exists(c => c.Type == "role" && (c.Value == "admin" || c.Value == "guest")))
                return false;

            if (app == null || !app.PasswordExpiryLifespan.HasValue)
                return false;

            return user.PasswordLastUpdated < DateTime.UtcNow - app.PasswordExpiryLifespan;
        }

        private async Task<bool> IsUserLockedOut(MongoLoginDao user) => await _userManager.IsLockedOutAsync(user) || user.LockoutEndDateUtc > DateTime.UtcNow;
    }
}
