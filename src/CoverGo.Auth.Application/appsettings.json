﻿{
  "Serilog": {
    "Using": ["Serilog.Sinks.Console"],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore.Server.Kestrel": "Fatal",
        "System": "Warning"
      }
    },

    "Enrich": ["FromLogContext"],
    "WriteTo": [
      {
        "Name": "Console"
      }
    ],

    "Properties": {
      "Service": "covergo-auth"
    }
  },
  "FeatureManagement": {
    "PermissionV2": false,
    "PasswordComplianceCheck": {
      "EnabledFor": [
        {
          "Name": "Tenants",
          "Parameters": {
            "Tenants": ["msig_dev", "msig_uat", "msig_qa", "msig_prod"]
          }
        }
      ]
    },
    "Claims3Permission": {
      "EnabledFor": [
        {
          "Name": "Tenants",
          "Parameters": {
            "Tenants": [
              "aevitae_dev",
              "aevitae_uat",
              "aevitae_qa",
              "aevitae_prod"
            ]
          }
        }
      ]
    },
    "NewForgotPasswordFlow": {
      "EnabledFor": [
        {
          "Name": "Tenants",
          "Parameters": {
            "Tenants": ["axaTh_test", "axaTh_uat", "axaTh_prod", "axaTh_preprod"]
          }
        }
      ]
    },
    "EnableContentRestriction": {
      "EnabledFor": [
        {
          "Name": "Tenants",
          "Parameters": {
            "Tenants": ["msig_test", "covergo", "msig_dev", "msig_uat", "msig_qa", "msig_prod"]
          }
        }
      ]
    },
    "EnableFindLoginByNameOrEmail": {
      "EnabledFor": [
        {
          "Name": "Tenants",
          "Parameters": {
            "Tenants": ["asia_dev", "asia_uat", "asia_preprod", "asia_prod"]
          }
        }
      ]
    }
  },
  "CoverGoClaims3ServiceSettings": {
    "MaxPageSize": 50
  }
}
