﻿{
  "Serilog": {
    "MinimumLevel": {
      "Override": {
        "Microsoft": "Information",
        "Microsoft.AspNetCore.Server.Kestrel": "Information",
        "System": "Information"
      }
    }
  },
  "serviceUrls": {
    "logging": "http://localhost:9200/",
    "gateway": "http://localhost:60060/",
    "auth": "http://localhost:60000/",
    "users": "http://localhost:60010/",
    "notifications": "http://localhost:60070/",
    "policies": "http://localhost:60050/",
    "cases": "http://localhost:60600/",
    "transactions": "http://localhost:60120/",
    "advisor": "http://localhost:60110/",
    "claims": "http://localhost:60080/",
    "claims3": "http://localhost:60922/",
    "channelManagement": "http://localhost:64483/",
    "scheduler": "http://localhost:63570/"
  },
  "FeatureManagement": {
    "PermissionV2": true
  },
  "LogRequestBodyEnabled": true
}
