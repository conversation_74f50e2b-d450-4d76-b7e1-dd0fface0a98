﻿{
  "Serilog": {
    // When doing development we use normal formating and avoid doing json output
    "Using": [
      "Serilog.Sinks.Console"
    ],

    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore.Server.Kestrel": "Fatal",
        "System": "Debug"
      }
    }
  },
  "serviceUrls": {
    "logging": "http://logging-2A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "auth": "http://auth-2A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "users": "http://users-2A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "notifications": "http://notification-2A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "policies": "http://policies-2A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "cases": "http://cases-2A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "transactions": "http://localhost:60120/",
    "advisor": "http://localhost:60110/",
    "claims": "http://localhost:60080/"
  }
}