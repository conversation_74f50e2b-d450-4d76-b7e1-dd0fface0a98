﻿using CoverGo.Auth.Domain;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application
{
    //NOTE: generate UtilsController automatically from API Reference file. Put logic into controller interface
    [Route("api/auth/utils")]
    public class UtilsController : ControllerBase
    {
        private readonly AuthService _authService;

        public UtilsController(AuthService authService)
        {
            _authService = authService;
        }

        [HttpGet("indexstats")]
        public async Task<ActionResult<IEnumerable<JToken>>> GetIndexStats(string colName, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            IEnumerable<JToken> indexResults = await _authService.GetIndexStats(tenantId, colName, cancellationToken);
            return Ok(indexResults);
        }
    }
}
