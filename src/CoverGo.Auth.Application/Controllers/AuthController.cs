using CoverGo.Auth.Application.Extensions;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Domain.PermissionSchemas;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using IdentityServer4;
using IdentityServer4.Models;
using IdentityServer4.Stores;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.MongoDB;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Proxies.Gateway;
using CoverGo.Auth.Application.GraphQl.SSO;

namespace CoverGo.Auth.Application.Controllers;

//NOTE: generate AuthController automatically from API Reference file. Put logic into contoller interface
[Route("api/v1/auth")]
public class AuthController : ControllerBase
{
    readonly IResourceStore _resourceStore;
    readonly IdentityServerTools _identityServerTools;
    readonly INotificationService _notificationService;
    readonly IEntityService _entityService;
    readonly ILogger _logger;
    readonly IEventStore _eventStore;
    readonly JsonSerializer _jsonSerializer;
    readonly AuthService _authService;
    readonly IPermissionService _permissionService;
    readonly IFeatureManager _featureManager;
    readonly IMultiTenantFeatureManager _multiTenantFeatureManager;
    readonly CustomPasswordResetTokenProviderContext _customPasswordResetTokenProviderContext;
    readonly IGatewayService _gatewayService;
    readonly LoginMutation _loginMutation;

    public AuthController(
        IdentityServerTools identityServerTools,
        INotificationService notificationService,
        IEntityService entityService,
        IResourceStore resourceStore,
        ILogger<AuthController> logger,
        IEventStore eventStore,
        AuthService authService,
        IPermissionService permissionService,
        IFeatureManager featureManager,
        IMultiTenantFeatureManager multiTenantFeatureManager,
        CustomPasswordResetTokenProviderContext customPasswordResetTokenProviderContext,
        IGatewayService gatewayService,
        LoginMutation loginMutation)
    {
        _identityServerTools = identityServerTools;
        _notificationService = notificationService;
        _entityService = entityService;
        _resourceStore = resourceStore;
        _logger = logger;
        _eventStore = eventStore;
        _authService = authService;
        _permissionService = permissionService;
        _featureManager = featureManager;
        _multiTenantFeatureManager = multiTenantFeatureManager;
        _customPasswordResetTokenProviderContext = customPasswordResetTokenProviderContext;
        _gatewayService = gatewayService;
        _loginMutation = loginMutation;
        var settings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            TypeNameHandling = TypeNameHandling.Auto,
            NullValueHandling = NullValueHandling.Ignore
        };
        settings.Converters.Add(new StringEnumConverter());

        _jsonSerializer = JsonSerializer.Create(settings);
    }

    [HttpGet("tokenasloginid/{loginId}/{appId}")]
    public async Task<Token> GetTokenAsLoginId(string loginId, string appId, CancellationToken cancellationToken) =>
        await GetTokenAsLoginIdWithAddedClaims(loginId, appId, new Dictionary<string, string>(), cancellationToken);

    [HttpPost("tokenasloginid/{loginId}/{appId}")]
    public async Task<Token> GetTokenAsLoginIdWithAddedClaims(string loginId,
        string appId,
        [FromBody] IDictionary<string, string> additionalClaims,
        CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
        if (user == null)
        {
            _logger.LogError($"{tenantId}: tokenasloginid | The login '{loginId}' doesn't exist");
            return null;
        }

        var claims = ClaimExtensions.GetClaims(tenantId, appId, user).ToList();

        await ExcludeUnnecessaryPermissionClaimsAsync(tenantId, user, claims, cancellationToken);

        claims.Add(new Claim("sub", loginId));
        claims.AddRange(additionalClaims?.ToClaims() ?? Enumerable.Empty<Claim>());

        App app = await _authService.GetAppAsync(tenantId, appId, cancellationToken);
        if (app == null)
        {
            _logger.LogError($"{tenantId}: tokenasloginid | The app '{appId}' doesn't exist");
            return null;
        }

        string accessToken = await _identityServerTools.IssueJwtAsync(app.AccessTokenLifetime, claims).ToCancellable(cancellationToken);
        return new Token
        {
            AccessToken = accessToken
        };
    }

    async Task ExcludeUnnecessaryPermissionClaimsAsync(string tenantId, MongoLoginDao user, List<Claim> claims, CancellationToken cancellationToken)
    {
        const string userClaimValueAll = "all";

        IEnumerable<PermissionGroup> userPermissionGroups = await _permissionService.GetPermissionGroupsFromClaims(tenantId, user, cancellationToken);
        List<Claim> targetedClientIdPermissions = (await _permissionService.GetTargettedPermissionsFromGroupsAsync(tenantId, userPermissionGroups, cancellationToken))
            .SelectMany(p => p.Value.Distinct().Select(v => new Claim(p.Key, v)))
            .ToList();

        IEnumerable<Claim> allPermissions = targetedClientIdPermissions.Where(p => p.Value == userClaimValueAll);

        foreach (Claim permission in allPermissions)
            claims.RemoveAll(x => x.Type == permission.Type && x.Value != userClaimValueAll);
    }

    public class Token
    {
        public string AccessToken { get; set; }
    }

    [HttpGet("tenantSettings")]
    public Task<TenantSettings> GetTenantSettings(CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        return _authService.GetTenantSettingsAsync(tenantId, cancellationToken);
    }

    [HttpPost("tenantSettings/hosts/add")]
    public Task<Result> AddHostToTenantSettingsAsync([FromBody] string domain, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        return _authService.AddHostToTenantSettingsAsync(tenantId, domain, cancellationToken);
    }

    [HttpPost("tenantSettings/hosts/remove")]
    public Task<Result> RemoveHostFromTenantSettingsAsync([FromBody] string domain, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        return _authService.RemoveHostFromTenantSettingsAsync(tenantId, domain, cancellationToken);
    }

    [HttpPost("createTenant")]
    public async Task<Result> CreateTenant([FromBody] CreateTenantCommand command)
    {
        var admin = new MongoLoginDao
        {
            UserName = command.AdminSettings.Username,
            Email = command.AdminSettings.Email,
            LastModifiedAt = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            EmailConfirmed = true,
            Claims = new List<IdentityUserClaim> { new IdentityUserClaim(new Claim("role", "admin")) }
        };

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager("covergo");

        var passwordValidators = new List<IPasswordValidator<MongoLoginDao>>(_mongoUserManager.PasswordValidators);
        _mongoUserManager.PasswordValidators.Clear();
        IdentityResult createAdminResult = await _mongoUserManager.CreateAsync(admin, command.AdminSettings.Password);
        foreach (IPasswordValidator<MongoLoginDao> passwordValidator in passwordValidators)
            _mongoUserManager.PasswordValidators.Add(passwordValidator);

        if (!createAdminResult.Succeeded)
            return new Result { Status = "failure", Errors = createAdminResult.Errors.Select(e => $"{e.Code}: {e.Description}")?.ToList() };

        return new Result { Status = "success" };
    }

    [HttpGet("passwordvalidators")]
    public async Task<ActionResult<PasswordValidators>> GetPasswordValidatorsAsync(string clientId,
        CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        //NOTE: need to insert documents into their DB
        if (tenantId == "aag" || tenantId == "aag_uat")
            return new PasswordValidators { RequireDigit = true, RequireNonAlphanumeric = true, RequireLowercase = true, RequireUppercase = true };

        return (await _authService.GetPasswordValidatorsAsync(tenantId, clientId, cancellationToken))
               ?? (await _authService.GetPasswordValidatorsAsync(tenantId, "default", cancellationToken))
               ?? GenerateRandomPasswordValidators(tenantId, clientId);

        PasswordValidators GenerateRandomPasswordValidators(string tenantId, string clientId)
        {
            Random random = new Random(HashCode.Combine(tenantId, clientId));
            return new PasswordValidators()
            {
                RequireConfirmedEmail = random.Next() % 2 == 0,
                RequireConfirmPhoneNumber = random.Next() % 2 == 0,
                RequireDigit = random.Next() % 2 == 0,
                RequireLength = random.Next(20),
                RequireLetter = random.Next() % 2 == 0,
                RequireLowercase = random.Next() % 2 == 0,
                RequireNonAlphanumeric = random.Next() % 2 == 0,
                RequireUniqueChars = random.Next(10),
                RequireUppercase = random.Next() % 2 == 0,
            };
        }
    }

    public class ValidatePasswordCommand
    {
        public string Password { get; set; }
    }

    [HttpPost("passwordValidators/validate")]
    public async Task<Result> ValidatePasswordAsync(string clientId, [FromBody] ValidatePasswordCommand command)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        UserManager<MongoLoginDao> mongoUserManager = _authService.GetMongoUserManager(tenantId);
        return await ValidatePassword(mongoUserManager, tenantId, null, command.Password, true);
    }

    [HttpPost("logins/queryids")]
    public async Task<IEnumerable<string>> GetLoginIds([FromBody] LoginQueryArguments queryArguments,
        CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        // TEC-1535 Temporary fix for tcb as they cannot fix data of Prod
        if (IsTenantTcb(tenantId) && (queryArguments.Where?.Email_in?.Any() ?? false))
        {
            queryArguments.Where.Email_in = AddNormalizedEmailFilter(queryArguments.Where.Email_in);
        }

        IEnumerable<string> ids = await _authService.GetLoginIdsAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);
        return ids;
    }

    [HttpGet("logins/fromLoginId/{loginId}")]
    public async Task<ActionResult<Login>> GetLoginById(string loginId, string appId, int? version,
        CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);

        return Ok(await ReturnLoginFromMongoUser());

        async Task<Login> ReturnLoginFromMongoUser()
        {
            if (user == null)
                return null;

            if (!user.IsActive)
                return LoginExtensions.ToLogin(user, null);

            (ApiResource apiResource, PermissionGroup defaultPermissionGroup, bool enforceMfa) = await GetPermissionsArguments();

            Permissions permissions;

            if (await _featureManager.IsPermissionV2EnabledAsync() && version == 2)
            {
                _logger.LogInformation("PermissionV2 enabled.New permission flow will be used");
                permissions = await _permissionService.GetPermissionsV2Async(
                    tenantId, user, apiResource, defaultPermissionGroup, enforceMfa, cancellationToken);
            }
            else
            {
                _logger.LogInformation("PermissionV2 disabled.Old permission flow will be used");
                permissions = await _permissionService.GetPermissionsAsync(
                    tenantId, user, apiResource, defaultPermissionGroup, enforceMfa, cancellationToken);
            }

            return LoginExtensions.ToLogin(user, permissions);
        }

        async Task<(ApiResource apiResource, PermissionGroup defaultPermissionGroup, bool enforceMfa)> GetPermissionsArguments()
        {
            ApiResource apiResource = await _resourceStore.FindApiResourceAsync("all_user_claims");
            PermissionGroup defaultPermissionGroup =
                await _authService.GetDefaultPermissionGroupAsync(tenantId, cancellationToken);

            bool enforceMfa = false;
            if (appId != null)
            {
                App app = await _authService.GetAppAsync(tenantId, appId, cancellationToken);
                if (app?.Requires2FA ?? false)
                    enforceMfa = true;
            }

            return (apiResource, defaultPermissionGroup, enforceMfa);
        }
    }

    [HttpPost("logins/filter")]
    public async Task<ActionResult<IEnumerable<Login>>> GetLogins([FromBody] LoginQueryArguments queryArguments, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        IEnumerable<MongoLoginDao> users = await _authService.GetLoginsAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);
        IEnumerable<Permissions> permissions = new List<Permissions> { };

        if (!(queryArguments?.Where?.ExcludePermissions ?? false))
        {
            ApiResource apiResource = await _resourceStore.FindApiResourceAsync("all_user_claims");
            PermissionGroup defaultPermissionGroup = await _authService.GetDefaultPermissionGroupAsync(tenantId, cancellationToken);
            permissions = await TaskWhenAllWithThrottling.ParallelForEachAsync(users.Select(u => u),
                10, async (u) => await _permissionService.GetPermissionsAsync(tenantId, u, apiResource, defaultPermissionGroup,
                    false, cancellationToken));

        }
        IEnumerable<Login> logins = users.Select(u => LoginExtensions.ToLogin(u, permissions.FirstOrDefault(p => p.LoginId == u.Id)));

        return Ok(logins);
    }

    [HttpPost("logins/totalCount")]
    public async Task<long> GetTotalCount([FromBody] LoginWhere @where, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        //long totalCount = await Task.FromResult(ComposeQuery(where).LongCount());
        long totalCount = await _authService.GetLoginTotalCount(tenantId, @where, cancellationToken);
        return totalCount;
    }

    [HttpPost("logins/events")]
    public async Task<IEnumerable<EventLog>> GetEvents([FromBody] EventQuery query, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        List<LoginEventType> types = new List<LoginEventType>();

        foreach (string type in query.Types ?? new List<string> { })
        {
            if (Enum.TryParse(type, out LoginEventType enumType))
                types.Add(enumType);
        }

        IEnumerable<LoginEvent> loginEvents = await _authService.GetLoginEventsAsync(tenantId, types, query.Ids, cancellationToken, query.FromDate, query.ToDate);
        return HandleLoginLogs(loginEvents);
    }

    [HttpPost("logins/event")]
    public async Task<Result> AddLoginEvent([FromBody] LoginEvent @event, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        if (@event == null)
            return new Result { Status = "failure", Errors = new List<string> { "Event is null" } };
        await _authService.AddEventAsync(tenantId, @event, cancellationToken);
        return new Result { Status = "success" };
    }

    public static IEnumerable<DetailedEventLog> HandleLoginLogs(IEnumerable<LoginEvent> loginEvents) =>
        loginEvents.Select(e =>
            new DetailedEventLog
            {
                RelatedId = e.LoginId,
                Type = e.Type.ToString(),
                Value = e.Values?.ToString(),
                Timestamp = e.Timestamp,
                ById = GetById(e),
                Id = e.Id
            })?.ToList();

    private static string GetById(LoginEvent @event)
    {
        if (@event.Type == LoginEventType.createLogin) return @event.Values.Value<string>("createdById");
        else if (@event.Type == LoginEventType.addPermission) return @event.Values.Value<string>("addedById");
        else if (@event.Type == LoginEventType.removePermission) return @event.Values.Value<string>("removedById");
        else if (@event.Type == LoginEventType.confirmEmail) return @event.Values.Value<string>("confirmedById");
        else if (@event.Type == LoginEventType.resendEmail) return @event.Values.Value<string>("resentById");
        else if (@event.Type == LoginEventType.changePassword) return @event.Values.Value<string>("changedById");
        else if (@event.Type == LoginEventType.forgotPassword) return @event.Values.Value<string>("forgottenById");
        else if (@event.Type == LoginEventType.resetPassword) return @event.Values.Value<string>("resetById");
        else if (@event.Type == LoginEventType.sendCode) return @event.Values.Value<string>("sentById");
        else if (@event.Type == LoginEventType.verifyCode) return @event.Values.Value<string>("verifiedById");
        else if (@event.Type == LoginEventType.verifyResetPasswordCode) return @event.Values.Value<string>("verifiedById");
        else if (@event.Type == LoginEventType.deleteLogin) return @event.Values.Value<string>("deletedById");
        else if (@event.Type == LoginEventType.deleteLogin) return @event.Values.Value<string>("requestedById");
        else if (@event.Type == LoginEventType.reactivate) return @event.Values.Value<string>("reactivatedById");
        else if (@event.Type == LoginEventType.deactivate) return @event.Values.Value<string>("deactivatedById");
        else return @event.Values.Value<string>("modifiedById");
    }

    [HttpGet("logins/fromUsername/{username}")]
    public async Task<ActionResult<Login>> GetLoginIdFromUserId(string username, string appId, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);

        bool enableFindLoginByNameOrEmail = await _multiTenantFeatureManager.IsEnabled("EnableFindLoginByNameOrEmail", tenantId);
        MongoLoginDao user = enableFindLoginByNameOrEmail && MailAddress.TryCreate(username, out _)
            ? await _mongoUserManager.FindByEmailAsync(username).ToCancellable(cancellationToken)
            : await _mongoUserManager.FindByNameAsync(username).ToCancellable(cancellationToken);

        if (user == null)
            return null;

        PermissionGroup defaultPermissionGroup = await _authService.GetDefaultPermissionGroupAsync(tenantId, cancellationToken);
        ApiResource apiResource = await _resourceStore.FindApiResourceAsync("all_user_claims").ToCancellable(cancellationToken);

        bool enforceMfa = false;
        if (appId != null)
        {
            App app = await _authService.GetAppAsync(tenantId, appId, cancellationToken);
            if (app.Requires2FA)
                enforceMfa = true;
        }

        Permissions permissions = await _permissionService.GetPermissionsAsync(tenantId, user, apiResource, defaultPermissionGroup, enforceMfa, cancellationToken);

        return LoginExtensions.ToLogin(user, permissions);
    }

    [HttpPost("logins")]
    public async Task<ActionResult<Result<CreatedStatus>>> CreateLogin([FromBody] CreateLoginCommand command,
        CancellationToken cancellationToken)
    {
        var user = new MongoLoginDao
        {
            UserName = command.Username,
            Email = command.Email,
            PhoneNumber = command.TelephoneNumber,
            LastModifiedAt = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            CreatedById = command.CreatedById,
            EntityId = command.EntityId,
            EntityType = command.EntityType,
            IgnorePasswordLifespan = command.IgnorePasswordLifespan,
            IgnoreAccountLockout = command.IgnoreAccountLockout,
            EmailConfirmed = command.IsEmailConfirmed,
            PasswordLastUpdated = DateTime.UtcNow
        };

        string tenantId = Request.PathBase.Value.Remove(0, 1);
        IdentityResult userCreatedResult;

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);
        try
        {
            var passwordValidators = new List<IPasswordValidator<MongoLoginDao>>(_mongoUserManager.PasswordValidators);
            if (command.IgnorePasswordValidation)
                _mongoUserManager.PasswordValidators.Clear();

            if (await EmailAlreadyExists(_mongoUserManager, command.Email))
            {
                _logger.LogError("{tenantId}: USER WITH SAME EMAIL ADDRESS ALREADY EXISTS: {command}", tenantId, JsonConvert.SerializeObject(command));
                return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { "User with same email address already exists." } };
            }

            if (await UsernameAlreadyExists(_mongoUserManager, command.Username))
            {
                _logger.LogError("{tenantId}: USER WITH SAME USERNAME ALREADY EXISTS: {command}", tenantId, JsonConvert.SerializeObject(command));
                return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { "User with same username already exists." } };
            }

            if (!string.IsNullOrEmpty(command.Password))
            {
                if (command.Password.Length > 128)
                {
                    _logger.LogError("{tenantId}: PASSWORD TOO LONG: {command}", tenantId, JsonConvert.SerializeObject(command));

                    return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { "Password too long." } };
                }

                if (!command.IgnorePasswordValidation)
                {
                    Result passwordValidationResult = await ValidatePassword(_mongoUserManager, tenantId, user, command.Password);
                    if (!passwordValidationResult.IsSuccess) return new Result<CreatedStatus> { Status = "failure", Errors = passwordValidationResult.Errors, Errors_2 = passwordValidationResult.Errors_2 };
                }

                userCreatedResult = await _mongoUserManager.CreateAsync(user, command.Password);
            }
            else
            {
                userCreatedResult = await _mongoUserManager.CreateAsync(user);
            }

            if (command.IgnorePasswordValidation)
                foreach (IPasswordValidator<MongoLoginDao> passwordValidator in passwordValidators)
                    _mongoUserManager.PasswordValidators.Add(passwordValidator);

            if (!userCreatedResult.Succeeded)
            {
                _logger.LogError($"{tenantId}:{string.Join(',', userCreatedResult.Errors.Select(e => $"{e.Code}|{e.Description}"))}");
                return new Result<CreatedStatus> { Status = "failure", Errors = userCreatedResult.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() };
            }

            user = await _mongoUserManager.FindByNameAsync(command.Username);
            await _eventStore.AddEventAsync(tenantId, new LoginEvent(user.Id, LoginEventType.createLogin, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            if (command.UseDefaultPermissions ?? true)
            {
                await AddClientIdClaimAsync(user, command.ClientId);

                // Grant access to defined apps
                foreach (string appId in command.AppIdsToBeGrantedAccessTo?.Except(new List<string> { command.ClientId }) ?? Enumerable.Empty<string>())
                    await AddClientIdClaimAsync(user, appId);
            }

            if (command.ActiveFrom != null && command.ActiveFrom > DateTime.UtcNow)
            {
                // Deactivate and reactivate user in a future time (ActiveFrom)
                ClaimsIdentity claimsIdentity = Request.HttpContext?.User?.Identity as ClaimsIdentity;
                if (claimsIdentity != null)
                {
                    await _loginMutation?.Deactivate(tenantId, claimsIdentity, user.Id, null, cancellationToken);
                    await _loginMutation?.Reactivate(tenantId, claimsIdentity, user.Id, command.ActiveFrom, null, cancellationToken);
                }
                else
                {
                    throw new UnauthorizedAccessException("Unauthorized");
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogError($"{tenantId}:{e.Message}");

            return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { e.Message } };
        }

        if (user.EmailConfirmed == true && command.SendNotificationCommand == null)
        {
            return new Result<CreatedStatus> { Status = "success", Value = new CreatedStatus { Id = user.Id } };
        }

        App app = (await _authService.GetAppsAsync(tenantId, new AppWhere { AppId = command.ClientId }, null, null, null, cancellationToken))?.FirstOrDefault();

        if (app == null)
        {
            _logger.LogError($"App '{command.ClientId}' doesn't exist for tenant '{tenantId}'.");
            return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The app '{command.ClientId}' doesn't exist for tenant '{tenantId}'." } };
        }

        if (user.EmailConfirmed == true && command.SendNotificationCommand != null)
        {
            
            command.SendNotificationCommand.LoginId = user.Id;
            command.SendNotificationCommand.EmailMessage.To = command.SendNotificationCommand.EmailMessage.To ?? command.Email;
            command.SendNotificationCommand.SentById = command.CreatedById;
            var ret = await SendPasswordSetEmail(tenantId, app, "email_welcome", command.SendNotificationCommand, string.Empty, cancellationToken);

            if (!ret.IsSuccess)
                return Result<CreatedStatus>.Failure(ret.Errors);
        }

        if (!app.RedirectUris.Any())
        {
            _logger.LogError($"App '{command.ClientId}' for tenant '{tenantId}' doesn't have any redirectUris.");
            return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"App '{command.ClientId}' for tenant '{tenantId}' doesn't have any redirectUris." } };
        }

        if (!string.IsNullOrEmpty(command.Password))
        {
            if (!command.IsEmailConfirmed)
            {
                string code = await _mongoUserManager.GenerateEmailConfirmationTokenAsync(user);
                string encodedCode = UrlEncoder.Default.Encode(code);

                if (command.SendNotificationCommand == null)
                    command.SendNotificationCommand = new SendNotificationCommand
                    {
                        EmailMessage = new EmailMessage()
                    };

                command.SendNotificationCommand.LoginId = user.Id;
                command.SendNotificationCommand.RedirectQueryString = command.RedirectQueryString;
                command.SendNotificationCommand.EmailMessage.To = command.SendNotificationCommand.EmailMessage.To ?? command.Email;
                command.SendNotificationCommand.SentById = command.CreatedById;

                var ret = await SendPasswordSetEmail(tenantId, app, "email_confirmation", command.SendNotificationCommand, encodedCode, cancellationToken);

                if (!ret.IsSuccess)
                    return Result<CreatedStatus>.Failure(ret.Errors);
            }
        }
        else
        {
            (string passwordResetToken, Result updateResult) = await _mongoUserManager.GenerateSingleUsePasswordResetTokenAsync(user);
            if (updateResult != null)
            {
                return Result<CreatedStatus>.Failure(updateResult.Errors);
            }
            string encodedCode = UrlEncoder.Default.Encode(passwordResetToken);

            if (command.SendNotificationCommand == null)
                command.SendNotificationCommand = new SendNotificationCommand
                {
                    EmailMessage = new EmailMessage()
                };

            command.SendNotificationCommand.LoginId = user.Id;
            command.SendNotificationCommand.RedirectQueryString = command.RedirectQueryString;
            command.SendNotificationCommand.EmailMessage.To = command.SendNotificationCommand.EmailMessage.To ?? command.Email;
            command.SendNotificationCommand.SentById = command.CreatedById;

            var ret = await SendPasswordSetEmail(tenantId, app, "password_set", command.SendNotificationCommand, encodedCode, cancellationToken);

            if (!ret.IsSuccess)
                return Result<CreatedStatus>.Failure(ret.Errors);
        }

        PasswordValidators passwordValidatorsAgain = await _authService.GetPasswordValidatorsAsync(tenantId, "default", cancellationToken);
        int? savePasswordsCount = passwordValidatorsAgain?.SavePasswordHistoryCount;
        if (savePasswordsCount is > 0 && command.Password != null)
        {
            string hashedPassword = _mongoUserManager.PasswordHasher.HashPassword(user, command.Password);
            user.PreviouslyUsedPasswords = LoginExtensions.AddToListandRemoveExtra(hashedPassword, user.PreviouslyUsedPasswords, (int) savePasswordsCount);
        }
        await _mongoUserManager.UpdateAsync(user);

        return new Result<CreatedStatus> { Status = "success", Value = new CreatedStatus { Id = user.Id } };

        async Task AddClientIdClaimAsync(MongoLoginDao createdUser, string appId)
        {
            var addTargettedPermissionCommand = new AddTargettedPermissionCommand { AddedById = command.CreatedById, Type = "clientId", Value = appId };
            IdentityResult addAppIdGrantResult = await _mongoUserManager.AddClaimAsync(createdUser, new Claim(addTargettedPermissionCommand.Type, addTargettedPermissionCommand.Value));
            if (addAppIdGrantResult.Succeeded)
                await _eventStore.AddEventAsync(tenantId, new LoginEvent(createdUser.Id, LoginEventType.addPermission, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(addTargettedPermissionCommand, _jsonSerializer)
                }, cancellationToken);
        }
    }

    [HttpPost("logins/{loginId}/sendCode")]
    public async Task<Result> SendCode(string loginId, [FromBody] SendCodeCommand command, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
        if (user == null)
            return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };
        if (command.Purpose == null && user.PhoneNumberConfirmed)
            return new Result { Status = "failure", Errors = new List<string> { $"The phone of '{loginId}' is already confirmed." } };
        if (!user.IsActive)
            return new Result { Status = "failure", Errors = new List<string> { $"The user is not active" } };


        App app = (await _authService.GetAppsAsync(tenantId, new AppWhere { AppId = command.ClientId }, null, null, null, cancellationToken))?.FirstOrDefault();
        if (app == null)
            return new Result { Status = "failure", Errors = new List<string> { $"App '{command.ClientId}' doesn't exist for tenant '{tenantId}'." } };

        string code;

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);

        if (command.Purpose == null)
            code = await _mongoUserManager.GenerateTwoFactorTokenAsync(user, "Phone");
        else
            code = await _mongoUserManager.GenerateUserTokenAsync(user, "Phone", command.Purpose);

        string phoneNumber = await _mongoUserManager.GetPhoneNumberAsync(user);

        var sendNotificationCommand = new SendNotificationCommand
        {
            SmsMessage = new SmsMessage
            {
                To = phoneNumber,
                From = app.AppName
            },
            SentById = command.SentById
        };

        if (command.TemplateId != null)
            sendNotificationCommand.SmsMessage.TemplateRendering = new TemplateRendering
            {
                TemplateId = command.TemplateId,
                Input = new RenderParameters
                {
                    Name = "data",
                    Content = JToken.FromObject(new { otp = code })
                }
            };
        else
        {
            string message = "Your security code is: " + code;
            sendNotificationCommand.SmsMessage.Body = message;
        }
        sendNotificationCommand.SentById = tenantId;

        Result notificationResult = await SendNotification(tenantId, sendNotificationCommand, cancellationToken);

        if (!notificationResult.IsSuccess)
        {
            return notificationResult;
        }

        command.SentById = loginId;
        await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.sendCode, DateTime.UtcNow)
        {
            Values = JObject.FromObject(command, _jsonSerializer)
        }, cancellationToken);

        return new Result { Status = "success" };
    }

    [HttpPost("logins/{loginId}/verifycode")]
    public async Task<Result> VerifyCode(string loginId, [FromBody] VerifyCodeCommand command,
        CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
        if (user == null)
            return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };
        if (!user.IsActive)
            return new Result { Status = "failure", Errors = new List<string> { $"The user is not active" } };

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);


        bool success = false;
        if ((tenantId == "tahoe_uat" || tenantId == "tahoe") && command.Code == "000000")
            success = true;
        else
        {
            success = command.Purpose == null
                ? await _mongoUserManager.VerifyTwoFactorTokenAsync(user, "Phone", command.Code)
                : await _mongoUserManager.VerifyUserTokenAsync(user, "Phone", command.Purpose, command.Code);

            if (!success)
                return new Result { Status = "failure", Errors = new List<string> { $"The code is invalid" } };
        }

        if (command.Purpose == null)
        {
            user.PhoneNumberConfirmed = true;

            if (user.EmailConfirmed)
                user.TwoFactorEnabled = true;
        }

        IdentityResult result = await _mongoUserManager.UpdateAsync(user);

        command.VerifiedById = loginId;
        if (result.Succeeded)
        {
            await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.verifyCode, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);
        }

        return !result.Succeeded
            ? new Result { Status = "failure", Errors = result.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() }
            : new Result { Status = "success" };
    }

    [HttpPost("logins/{loginId}/verifyResetPasswordCode")]
    public async Task<ActionResult<Result>> VerifyResetPasswordCode(string loginId, [FromBody] VerifyCodeCommand command,
        CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
        if (user == null)
            return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };
        if (!user.IsActive)
            return new Result { Status = "failure", Errors = new List<string> { $"The user is not active" } };

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);

        ClaimsIdentity identity = (ClaimsIdentity) Request.HttpContext.User.Identity;
        string clientId = GetClaim(identity, "appId") ?? GetClaim(identity, "client_id");
        _customPasswordResetTokenProviderContext.ClientId = clientId;
        bool success = await _mongoUserManager.VerifyUserTokenAsync(user, "CustomPasswordResetTokenProvider", "ResetPassword", command.Code);
        if (!success)
            return new Result { Status = "failure", Errors = new List<string> { "The reset password code is invalid or has expired" } };

        command.VerifiedById = loginId;
        await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.verifyResetPasswordCode, DateTime.UtcNow)
        {
            Values = JObject.FromObject(command, _jsonSerializer)
        }, cancellationToken);

        return new Result { Status = "success" };
    }

    [HttpPost("logins/{loginId}/resendConfirmationEmail")]
    public async Task<ActionResult<Result>> ResendEmail(string loginId, [FromBody] ResendEmailCommand command, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
        if (user == null)
            return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };
        if (user.EmailConfirmed)
            return new Result { Status = "failure", Errors = new List<string> { $"The email of '{loginId}' is already confirmed." } };
        if (!user.IsActive)
            return new Result { Status = "failure", Errors = new List<string> { $"The user is not active" } };

        App app = (await _authService.GetAppsAsync(tenantId, new AppWhere { AppId = command.ClientId }, null, null, null, cancellationToken))?.FirstOrDefault();

        string clientEmail = app?.Email;
        string senderName = app?.EmailSenderName;

        if (app == null)
        {
            _logger.LogError($"App '{command.ClientId}' doesn't exist for tenant '{tenantId}'.");
            return new Result { Status = "failure", Errors = new List<string> { $"App '{command.ClientId}' doesn't exist for tenant '{tenantId}'." } };
        }
        if (!app.RedirectUris.Any())
        {
            _logger.LogError($"App '{command.ClientId}' for tenant '{tenantId}' doesn't have any redirectUris.");
            return new Result { Status = "failure", Errors = new List<string> { $"App '{command.ClientId}' for tenant '{tenantId}' doesn't have any redirectUris." } };
        }

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);

        string code = await _mongoUserManager.GenerateEmailConfirmationTokenAsync(user);
        string encodedCode = UrlEncoder.Default.Encode(code);

        if (command.SendNotificationCommand == null)
            command.SendNotificationCommand = new SendNotificationCommand
            {
                EmailMessage = new EmailMessage()
            };

        command.SendNotificationCommand.LoginId = user.Id;
        command.SendNotificationCommand.SentById = command.ResentById;
        command.SendNotificationCommand.CallbackUrl = command.CallbackUrl;
        command.SendNotificationCommand.EmailMessage.To = user.Email;

        var ret = await SendPasswordSetEmail(tenantId, app, "password_reset", command.SendNotificationCommand, encodedCode, cancellationToken);

        if (!ret.IsSuccess)
            return Result.Failure(ret.Errors);

        await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.resendEmail, DateTime.UtcNow)
        {
            Values = JObject.FromObject(command, _jsonSerializer)
        }, cancellationToken);

        return new Result { Status = "success" };
    }

    [HttpPost("logins/{loginId}/update")]
    public async Task<ActionResult<Result>> UpdateLogin(string loginId, [FromBody] UpdateLoginCommand command, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
        if (user == null)
            return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };

        var defClaims = new List<Claim>(user.Claims.Select(c => c.ToSecurityClaim()));
        if (IsGuestUser(defClaims))
            return Result.Failure("Guest user is not allowed to have its login info changed");

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);

        if (command.IsEmailChanged)
        {
            MongoLoginDao checkEmailExistDao = await _mongoUserManager.FindByEmailAsync(command.Email);

            if (checkEmailExistDao is not null)
                return new Result { Status = "failure", Errors = new List<string> { "failed to change the email, email has been occupied" } };

            IdentityResult emailChangeResult = await _mongoUserManager.SetEmailAsync(user, command.Email);

            if (emailChangeResult.Succeeded is false)
                return new Result { Status = "failure", Errors = new List<string> { "failed to change the email" } };

            user.TwoFactorEnabled = false;
        }

        if (command.IsUserNameChanged)
        {
            IdentityResult usernameChangeResult = await _mongoUserManager.SetUserNameAsync(user, command.UserName);
            if (!usernameChangeResult.Succeeded)
            {
                return new Result { Status = "failure", Errors = new List<string> { "failed to change the user name" } };
            }
        }

        if (command.IsTelephoneNumberChanged)
        {
            user.PhoneNumber = command.TelephoneNumber;
            user.PhoneNumberConfirmed = false;
            user.PhoneNumberConfirmed = false;
        }

        IdentityResult result = await _mongoUserManager.UpdateAsync(user);
        if (result.Succeeded)
        {
            await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.updateLogin, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);
        }

        if (command.IsUserNameChanged && tenantId.ToLower().Contains("dlvn"))
        {
            _logger.LogInformation("[DLVN UpdateLogin] Reset password and confirm email automatically due to username changed");
            (string passwordResetToken, Result updateResult) = await _mongoUserManager.GenerateSingleUsePasswordResetTokenAsync(user);
            if (updateResult != null)
            {
                return updateResult;
            }

            var resetResult = await _mongoUserManager.ResetPasswordAsync(user, passwordResetToken, user.UserName);

            if (!resetResult.Succeeded)
            {
                _logger.LogError($"[DLVN UpdateLogin] Reset password failed {string.Join(", ", resetResult.Errors)}");
            }

            var confirmEmailToken = await _mongoUserManager.GenerateEmailConfirmationTokenAsync(user);
            var confirmEmailResult = await _mongoUserManager.ConfirmEmailAsync(user, confirmEmailToken);

            if (!confirmEmailResult.Succeeded)
            {
                _logger.LogError($"[DLVN UpdateLogin] Confirm email failed {string.Join(", ", confirmEmailResult.Errors)}");
            }
        }

        return result.Succeeded
            ? new Result { Status = "success" }
            : new Result { Status = "failure", Errors = result.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() };
    }

    [HttpPost("logins/{loginId}/delete")]
    public async Task<ActionResult<Result>> DeleteLogin(string loginId, [FromBody] DeleteCommand command,
        CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
        if (user == null)
            return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);

        IdentityResult result = await _mongoUserManager.DeleteAsync(user);

        if (result.Succeeded)
        {
            await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.deleteLogin, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);
        }

        return result.Succeeded
            ? new Result { Status = "success" }
            : new Result { Status = "failure", Errors = result.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() };
    }

    private async Task<Result<string>> ValidateDateOfBirth(string tenantId, string loginId, DateTime? dateOfBirth, UserManager<MongoLoginDao> mongoUserManager, MongoLoginDao user, CancellationToken cancellationToken)
    {
        if (dateOfBirth == null)
            return Result<string>.Success("success");

        PasswordValidators passwordValidators = await _authService.GetPasswordValidatorsAsync(tenantId, "default", cancellationToken);

        if (passwordValidators is { PasswordResetRequireDobVerification: true })
        {
            IndividualWhere where = new IndividualWhere { Id = user.EntityId };
            List<Individual> individuals = (await _entityService.GetIndividualsAsync(tenantId, new QueryArguments { Where = @where }, cancellationToken)).ToList();

            if (individuals.FirstOrDefault()?.DateOfBirth?.Date != dateOfBirth.Value.Date)
            {
                await mongoUserManager.AccessFailedAsync(user);
                if (passwordValidators.MaxDobVerificationAttempts.GetValueOrDefault() > 0)
                {
                    user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);

                    if (user == null)
                        return Result<string>.Failure("Login not found");

                    if (user.AccessFailedCount >= passwordValidators.MaxDobVerificationAttempts)
                    {
                        int lockOutTimeInSeconds = passwordValidators.LockoutTimespanInSeconds > 0 ? passwordValidators.LockoutTimespanInSeconds : **********; // 65 years fall back
                        await mongoUserManager.SetLockoutEnabledAsync(user, true);
                        await mongoUserManager.SetLockoutEndDateAsync(user, DateTimeOffset.Now.AddSeconds(lockOutTimeInSeconds));
                        return Result<string>.Failure("Too many unsuccessful reset password attempts with incorrect date of birth, the account is suspended.");
                    }
                }
                return Result<string>.Failure("Incorrect/Invalid date of birth provided. Password reset requires correct date of birth.");
            }
        }

        return Result<string>.Success("success");
    }

    [HttpPost("logins/{loginId}/confirmEmail")]
    public async Task<ActionResult<Result<Token>>> ConfirmEmailAsync2(string loginId,
        [FromBody] ConfirmEmailCommand command, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
        if (user == null)
            return new Result<Token> { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };

        if (!user.IsActive)
        {
            _logger.LogWarning($"The user with loginId '{loginId}' is not active.");
            return Result<Token>.Failure("The user is not active.");
        }

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);
        PasswordValidators passwordValidators = await _authService.GetPasswordValidatorsAsync(tenantId, "default", cancellationToken);

        Result<string> dateOfBirthValidationResult = await ValidateDateOfBirth(tenantId, loginId, command.DateOfBirth, _mongoUserManager, user, cancellationToken);
        if (!dateOfBirthValidationResult.IsSuccess)
            return Result<Token>.Failure(dateOfBirthValidationResult.Errors?.First());

        IdentityResult result = await _mongoUserManager.ConfirmEmailAsync(user, command.Code);
        if (!result.Succeeded)
        {
            _logger.LogError($"{result.Errors.FirstOrDefault()?.Code}|{result.Errors.FirstOrDefault()?.Description}|{command.Code}");
            return new Result<Token> { Status = "failure", Errors = result.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() };
        }

        if (command.SendNotificationCommand != null)
        {
            EmailMessage emailMessage = command.SendNotificationCommand.EmailMessage;
            emailMessage.To = user.Email; // For security, should not be able to send confirmEmail email to a different account
            command.SendNotificationCommand.SmsMessage = null; // For security, should not be able to send sms to whatever phoneNumber defined by the FE

            if (emailMessage.HtmlContent != null)
            {
                emailMessage.HtmlContent = emailMessage.HtmlContent.Replace("{{email}}", user.Email);
            }

            Result notificationResult = await SendNotification(tenantId, command.SendNotificationCommand, cancellationToken);

            if (!notificationResult.IsSuccess)
            {
                return Result<Token>.Failure(notificationResult.Errors);
            }
        }

        if (user.PhoneNumberConfirmed)
        {
            user.TwoFactorEnabled = true;
            IdentityResult identityResult = await _mongoUserManager.SetTwoFactorEnabledAsync(user, true);
        }

        await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.confirmEmail, DateTime.UtcNow)
        {
            Values = JObject.FromObject(command, _jsonSerializer)
        }, cancellationToken);

        if (command.AppId != null)
        {
            Token token = await GetTokenAsLoginId(user.Id, command.AppId, cancellationToken);
            return new Result<Token> { Status = "success", Value = token };
        }

        return new Result<Token> { Status = "success" };
    }

    [HttpPost("logins/{loginId}/resetPassword")]
    public async Task<ActionResult<Result>> ResetPasswordAsync(string loginId,
        [FromBody] ResetPasswordCommand command, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);
        if (user == null)
            return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };

        if (!user.IsActive)
        {
            _logger.LogWarning($"The user with loginId '{loginId}' is not active.");
            return Result.Success();
        }

        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);
        PasswordValidators passwordValidators = await _authService.GetPasswordValidatorsAsync(tenantId, "default", cancellationToken);
        Result passwordValidationResult = await ValidatePassword(_mongoUserManager, tenantId, user, command.Password);
        if (!passwordValidationResult.IsSuccess) return passwordValidationResult;

        Result<string> dateOfBirthValidationResult = await ValidateDateOfBirth(tenantId, loginId, command.DateOfBirth, _mongoUserManager, user, cancellationToken);
        if (!dateOfBirthValidationResult.IsSuccess)
            return Result.Failure(dateOfBirthValidationResult.Errors?.First());

        int? savePasswordsCount = passwordValidators?.SavePasswordHistoryCount;

        if (savePasswordsCount > 0)
        {
            string hashedPassword = _mongoUserManager.PasswordHasher.HashPassword(user, command.Password);
            bool? previousContainsHashedPassword = user.PreviouslyUsedPasswords?.Select(p =>
                    _mongoUserManager.PasswordHasher.VerifyHashedPassword(user, p, command.Password))
                .Any(a => a == PasswordVerificationResult.Success);

            if (previousContainsHashedPassword == true)
                return !passwordValidators.ExposeErrorMessage ? Result.Success()
                    : new Result { Status = "failure", Errors = new List<string> { $"Please choose a new password, You cannot use a previous password." } };


            user.PreviouslyUsedPasswords = LoginExtensions.AddToListandRemoveExtra(hashedPassword, user.PreviouslyUsedPasswords, (int) savePasswordsCount);
        }


        IdentityResult result = await _mongoUserManager.ResetPasswordAsync(user, command.Code, command.Password);
        if (!result.Succeeded)
        {
            if (string.IsNullOrEmpty(user.PasswordHash))
            {
                _logger.LogError($"SetNewPassword:{user.UserName}|{result.Errors.First().Code}|{result.Errors.First().Description}|{command.Code}");
            }
            else
            {
                _logger.LogError($"ResetPassword:{user.UserName}|{result.Errors.FirstOrDefault()?.Code}|{result.Errors.FirstOrDefault()?.Description}|{command.Code}");
            }

            return new Result { Status = "failure", Errors = result.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() };
        }

        user.EmailConfirmed = true;
        user.PasswordLastUpdated = DateTime.UtcNow;
        user.PasswordResetToken = null;

        IdentityResult updateResult = await _mongoUserManager.UpdateAsync(user);
        if (!updateResult.Succeeded)
        {
            _logger.LogError($"{updateResult.Errors.First().Code}|{updateResult.Errors.First().Description}");
            return new Result { Status = "failure", Errors = updateResult.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() };
        }

        await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.resetPassword, DateTime.UtcNow)
        {
            Values = JObject.FromObject(command, _jsonSerializer)
        }, cancellationToken);

        if (command.SendNotificationCommand != null)
        {
            EmailMessage emailMessage = command.SendNotificationCommand.EmailMessage;
            emailMessage.To = emailMessage?.To ?? user.Email;

            if (emailMessage.HtmlContent != null)
            {
                emailMessage.HtmlContent = emailMessage.HtmlContent.Replace("{{email}}", emailMessage.To);
            }
            if (emailMessage.TemplateRendering != null)
                emailMessage.TemplateRendering.Input = new RenderParameters { Name = "data", Content = JToken.FromObject(new { changedAt = DateTime.UtcNow.ToString("o") }) };

            Result notificationResult = await SendNotification(tenantId, command.SendNotificationCommand, cancellationToken);

            if (!notificationResult.IsSuccess)
            {
                return notificationResult;
            }
        }

        await UnlockUser(_mongoUserManager, user);
        return new Result { Status = "success" };
    }

    [HttpPost("logins/forgotPassword")]
    public async Task<ActionResult<Result>> ForgotPasswordAsync([FromBody] ForgotPasswordCommand command,
        CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);
        MongoLoginDao user = command.Username != null ? await _mongoUserManager.FindByNameAsync(command.Username) : await _mongoUserManager.FindByEmailAsync(command.Email);

        PasswordValidators passwordValidators = await _authService.GetPasswordValidatorsAsync(tenantId, command.ClientId, cancellationToken)
                                                ?? await _authService.GetPasswordValidatorsAsync(tenantId, "default", cancellationToken)
                                                ?? new PasswordValidators();

        if (user == null)
        {
            string errorMessage = command.Username != null ? $"The user with username '{command.Username}' doesn't exist." : $"The user with email '{command.Email}' doesn't exist.";
            _logger.LogWarning(errorMessage);
            return Result.Failure("Invalid email address");
        }

        if (!user.IsActive)
        {
            _logger.LogWarning($"The user with username '{command.Username ?? command.Email}' is not active.");
            return Result.Failure("The user is not active.");
        }

        if (!string.IsNullOrEmpty(user.EntityId) && user.EntityType == "Internal")
        {
            Token accessToken = await GetTokenAsLoginId(user.Id, command.ClientId, cancellationToken);
            JObject queryResult = await _gatewayService.QueryAsync(accessToken.AccessToken, "query agentActiveAndTerminationDateTime($email: String!) {\n  agentActiveAndTerminationDateTime(email: $email) {\n    activeOnDateTime\n    terminationOnDateTime\n  }\n}", new JObject
            {
                ["email"] = user.Email
            }, cancellationToken);
            _logger.LogInformation("Query result: {queryResult}", queryResult);

            var activeFromDateTime = queryResult.SelectToken("agentActiveAndTerminationDateTime.activeOnDateTime")?.ToObject<DateTimeOffset?>();
            var terminationDateTime = queryResult.SelectToken("agentActiveAndTerminationDateTime.terminationOnDateTime")?.ToObject<DateTimeOffset?>();

            if (activeFromDateTime.HasValue && activeFromDateTime.Value > DateTimeOffset.UtcNow)
            {
                _logger.LogWarning($"The user with username '{command.Username ?? command.Email}' is not activated.");
                return Result.Failure("Account is not activated.");
            }
            else if (terminationDateTime.HasValue && terminationDateTime.Value < DateTimeOffset.UtcNow)
            {
                _logger.LogWarning($"The user with username '{command.Username ?? command.Email}' has been deactivated.");
                return Result.Failure("Account has been deactivated");
            }
        }

        IEnumerable<PermissionGroup> userPermissionGroups = await _permissionService.GetPermissionGroupsFromClaims(tenantId, user, cancellationToken);
        var targetedClientIdPermissions = (await _permissionService.GetTargettedPermissionsFromGroupsAsync(tenantId, userPermissionGroups, cancellationToken))
            .SelectMany(p => p.Value.Distinct().Select(v => new Claim(p.Key, v)))
            .Where(x => x.Type == "clientId")
            .ToList();

        if (!user.Claims.Any(x => x.Type == "clientId" && (x.Value == command.ClientId || x.Value == "all")) &&
            !targetedClientIdPermissions.Any(x => x.Value == command.ClientId || x.Value == "all"))
        {
            string errorMessage =
                $"The user with username '{command.Username ?? command.Email}' doesn't have access on clientId {command.ClientId}";
            _logger.LogWarning(errorMessage);
            return (passwordValidators?.ExposeErrorMessage) == true ? Result.Failure(errorMessage) : Result.Success();
        }

        if (passwordValidators.PasswordResetRequireDobVerification)
        {
            IndividualWhere where = new IndividualWhere { Id = user.EntityId };
            List<Individual> individuals = (await _entityService.GetIndividualsAsync(tenantId, new QueryArguments { Where = @where }, cancellationToken)).ToList();
            if (individuals.FirstOrDefault()?.DateOfBirth != command.DateOfBirth)
                return Result.Success();
        }

        (string passwordResetToken, Result updateResult) = await _mongoUserManager.GenerateSingleUsePasswordResetTokenAsync(user);
        if (updateResult != null)
        {
            return updateResult;
        }
        string encodedCode = UrlEncoder.Default.Encode(passwordResetToken);

        App app = (await _authService.GetAppsAsync(tenantId, new AppWhere { AppId = command.ClientId }, null, null, null, cancellationToken))?.FirstOrDefault();

        string clientEmail = app?.Email;
        string senderName = app?.EmailSenderName;
        if (app == null)
        {
            _logger.LogError($"App '{command.ClientId}' doesn't exist for tenant '{tenantId}'.");
            return new Result { Status = "failure", Errors = new List<string> { $"App '{command.ClientId}' doesn't exist for tenant '{tenantId}'." } };
        }

        if (!app.RedirectUris.Any())
        {
            _logger.LogError($"App '{command.ClientId}' for tenant '{tenantId}' doesn't have any redirectUris.");
            return new Result { Status = "failure", Errors = new List<string> { $"App '{command.ClientId}' for tenant '{tenantId}' doesn't have any redirectUris." } };
        }

        if (command.SendNotificationCommand == null)
            command.SendNotificationCommand = new SendNotificationCommand
            {
                EmailMessage = new EmailMessage()
            };

        if (await _multiTenantFeatureManager.IsDisableGenericForgotPasswordEmailEnabledAsync(tenantId))
        {
            OverrideSendNotificationCommandBySettings(command, app);
        }

        command.SendNotificationCommand.LoginId = user.Id;
        command.SendNotificationCommand.EmailMessage.To = user.Email;
        command.SendNotificationCommand.SentById = command.ForgottenById;
        command.SendNotificationCommand.Language = command.Language;

        var ret = await SendPasswordSetEmail(tenantId, app, "password_forgot", command.SendNotificationCommand, encodedCode, cancellationToken);

        if (!ret.IsSuccess)
            return Result.Failure(ret.Errors);

        command.ForgottenById = user.Id;
        await _eventStore.AddEventAsync(tenantId, new LoginEvent(user.Id, LoginEventType.forgotPassword, DateTime.UtcNow)
        {
            Values = JObject.FromObject(command, _jsonSerializer)
        }, cancellationToken);

        return new Result { Status = "success" };
    }

    private void OverrideSendNotificationCommandBySettings(ForgotPasswordCommand command, App? app)
    {
        if (app?.ForgotPasswordEmailSettings == null)
        {
            _logger.LogDebug("ForgotPasswordEmailSettings is null. Using default values");
            return;
        }

        string link = string.IsNullOrEmpty(app.UrlRouting?.Url)
            ? app.ForgotPasswordEmailSettings.Link
            : app.UrlRouting.Url;

        string emailFrom = app.Email ?? app.ForgotPasswordEmailSettings.From;
        string emailFromName = app.EmailSenderName ?? app.ForgotPasswordEmailSettings.FromName;
        string subject = app.ForgotPasswordEmailSettings.Subject ?? "Forgot Password";

        RenderParameters templateInput = CreateRenderParameters(link, command.Email);

        command.SendNotificationCommand = new SendNotificationCommand
        {
            EmailMessage = new EmailMessage
            {
                From = emailFrom,
                FromName = emailFromName,
                Subject = subject,
                TemplateRendering = new TemplateRendering
                {
                    TemplateId = app.ForgotPasswordEmailSettings.TemplateId,
                    Input = templateInput
                }
            }
        };

        _logger.LogDebug("Successfully applied ForgotPasswordEmailSettings");
    }

    private static RenderParameters CreateRenderParameters(string link, string memberName)
    {
        string json = $@"
        {{
            ""link"": ""{link}"",
            ""memberName"": ""{memberName}""
        }}";

        return new RenderParameters
        {
            Name = "data",
            Content = JObject.Parse(json)
        };
    }

    [HttpPost("logins/{loginId}/changepassword")]
    public async Task<ActionResult<Result>> ChangePassword(string loginId, [FromBody] ChangePasswordCommand command,
        CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, loginId, cancellationToken);

        if (user == null)
            return Result.Failure("Login not found");

        var defClaims = new List<Claim>(user.Claims.Select(c => c.ToSecurityClaim()));
        if (IsGuestUser(defClaims))
            return Result.Failure("Guest user is not allowed to have its password changed");

        UserManager<MongoLoginDao> mongoUserManager = _authService.GetMongoUserManager(tenantId);

        PasswordValidators passwordValidators = await _authService.GetPasswordValidatorsAsync(tenantId, "default", cancellationToken);

        // Password compliance checks when PasswordComplianceCheck feature flag is on
        Result passwordValidationResult = await ValidatePassword(mongoUserManager, tenantId, user, command.NewPassword);
        if (!passwordValidationResult.IsSuccess) return passwordValidationResult;

        int? savePasswordsCount = passwordValidators?.SavePasswordHistoryCount;
        if (savePasswordsCount > 0)
        {
            string hashedPassword = mongoUserManager.PasswordHasher.HashPassword(user, command.NewPassword);
            bool? previousContainsHashedPassword = user.PreviouslyUsedPasswords?.Select(p =>
                    mongoUserManager.PasswordHasher.VerifyHashedPassword(user, p, command.NewPassword))
                .Any(a => a == PasswordVerificationResult.Success);

            if (previousContainsHashedPassword == true)
                return !passwordValidators.ExposeErrorMessage ? Result.Success()
                    : new Result { Status = "failure", Errors = new List<string> { $"Please choose a new password, You cannot use a previous password." } };

            user.PreviouslyUsedPasswords = LoginExtensions.AddToListandRemoveExtra(hashedPassword, user.PreviouslyUsedPasswords, (int) savePasswordsCount);
        }

        var identityPasswordValidators = new List<IPasswordValidator<MongoLoginDao>>(mongoUserManager.PasswordValidators);
        if (command.IgnorePasswordValidation)
            mongoUserManager.PasswordValidators.Clear();

        IdentityResult result = await mongoUserManager.ChangePasswordAsync(user, command.CurrentPassword, command.NewPassword);

        if (command.IgnorePasswordValidation)
            foreach (IPasswordValidator<MongoLoginDao> passwordValidator in identityPasswordValidators)
                mongoUserManager.PasswordValidators.Add(passwordValidator);

        if (!result.Succeeded)
        {
            _logger.LogError($"{result.Errors.First().Code}|{result.Errors.First().Description}");
            return new Result { Status = "failure", Errors = result.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() };
        }

        command.ChangedById = loginId;

        user.PasswordLastUpdated = DateTime.UtcNow;

        IdentityResult updateResult = await mongoUserManager.UpdateAsync(user);
        if (!updateResult.Succeeded)
        {
            _logger.LogError($"{updateResult.Errors.First().Code}|{updateResult.Errors.First().Description}");
            return new Result { Status = "failure", Errors = updateResult.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() };
        }

        await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.changePassword, DateTime.UtcNow)
        {
            Values = JObject.FromObject(command, _jsonSerializer)
        }, cancellationToken);

        if (command.SendNotificationCommand != null)
        {
            EmailMessage emailMessage = command.SendNotificationCommand.EmailMessage;
            emailMessage.To = emailMessage?.To ?? user.Email;

            if (emailMessage.HtmlContent != null)
            {
                emailMessage.HtmlContent = emailMessage.HtmlContent.Replace("{{email}}", emailMessage.To);
            }
            if (emailMessage.TemplateRendering != null)
                emailMessage.TemplateRendering.Input = new RenderParameters { Name = "data", Content = JToken.FromObject(new { changedAt = DateTime.UtcNow }) };

            Result notificationResult = await SendNotification(tenantId, command.SendNotificationCommand, cancellationToken);

            if (!notificationResult.IsSuccess)
            {
                return notificationResult;
            }
        }

        return new Result { Status = "success" };
    }

    [HttpPost("logins/changeExpiredPassword")]
    public async Task<ActionResult<Result>> ChangeExpiredPasswordAsync(
        [FromBody] ChangeExpiredPasswordCommand command, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        UserManager<MongoLoginDao> mongoUserManager = _authService.GetMongoUserManager(tenantId);
        bool enableFindLoginByNameOrEmail = await _multiTenantFeatureManager.IsEnabled("EnableFindLoginByNameOrEmail", tenantId);

        MongoLoginDao user = enableFindLoginByNameOrEmail && MailAddress.TryCreate(command.UserName, out _)
            ? await mongoUserManager.FindByEmailAsync(command.UserName)
            : await mongoUserManager.FindByNameAsync(command.UserName);

        if (user == null) return new Result
        {
            Status = "failure",
            Errors = new List<string> { $"Login does not exist" }
        };

        if (user.IsTemporaryPassword) return new Result
        {
            Status = "failure",
            Errors = new List<string> { $"Cannot chnage temporary password in this way" }
        };

        if (!user.IsActive)
            return new Result
            {
                Status = "failure", Errors = new List<string> { $"The user is not active" }
            };

        string clientId = user.Claims.FirstOrDefault(c => c.Type == "clientId")?.Value ?? "default";

        PasswordValidators passwordValidators =
            await _authService.GetPasswordValidatorsAsync(tenantId, clientId, cancellationToken);

        if (passwordValidators == null)
            return new Result
            {
                Status = "failure", Errors = new List<string> { $"No password expiration configuration found" }
            };

        if (!passwordValidators.AllowExpiredPasswordEasyReset)
            return new Result
            {
                Status = "failure", Errors = new List<string> { $"Expired password easy reset is not allowed" }
            };

        Result passwordValidationResult = await ValidatePassword(mongoUserManager, tenantId, user, command.NewPassword);
        if (!passwordValidationResult.IsSuccess) return passwordValidationResult;

        string newPasswordHash = mongoUserManager.PasswordHasher.HashPassword(user, command.NewPassword);

        int? savePasswordsCount = passwordValidators.SavePasswordHistoryCount;
        if (savePasswordsCount > 0)
        {
            bool? previousContainsHashedPassword = user.PreviouslyUsedPasswords
                ?.Select(p => mongoUserManager.PasswordHasher.VerifyHashedPassword(user, p, command.NewPassword))
                .Any(a => a == PasswordVerificationResult.Success);
            if (previousContainsHashedPassword == true)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string>
                    {
                        $"Please choose a new password, You cannot use a previous password."
                    }
                };
            user.PreviouslyUsedPasswords = LoginExtensions.AddToListandRemoveExtra(newPasswordHash,
                user.PreviouslyUsedPasswords, (int) savePasswordsCount);
        }

        int? passwordLifetime = passwordValidators.PasswordLifespanInSeconds;
        if (passwordLifetime == null)
            return new Result
            {
                Status = "failure", Errors = new List<string> { $"No password expiration configuration found" }
            };

        DateTime now = DateTime.UtcNow;
        if ((now - user.PasswordLastUpdated)?.TotalSeconds < passwordLifetime)
            return new Result
            {
                Status = "failure", Errors = new List<string> { $"The password is not yet expired" }
            };

        user.PasswordLastUpdated = now;
        PasswordVerificationResult verifyResult =
            mongoUserManager.PasswordHasher.VerifyHashedPassword(user, user.PasswordHash, command.CurrentPassword);

        if (verifyResult != PasswordVerificationResult.Success)
            return new Result { Status = "failure", Errors = new List<string> { $"Unable update the password" } };

        user.PasswordHash = newPasswordHash;
        user.PasswordLastUpdated = now;

        await _eventStore.AddEventAsync(tenantId,
            new LoginEvent(user.Id, LoginEventType.changeExpiredPassword, now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

        await mongoUserManager.UpdateAsync(user);
        return Result.Success();
    }

    [HttpPost("logins/{loginId}/updateLockoutEndDate")]
    public async Task<ActionResult<Result>> UpdateLockoutEndDate(string loginId,
        [FromBody] ChangeUserLockoutDateCommand command, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);
        return await _authService.UpdateLoginLockoutEndDate(tenantId, loginId, command, cancellationToken);
    }

    [HttpPost("logins/targetedPermissionSchemas/add")]
    public Task<Result> AddTargetedPermissionSchemaToLogin(
        [FromBody] AddTargetedPermissionSchemaToLoginCommand command, CancellationToken cancellationToken) =>
        _authService.AddTargetedPermissionSchemaToLogin(GetTenantId(), command, cancellationToken);

    [HttpPost("logins/targetedPermissionSchemas/remove")]
    public Task<Result> RemoveTargetedPermissionSchemaFromLogin(
        [FromBody] RemoveTargetedPermissionSchemaFromLoginCommand command, CancellationToken cancellationToken) =>
        _authService.RemoveTargetedPermissionSchemaFromLogin(GetTenantId(), command, cancellationToken);

    [HttpPost("logins/targetedPermissionSchemas")]
    public Task<IReadOnlyCollection<TargetedPermissionSchema>> GetTargetedPermissionSchemas(
        [FromBody] IReadOnlyCollection<string> targetedPermissionSchemaIds, CancellationToken cancellationToken) =>
        _authService.GetTargetedPermissionSchemas(GetTenantId(), targetedPermissionSchemaIds, cancellationToken);

    [HttpPost("logins/scheduledNotifications")]
    public async Task<Result> SendScheduledNotifications(CancellationToken cancellationToken)
    {
        string tenantId = GetTenantId();
        UserManager<MongoLoginDao> _mongoUserManager = _authService.GetMongoUserManager(tenantId);
        var notifications = await _authService.GetScheduledNotifications(tenantId, cancellationToken);

        string encodedCode = string.Empty;

        foreach (var notification in notifications)
        {
            try
            {
                MongoLoginDao user = await _authService.FindLoginByIdAsync(tenantId, notification.LoginId, cancellationToken);

                if (user == null)
                {
                    _logger.LogError($"User not found for notificationId: {notification.Id}");
                    continue;
                }

                App app = (await _authService.GetAppsAsync(tenantId, new AppWhere { AppId = notification.AppId }, null, null, null, cancellationToken))?.FirstOrDefault();

                if (app == null)
                {
                    _logger.LogError($"App not found for notificationId: {notification.Id}");
                    continue;
                }

                switch (notification.Type)
                {
                    case "password_set":
                    case "password_forgot":
                        (string passwordResetToken, Result updateResult) = await _mongoUserManager.GenerateSingleUsePasswordResetTokenAsync(user);
                        if (updateResult != null)
                        {
                            return Result.Failure(updateResult.Errors);
                        }
                        encodedCode = UrlEncoder.Default.Encode(passwordResetToken);
                        break;
                    case "email_confirmation":
                    case "password_reset":
                        string code = await _mongoUserManager.GenerateEmailConfirmationTokenAsync(user);
                        encodedCode = UrlEncoder.Default.Encode(code);
                        break;
                }

                var ret = await SendPasswordSetEmail(tenantId, app, notification.Type, new SendNotificationCommand
                {
                    Type = notification.Type,
                    FromEntityId = notification.FromEntityId,
                    ToEntityId = notification.ToEntityId,
                    PolicyId = notification.PolicyId,
                    OfferId = notification.OfferId,
                    PushMessage = notification.PushMessage,
                    EmailMessage = notification.EmailMessage,
                    SmsMessage = notification.SmsMessage,
                    UseConfig = notification.UseConfig,
                    SentById = notification.SentById,
                    LoginId = notification.LoginId,
                    CallbackUrl = notification.CallbackUrl,
                    RedirectQueryString = notification.RedirectQueryString,
                    Language = notification.Language
                }, encodedCode, cancellationToken);

                var toUpdate = new UpsertSendNotificationScheduleCommand
                {
                    Id = notification.Id,
                    LastSentAt = DateTime.UtcNow
                };

                if (ret.IsSuccess)
                {
                    toUpdate.Status = "Sent";
                }

                var updateRet = await _authService.UpdateSendNotificationScheduleAsync(tenantId, toUpdate, cancellationToken);

                if (!updateRet.IsSuccess)
                {
                    _logger.LogError(string.Join(", ", updateRet.Errors));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending scheduled notification: {notification.Id}");
            }
        }
        return Result.Success();
    }

    [HttpPost("logins/scheduledNotifications/reschedule")]
    public async Task<Result> RescheduledNotification([FromBody] RescheduleNotificationCommand command, CancellationToken cancellationToken)
    {
        string tenantId = GetTenantId();

        var scheduledNotifications = await _authService.QueryScheduledNotifications(tenantId, new QueryArguments<Filter<SendNotificationScheduleFilter>>
        {
            Where = new Filter<SendNotificationScheduleFilter>
            {
                And = new List<Filter<SendNotificationScheduleFilter>>
                {
                    new Filter<SendNotificationScheduleFilter>
                    {
                        Where = new SendNotificationScheduleFilter
                        {
                            Status = "Scheduled"
                        }
                    },
                    new Filter<SendNotificationScheduleFilter>
                    {
                        Where = new SendNotificationScheduleFilter
                        {
                            LoginId = command.LoginId
                        }
                    },
                    new Filter<SendNotificationScheduleFilter>
                    {
                        Where = new SendNotificationScheduleFilter
                        {
                            Type = command.Type
                        }
                    }
                }
            }
        }, cancellationToken);

        if (scheduledNotifications.Count() == 0)
        {
            _logger.LogInformation($"No scheduled notifications found for loginId: {command.LoginId} and type: {command.Type}");
            return Result.Success();
        }

        return await _authService.UpdateSendNotificationScheduleAsync(tenantId, new UpsertSendNotificationScheduleCommand
        {
            Id = scheduledNotifications.First().Id,
            ScheduleToSendAt = command.ScheduleToSendAt
        }, cancellationToken);
    }

    string GetTenantId() =>
        Request.PathBase.Value.Remove(0, 1);

    static bool IsGuestUser(List<Claim> defClaims) =>
        defClaims.Exists(c => c.Type == "role" && c.Value == "guest");

    private async Task<Result> SendPasswordSetEmail(string tenantId, App app, string templateType, SendNotificationCommand command, string encodedCode, CancellationToken cancellationToken)
    {
        if (command == null)
            return Result.Failure("SendNotificationCommand is null");

        if (command.ScheduleToSendAt.HasValue)
        {
            try
            {
                var result = await _authService.CreateSendNotificationScheduleAsync(tenantId, new UpsertSendNotificationScheduleCommand
                {
                    Type = string.IsNullOrEmpty(command.Type) ? templateType : command.Type,
                    FromEntityId = command.FromEntityId,
                    ToEntityId = command.ToEntityId,
                    PolicyId = command.PolicyId,
                    OfferId = command.OfferId,
                    PushMessage = command.PushMessage,
                    EmailMessage = command.EmailMessage,
                    SmsMessage = command.SmsMessage,
                    UseConfig = command.UseConfig,
                    SentById = command.SentById,
                    AppId = app.AppId,
                    LoginId = command.LoginId,
                    CallbackUrl = command.CallbackUrl,
                    RedirectQueryString = command.RedirectQueryString,
                    Language = command.Language,
                    ScheduleToSendAt = command.ScheduleToSendAt,
                    Status = "Scheduled"
                }, cancellationToken);

                if (!result.IsSuccess)
                    return Result.Failure(result.Errors);

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cannot create scheduled notification");
                return Result.Failure(ex.Message);
            }
        }

        string clientEmail = app?.Email;
        string senderName = app?.EmailSenderName;
        string encodedAccessToken = string.Empty;
        string callbackUrl;

        switch (templateType)
        {
            case "password_set":
            case "email_confirmation":
                Token accessToken = await GetTokenAsLoginId(command.LoginId, app.AppId, cancellationToken);
                encodedAccessToken = UrlEncoder.Default.Encode(accessToken.AccessToken);
                callbackUrl = $"{app.RedirectUris.First()}?loginId={command.LoginId}&code={encodedCode}&accesstoken={encodedAccessToken}";
                if (command.RedirectQueryString != null)
                    callbackUrl += $"&{command.RedirectQueryString}";
                break;
            case "password_reset":
                string callbackBaseUrl = app.RedirectUris.First();
                if (command.CallbackUrl != null)
                {
                    callbackBaseUrl = command.CallbackUrl;
                }

                callbackUrl = $"{callbackBaseUrl}?loginId={command.LoginId}&code={encodedCode}";
                break;
            case "password_forgot":
                callbackUrl = $"{app.RedirectUris.ElementAt(1)}?loginId={command.LoginId}&code={encodedCode}&locale={command.Language}";
                break;
            case "email_welcome":
                callbackUrl = app.UrlRouting?.Url ?? string.Empty;
                break;
            default:
                return Result.Failure("Invalid email template type to set callback url");
        }

        string encodedCallbackUrl = HtmlEncoder.Default.Encode(callbackUrl);

        command.EmailMessage.From = command.EmailMessage.From ?? clientEmail ?? "<EMAIL>";
        command.EmailMessage.FromName = command.EmailMessage.FromName ?? senderName ?? "CoverGo";
        command.UseConfig = command.UseConfig ?? app.UseNotificationConfig;

        if (command.EmailMessage.HtmlContent != null)
        {
            command.EmailMessage.HtmlContent = command.EmailMessage.HtmlContent.Replace("{{callbackUrl}}", callbackUrl);
            command.EmailMessage.HtmlContent = command.EmailMessage.HtmlContent.Replace("{{email}}", command.EmailMessage.To);
            command.EmailMessage.HtmlContent = command.EmailMessage.HtmlContent.Replace("{{loginId}}", command.LoginId);
            command.EmailMessage.HtmlContent = command.EmailMessage.HtmlContent.Replace("{{code}}", encodedCode);
            command.EmailMessage.HtmlContent = command.EmailMessage.HtmlContent.Replace("{{accessToken}}", encodedAccessToken);
            command.EmailMessage.HtmlContent = command.EmailMessage.HtmlContent.Replace("{{redirectQueryString}}", command.RedirectQueryString);
        }

        if (command.EmailMessage.TemplateRendering != null)
        {
            var renderContent = JToken.FromObject(new
            {
                callbackUrl = encodedCallbackUrl,
                email = command.EmailMessage.To,
                loginId = command.LoginId,
                code = encodedCode,
                accessToken = encodedAccessToken,
                redirectQueryString = command.RedirectQueryString,
                locale = command.Language
            });

            if (command.EmailMessage.TemplateRendering.Input == null)
                command.EmailMessage.TemplateRendering.Input = new RenderParameters
                {
                    Name = "data",
                    Content = renderContent
                };
            else
            {
                if (command.EmailMessage.TemplateRendering.Input.Content?.Any() == true)
                    (command.EmailMessage.TemplateRendering.Input.Content as JObject).Merge(renderContent);
                else
                    command.EmailMessage.TemplateRendering.Input.Content = renderContent;
            }

            if (templateType == "password_forgot" && (await _multiTenantFeatureManager.IsEnabled("NewForgotPasswordFlow", tenantId)))
            {
                string appRootUrl = string.IsNullOrWhiteSpace(app.ClientUri)
                    ? GetRootUrl(app.RedirectUris.Skip(1).FirstOrDefault())
                    : app.ClientUri;

                // Replace link in template rendering input by appRootUrl
                if (!string.IsNullOrWhiteSpace(appRootUrl))
                {
                    JToken forgotPasswordLinkContent = JToken.FromObject(new
                    {
                        link = appRootUrl,
                    });

                    (command.EmailMessage.TemplateRendering.Input.Content as JObject)?.Merge(forgotPasswordLinkContent);
                }
            }
        }
        else if (string.IsNullOrEmpty(command.EmailMessage.HtmlContent))
        {
            string LoadEmailContent(string emailHtmlPath)
            {
                try
                {
                    var htmlContent = System.IO.File.ReadAllText(Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, emailHtmlPath)));
                    htmlContent = htmlContent.Replace("{{callbackUrl}}", encodedCallbackUrl);
                    htmlContent = htmlContent.Replace("{{email}}", command.EmailMessage.To);
                    htmlContent = htmlContent.Replace("{{locale}}", command.Language);
                    return htmlContent;
                }
                catch { return null; }
            }
            string emailHtmlPath;
            switch (templateType)
            {
                case "password_set":
                    emailHtmlPath = $"{tenantId}-{app.AppId}-email_confirmation.html";
                    command.EmailMessage.Subject = command.EmailMessage.Subject ?? "Set your password";
                    command.EmailMessage.HtmlContent = LoadEmailContent(emailHtmlPath) ?? $"Please set your password by <a href='{encodedCallbackUrl}'>clicking here</a>.";
                    break;
                case "password_reset":
                    emailHtmlPath = $"{tenantId}-{app.AppId}-email_confirmation.html";
                    command.EmailMessage.Subject = command.EmailMessage.Subject ?? "Reset your password";
                    command.EmailMessage.HtmlContent = LoadEmailContent(emailHtmlPath) ?? $"Please reset your password by <a href='{encodedCallbackUrl}'>clicking here</a>.";
                    break;
                case "email_confirmation":
                    emailHtmlPath = $"{tenantId}-{app.AppId}-email_confirmation.html";
                    command.EmailMessage.Subject = command.EmailMessage.Subject ?? "Confirm your email";
                    command.EmailMessage.HtmlContent = LoadEmailContent(emailHtmlPath) ?? $"Please confirm your account by <a href='{encodedCallbackUrl}'>clicking here</a>.";
                    break;
                case "password_forgot":
                    emailHtmlPath = $"{tenantId}-{app.AppId}-forgot_password.html";
                    command.EmailMessage.Subject = command.EmailMessage.Subject ?? "Reset your password";
                    command.EmailMessage.HtmlContent = LoadEmailContent(emailHtmlPath) ?? $"Please reset your password by <a href='{encodedCallbackUrl}'>clicking here</a>.";
                    break;
            }
        }

        return await SendNotification(tenantId, command, cancellationToken);
    }

    private async Task<Result> SendNotification(string tenantId, SendNotificationCommand command, CancellationToken cancellationToken)
    {
        Result notificationResult = await _notificationService.SendAsync(tenantId, command, cancellationToken);

        if (!notificationResult.IsSuccess)
        {
            _logger.LogError($"Errors sending notification: {string.Join(", ", notificationResult.Errors)}");
        }

        return notificationResult;
    }

    private async Task<Result> ValidatePassword(UserManager<MongoLoginDao> mongoUserManager, string tenantId, MongoLoginDao user, string password, bool enforcePasswordValidation = false)
    {
        if (!enforcePasswordValidation && !await _multiTenantFeatureManager.IsEnabled("PasswordComplianceCheck", tenantId))
        {
            return Result.Success();
        }

        List<Error> errors = new() { };
        _logger.LogInformation($"[{tenantId}] Validating password with validators: {string.Join(", ", mongoUserManager.PasswordValidators?.Select(pv => pv.GetType().Name))}");
        foreach (IPasswordValidator<MongoLoginDao> identityPV in mongoUserManager.PasswordValidators)
        {
            IdentityResult result = await identityPV.ValidateAsync(mongoUserManager, user ?? new MongoLoginDao(), password);
            if (!result.Succeeded)
            {
                errors.AddRange(result.Errors.Select(e => new Error
                {
                    Code = e.Code,
                    Message = e.Description
                }));
            }
        }

        return new Result
        {
            Status = !errors.Any() ? "success" : "failure",
            Errors_2 = errors
        };
    }

    private async Task<bool> EmailAlreadyExists(UserManager<MongoLoginDao> mongoUserManager, string email)
    {
        if (string.IsNullOrEmpty(email))
            return false;

        var user = await mongoUserManager.FindByEmailAsync(email);
        return user != null;
    }

    private async Task<bool> UsernameAlreadyExists(UserManager<MongoLoginDao> mongoUserManager, string username)
    {
        if (string.IsNullOrEmpty(username))
            return false;

        var user = await mongoUserManager.FindByNameAsync(username);
        return user != null;
    }
    private static IEnumerable<string> AddNormalizedEmailFilter(IEnumerable<string> Email_in)
    {
        List<string> inputEmailIn = Email_in.ToList();
        inputEmailIn.AddRange(Email_in.Select(email => email.ToUpper()));

        return inputEmailIn;
    }

    private static async Task UnlockUser(UserManager<MongoLoginDao> mongoUserManager, MongoLoginDao user)
    {
        if (!await mongoUserManager.IsLockedOutAsync(user) && user.LockoutEndDateUtc <= DateTime.UtcNow)
            return;

        if (!user.LockoutEnabled)
        {
            await mongoUserManager.SetLockoutEnabledAsync(user, true);
        }

        await mongoUserManager.ResetAccessFailedCountAsync(user);
        await mongoUserManager.SetLockoutEndDateAsync(user, DateTimeOffset.Now.AddDays(-1));
        await mongoUserManager.SetLockoutEnabledAsync(user, false);
    }

    private static bool IsTenantTcb(string tenantId)
    {
        return (tenantId == "tcb" || tenantId == "tcb_uat");
    }

    private static string? GetClaim(ClaimsIdentity identity, string claimType) => identity.Claims.FirstOrDefault(x => x.Type == claimType)?.Value;

    private static string GetRootUrl(string url)
    {
        // Regex pattern to match all except the last segment of the path
        Regex regex = new(@"(.*?://.*?)(/.*)?/[^/]*$", RegexOptions.None, TimeSpan.FromSeconds(1));

        Match match = regex.Match(url);
        if (match.Success)
        {
            string rootUrl = match.Groups[1].Value;
            if (match.Groups[2].Success)
            {
                rootUrl += match.Groups[2].Value;
            }

            return rootUrl;
        }

        return null;
    }

}

public static class LoginExtensions
{
    public static Login ToLogin(MongoLoginDao user, Permissions permissions) =>
        user == null
            ? null
            : new Login
            {
                Id = user.Id,
                Username = user.UserName,
                Email = user.Email,
                IsEmailConfirmed = user.EmailConfirmed,
                TelephoneNumber = user.PhoneNumber,
                IsTelephoneNumberConfirmed = user.PhoneNumberConfirmed,
                LockoutEndDateUtc = user.LockoutEndDateUtc,
                AccessFailedCount = user.AccessFailedCount,
                TargettedPermissions = permissions?.TargettedPermissions ?? new Dictionary<string, IEnumerable<string>>(),
                IgnorePasswordLifespan = user.IgnorePasswordLifespan,
                IgnoreAccountLockout = user.IgnoreAccountLockout,
                PermissionGroups = permissions?.PermissionGroups ?? Enumerable.Empty<PermissionGroup>(),
                InheritedLoginIds = permissions?.InheritedLoginIds ?? Enumerable.Empty<string>(),
                CreatedById = user.CreatedById,
                LastModifiedById = user.LastModifiedById,
                CreatedAt = user.CreatedAt.GetValueOrDefault(),
                LastModifiedAt = user.LastModifiedAt.GetValueOrDefault(),
                EntityId = user.EntityId,
                TargetedPermissionSchemaIds = user.TargetedPermissionSchemaIds,
                PermissionLazyLoadingRequired = permissions?.LazyLoadingRequired ?? false,
                IsActive = user.IsActive,
                LatestOtp = user.LatestOtp,
                PasswordLastUpdated = user.PasswordLastUpdated
            };

    public static List<string> AddToListandRemoveExtra(string newItem, List<string> list, int maxLength)
    {
        //helper fuction used for passwordHistory storage
        //Add new item to back of list
        if (list == null) list = new List<string> { };
        list.Add(newItem);
        var extraItems = list.Count - maxLength;

        if (extraItems > 0)
        {
            //remove oldest extra items from start of list
            list.RemoveRange(0, extraItems);
        }

        return list;
    }
}