﻿using CoverGo.Auth.Domain;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application
{
    //NOTE: generate AppController automatically from API Reference file. Put logic into contoller interface
    [Route("api/v1/auth/apps")]
    public class AppController : ControllerBase
    {

        private readonly AuthService _authService;

        public AppController(AuthService authService)
        {
            _authService = authService;
        }

        [HttpPost("query")]
        public async Task<IEnumerable<App>> GetAppsAsync([FromBody] AppQueryArguments queryArguments, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            return await _authService.GetAppsAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);
        }
        [HttpPost("events")]
        public async Task<IEnumerable<EventLog>> GetEvents([FromBody] EventQuery query,
            CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            IEnumerable<AppEvent> appEvents = await _authService.GetAppEventsAsync(tenantId, null, query.Ids, cancellationToken);
            return HandleLogs(appEvents);
        }

        private static IEnumerable<EventLog> HandleLogs(IEnumerable<AppEvent> appEvents) => //NOTE: share this somewhere or duplicate?
         appEvents.Select(e =>
             new EventLog
             {
                 RelatedId = e.AppId,
                 Type = e.Type.ToString(),
                 Timestamp = e.Timestamp,
                 ById = GetById(e)
             })?.ToList();

        private static string GetById(AppEvent @event)
        {
            if (@event.Type == AppEventType.createApp) return @event.Values.Value<string>("createdById");
            else if (@event.Type == AppEventType.deleteApp) return @event.Values.Value<string>("deletedById");
            else return @event.Values.Value<string>("modifiedById");
        }

        [HttpPost("totalCount")]
        public async Task<long> GetTotalCount([FromBody] AppWhere @where, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            long totalCount = await _authService.GetAppTotalCount(tenantId, @where, cancellationToken);
            return totalCount;
        }

        [HttpPost("create")]
        public async Task<Result> CreateAppAsync([FromBody] CreateAppCommand command, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            App app = await _authService.GetAppAsync(tenantId, command.AppId, cancellationToken);

            if (app != null) return new Result { Status = "failure", Errors = new List<string> { "This AppId is already in use" } };

            Result result = await _authService.CreateAppAsync(tenantId, command, cancellationToken);

            return result;
        }

        [HttpPost("{appId}/update")]
        public async Task<Result> UpdateAppAsync(string appId, [FromBody] UpdateAppCommand command, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            Result result = await _authService.UpdateAppAsync(tenantId, appId, command, cancellationToken);

            return result;
        }

        [HttpPost("{appId}/delete")]
        public async Task<Result> DeleteAppAsync(string appId, [FromBody] DeleteCommand command, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            Result result = await _authService.DeleteAppAsync(tenantId, appId, command, cancellationToken);

            return result;
        }
    }
}
