﻿using System.Threading;
using System.Threading.Tasks;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.Otp;
using CoverGo.Auth.Domain.Otp.Commands;
using CoverGo.Auth.Domain.Otp.Responses;
using IdentityModel.Client;
using Microsoft.AspNetCore.Mvc;

namespace CoverGo.Auth.Application
{
    [Route("api/v1/otpLogin")]
    public class OtpController : ControllerBase
    {
        private readonly IOtpService _otpService;

        public OtpController(IOtpService otpService)
        {
            _otpService = otpService;
        }

        [HttpPost("preOtpLogin")]
        public async Task<ActionResult<PreOtpLogin>> CreatePreOtpLoginToken([FromBody] CreatePreOtpLoginCommand command)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            return Ok(_otpService.CreatePreOtpLogin(tenantId, command));
        }

        [HttpPost("otpLogin")]
        public async Task<ActionResult<OtpLogin>> CreateOtpLogin([FromBody] CreateOtpLoginCommand command, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            return Ok(await _otpService.CreateOtpLoginAsync(tenantId, command, cancellationToken));
        }

        [HttpPost("accessTokenFromOtp")]
        public async Task<ActionResult<TokenResponse>> CreateAccessTokenFromOtpLogin([FromBody] CreateAccessTokenFromOtpLoginCommand command, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            return Ok(await _otpService.CreateAccessTokenFromOtpLoginAsync(tenantId, command, cancellationToken));
        }

        [HttpGet("sendRemarksOTP")]
        public async Task<ActionResult> GetRemarksOTP([FromBody] string username, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            return Ok(await _otpService.GetRemarksOTPAsync(tenantId, username, cancellationToken));
        }
        

        [HttpPost("validateRemarksOTP")]
        public async Task<ActionResult> ValidateRemarksOTP([FromBody] OTPRemarks lstUser, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            return Ok(await _otpService.ValidateRemarksOTPAsync(tenantId, lstUser, cancellationToken));
        }
    }
}
