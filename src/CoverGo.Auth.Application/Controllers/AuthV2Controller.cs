﻿using CoverGo.Auth.Domain;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.Controllers;

[Route("api/v2/auth")]
public class AuthV2Controller : ControllerBase
{
    private readonly AuthService _authService;

    public AuthV2Controller(AuthService authService)
    {
        _authService = authService;
    }

    [HttpPost("logins/events")]
    public async Task<IEnumerable<DetailedEventLog>> GetEventsV2([FromBody] QueryArguments<LoginEventWhere> query, CancellationToken cancellationToken)
    {
        string tenantId = Request.PathBase.Value.Remove(0, 1);

        IEnumerable<LoginEvent> loginEvents = await _authService.GetLoginEventsV2Async(tenantId, query, cancellationToken);
        return AuthController.HandleLoginLogs(loginEvents);
    }
}
