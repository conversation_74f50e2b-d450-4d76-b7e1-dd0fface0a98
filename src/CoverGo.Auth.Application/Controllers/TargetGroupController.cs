﻿using CoverGo.Auth.Domain;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application
{
    //NOTE: generate TargetGroupController automatically from API Reference file. Put logic into controller interface
    [Route("api/v1/auth/targetgroups")]
    public class TargetGroupController : ControllerBase
    {
        private readonly AuthService _authService;

        public TargetGroupController(AuthService authService)
        {
            _authService = authService;
        }

        [HttpPost]
        public async Task<ActionResult<Result>> CreateTargetGroup([FromBody] CreateTargetGroupCommand command, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            Result result = await _authService.CreateTargetGroupAsync(tenantId, command, cancellationToken);

            return result;
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<Result>> UpdateTargetGroup(string id, [FromBody] UpdateTargetGroupCommand command, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            Result result = await _authService.UpdateTargetGroupAsync(tenantId, id, command, cancellationToken);

            return result;
        }

        [HttpPost("{id}")]
        public async Task<ActionResult<Result>> DeleteTargetGroup(string id, [FromBody] DeleteCommand command, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            Result result = await _authService.DeleteTargetGroupAsync(tenantId, id, command, cancellationToken);

            return result;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<TargetGroup>>> GetTargetGroups(CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            return Ok(await _authService.GetAllTargetGroups(tenantId, cancellationToken));
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<TargetGroup>> GetTargetGroup(string id, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            return Ok(await _authService.GetTargetGroupAsync(tenantId, id, cancellationToken));
        }

        [HttpPost("{id}/user/{targetId}")]
        public async Task<ActionResult<Result>> AddUserToTargetGroup(string id, string targetId, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            Result result = await _authService.AddUserToTargetGroupAsync(tenantId, id, targetId, cancellationToken);

            return result;
        }

        [HttpPost("{id}/targetgroup/{targetGroupId}")]
        public async Task<ActionResult<Result>> AddTargetGroupToTargetGroup(string id, string targetGroupId, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            Result result = await _authService.AddTargetGroupToTargetGroupAsync(tenantId, id, targetGroupId, cancellationToken);

            return result;
        }

        [HttpDelete("{id}/user/{targetId}")]
        public async Task<ActionResult<Result>> RemoveUserFromTargetGroup(string id, string targetId, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            Result result = await _authService.RemoveUserFromTargetGroupAsync(tenantId, id, targetId, cancellationToken);

            return result;
        }

        [HttpDelete("{id}/targetgroup/{targetGroupId}")]
        public async Task<ActionResult<Result>> RemoveTargetGroupFromTargetGroup(string id, string targetGroupId, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            Result result = await _authService.RemoveTargetGroupFromTargetGroupAsync(tenantId, id, targetGroupId, cancellationToken);

            return result;
        }
    }
}
