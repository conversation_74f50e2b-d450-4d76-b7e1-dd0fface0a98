﻿using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.Abstraction;
using CoverGo.Auth.Domain.Permission.Queries;
using CoverGo.Auth.Domain.Utils;
using CoverGo.DomainUtils;
using IdentityServer4.Stores;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application.Controllers
{
    public class PermissionController : ControllerBase
    {
        private readonly IResourceStore _resourceStore;
        private readonly IEventStore _eventStore;
        private readonly JsonSerializer _jsonSerializer;
        private readonly AuthService _authService;
        private readonly IQueryHandler<GetPermissionTargetIdsQuery, PermissionTargetIds> _permissionTargetIdQueryHandler;
        readonly IQueryHandler<GetMultipleLoginPermissionsQuery, LoginPermission[]>
            _getLoginPermissionsQueryHandler;

        public PermissionController(
            IEventStore eventStore,
            IResourceStore resourceStore,
            AuthService authService,
            IQueryHandler<GetPermissionTargetIdsQuery, PermissionTargetIds> permissionTargetIdQueryHandler, 
            IQueryHandler<GetMultipleLoginPermissionsQuery, LoginPermission[]> getLoginPermissionsQueryHandler)
        {
            _eventStore = eventStore;
            _resourceStore = resourceStore;
            _authService = authService;
            _permissionTargetIdQueryHandler = permissionTargetIdQueryHandler;
            _getLoginPermissionsQueryHandler = getLoginPermissionsQueryHandler;

            var settings = new JsonSerializerSettings {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                TypeNameHandling = TypeNameHandling.Auto,
                NullValueHandling = NullValueHandling.Ignore,
            };
            settings.Converters.Add(new StringEnumConverter());

            _jsonSerializer = JsonSerializer.Create(settings);
        }

        [HttpPost("~/api/v1/auth/permissions/{loginId}")]
        public async Task<ActionResult<Result>> AddPermissions(string loginId,
            [FromBody] List<AddTargettedPermissionCommand> commands, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            MongoLoginDao user = await _authService.GetLoginAsync(tenantId, loginId, cancellationToken);
            if (user == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };

            IEnumerable<AddTargettedPermissionCommand> addLoginCommands = commands.Where(c => c.Type == "logins");
            if (addLoginCommands.Any(a => a.Value == loginId))
                return new Result { Status = "failure", Errors = new List<string> { $"The loginId and value '{loginId}' cannot be the same for inheritedLogins" } };

            foreach (AddTargettedPermissionCommand addLoginCommand in addLoginCommands)
            {
                MongoLoginDao targetUser = await _authService.GetLoginAsync(tenantId, loginId, cancellationToken);
                IEnumerable<string> targetInheritedIds = await GetInheritedLoginIdsAsync(tenantId, new List<MongoLoginDao> { targetUser }, cancellationToken);
                if (targetInheritedIds.Contains(loginId))
                    return new Result { Status = "failure", Errors = new List<string> { $"The inheritedLogin '{addLoginCommand.Value}' you are trying to add already inherits from this user {loginId}" } };
            }

            AddTargettedPermissionCommand lastCommand = commands.LastOrDefault(a => a.AddedById != null);
            if (lastCommand != null)
            {
                user.LastModifiedById = lastCommand.AddedById;
                user.LastModifiedAt = DateTime.UtcNow;
                await _authService.UpdateLoginAsync(tenantId, user, cancellationToken);
            }

            IEnumerable<IdentityResult> results = await TaskWhenAllWithThrottling.ParallelForEachAsync(commands.Select(c=>c), 10,async (
                c) =>
                {
                    IdentityResult result = await _authService.AddClaimToLoginAsync(tenantId, user, new Claim(c.Type, c.Value), cancellationToken);

                    if (result.Succeeded)
                        await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.addPermission, DateTime.UtcNow)
                        {
                            Values = JObject.FromObject(c, _jsonSerializer)
                        }, cancellationToken);
                    return result;
                });


            return results.Any(a => !a.Succeeded)
                ? new Result { Status = "failure", Errors = results.Where(a => !a.Succeeded).Select(r => string.Join(",", r.Errors.Select(e => $"{e.Code}|{e.Description}"))).ToList() }
                : new Result { Status = "success" };
        }

        [HttpDelete("~/api/v1/auth/permissions/{loginId}/{type}/{value}")]
        public async Task<ActionResult<Result>> RemovePermission(string loginId, string type, string value, string removedById, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);

            MongoLoginDao user = await _authService.GetLoginAsync(tenantId, loginId, cancellationToken);
            if (user == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };

            if (removedById != null)
            {
                user.LastModifiedById = removedById;
                user.LastModifiedAt = DateTime.UtcNow;
                await _authService.UpdateLoginAsync(tenantId, user, cancellationToken);
            }

            IdentityResult result = await _authService.RemoveClaimFromLogin(tenantId, user, new Claim(type, value), cancellationToken);

            if (result.Succeeded)
            {
                await _eventStore.AddEventAsync(tenantId, new LoginEvent(loginId, LoginEventType.removePermission, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(new RemoveTargettedPermissionCommand
                    {
                        Type = type,
                        Value = value,
                        RemovedById = removedById
                    }, _jsonSerializer)
                }, cancellationToken);
            }

            return result.Succeeded
                ? new Result { Status = "success" }
                : new Result { Status = "failure", Errors = result.Errors.Select(e => $"{e.Code}|{e.Description}").ToList() };
        }

        [HttpPost("~/api/v1/auth/{loginId}/permissions/remove")]
        public async Task<Result> RemoveTargettedPermissions(string loginId, [FromBody] List<RemoveTargettedPermissionCommand> commands, CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            MongoLoginDao user = await _authService.GetLoginAsync(tenantId, loginId, cancellationToken);
            if (user == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The loginId '{loginId}' doesn't exist" } };

            user.LastModifiedById = commands.FirstOrDefault().RemovedById;
            user.LastModifiedAt = DateTime.UtcNow;
            await _authService.UpdateLoginAsync(tenantId, user, cancellationToken);
            IEnumerable<IdentityResult> results = await TaskWhenAllWithThrottling.ParallelForEachAsync(
                commands.Select(c => c), 10,
                async (c) =>
                {
                    IdentityResult removeResult = await _authService.RemoveClaimFromLogin(tenantId, user,
                        new Claim(c.Type, c.Value), cancellationToken);

                    if (removeResult.Succeeded)
                        await _eventStore.AddEventAsync(tenantId,
                            new LoginEvent(loginId, LoginEventType.removePermission, DateTime.UtcNow)
                            {
                                Values = JObject.FromObject(c, _jsonSerializer)
                            }, cancellationToken);
                    return removeResult;
                });

            return results.Any(a => !a.Succeeded)
                ? new Result { Status = "failure", Errors = results.Where(a => !a.Succeeded).Select(r => string.Join(",", r.Errors.Select(e => $"{e.Code}|{e.Description}"))).ToList() }
                : new Result { Status = "success" };
        }

        [Route("~/api/v1/auth/permissions")]
        [HttpGet]
        public async Task<ActionResult<IEnumerable<string>>> GetAllPermissionsAsync() =>
            Ok((await _resourceStore.FindApiResourceAsync("all_user_claims")).UserClaims); // TODO

        [HttpGet("api/v1/logins/{loginId}/permissions")]
        public async Task<ActionResult<IDictionary<string, IEnumerable<string>>>> GetPermisionTargetIdsAsync(
            string loginId,
            [FromQuery(Name = "names")] IEnumerable<string> permissionNames,
            CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            var query = new GetPermissionTargetIdsQuery(tenantId, loginId, permissionNames);
            var result = await _permissionTargetIdQueryHandler.Handle(
                query, cancellationToken);

            return Ok(result.Ids);
        }

        [HttpGet("api/v1/logins/permissions")]
        public async Task<ActionResult<IDictionary<string, IEnumerable<string>>>> GetLoginsPermissionsAsync(
            [FromQuery(Name = "ids")] string[] loginIds,
            CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            var result = await _getLoginPermissionsQueryHandler.Handle(
                new GetMultipleLoginPermissionsQuery(tenantId, loginIds), cancellationToken);

            return Ok(result);
        }

        [HttpPost("api/v1/logins/permissions/verifyFullAccessForClientId")]
        public async Task<ActionResult<bool>> VerifyFullAccessForClientIdAsync(
            [FromBody] FullAccessVerificationCommand command,
            CancellationToken cancellationToken)
        {
            string tenantId = Request.PathBase.Value.Remove(0, 1);
            List<string> permissionNames = new() { "clientId" };
            GetPermissionTargetIdsQuery query = new(tenantId, command.LoginId, permissionNames);
            PermissionTargetIds clientIds = await _permissionTargetIdQueryHandler.Handle(query, cancellationToken);

            return UserClaimsHelper.CanGrantFullAccessRestrictedContentPermission(clientIds.Ids["clientId"], command.ClientId);
        }

        private async Task<IEnumerable<string>> GetInheritedLoginIdsAsync(string tenantId,
            IEnumerable<MongoLoginDao> inheritedLogins, CancellationToken cancellationToken)
        {
            var inheritedLoginIds = inheritedLogins.SelectMany(il => il.Claims?.Where(c => c.Type == "logins").Select(a => a.Value).Distinct()).ToList();

            IEnumerable<MongoLoginDao> childLogins = Enumerable.Empty<MongoLoginDao>();
            if (inheritedLoginIds?.Any() ?? false)
                childLogins = await _authService.GetLoginsAsync(tenantId, new LoginWhere { Ids = inheritedLoginIds }, null, null, null, cancellationToken);

            if (childLogins.Any())
                inheritedLoginIds.AddRange(await GetInheritedLoginIdsAsync(tenantId, childLogins, cancellationToken));

            return inheritedLoginIds;
        }

    }

    public class AddTargettedPermissionCommand
    {
        public string Type { get; set; }
        public string Value { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveTargettedPermissionCommand
    {
        public string Type { get; set; }
        public string Value { get; set; }
        public string RemovedById { get; set; }
    }

    public class FullAccessVerificationCommand
    {
        public string TenantId { get; set; }
        public string ClientId { get; set; }
        public string LoginId { get; set; }

    }
}
