﻿using CoverGo.Auth.Domain.PermissionSchemas;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MongoDB.Driver;

namespace CoverGo.Auth.Application.Controllers
{
    [Route("api/v1/auth/permissionSchemas")]
    [ApiController]
    public class PermissionSchemasController : ControllerBase
    {
        public PermissionSchemasController(PermissionSchemaService service) =>
            _service = service;

        readonly PermissionSchemaService _service;

        [HttpPost("query")]
        public Task<IReadOnlyCollection<PermissionSchema>> GetPermissionSchemasAsync(string tenantId,
            [FromBody] QueryArguments<PermissionSchemaWhere> queryArguments, CancellationToken cancellationToken) =>
            _service.Get(tenantId, queryArguments.Where, cancellationToken, queryArguments.OrderBy, skip: queryArguments.Skip, first: queryArguments.First);

        [HttpPost("totalCount")]
        public Task<long> GetTotalCount(string tenantId, [FromBody] PermissionSchemaWhere @where, CancellationToken cancellationToken) =>
            _service.GetTotalCount(tenantId, @where, cancellationToken);

        [HttpPost("create")]
        public Task<Result<CreatedStatus>> Create(string tenantId, [FromBody] CreatePermissionSchemaCommand command, CancellationToken cancellationToken) =>
            _service.Create(tenantId, command, cancellationToken);

        [HttpPost("update")]
        public async Task<ActionResult<Result>> Update(string tenantId, [FromBody] UpdatePermissionSchemaCommand command, CancellationToken cancellationToken) =>
            await _service.Update(tenantId, command, cancellationToken);

        [HttpPost("{permissionSchemaId}/delete")]
        public async Task<ActionResult<Result>> Delete(string tenantId, string permissionSchemaId, [FromBody] DeleteCommand command, CancellationToken cancellationToken) =>
            await _service.Delete(tenantId, permissionSchemaId, command, cancellationToken);
    }
}