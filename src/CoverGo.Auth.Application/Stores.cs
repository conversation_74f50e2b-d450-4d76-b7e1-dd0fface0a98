﻿using CoverGo.Auth.Domain;
using IdentityServer4.Models;
using IdentityServer4.Stores;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Auth.Application
{
    public class CustomClientStore : IClientStore
    {
        private readonly AuthService _authService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger _logger;

        public CustomClientStore(AuthService authService, IHttpContextAccessor httpContextAccessor, ILogger<CustomClientStore> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _authService = authService;
            _logger = logger;
        }

        public async Task<IdentityServer4.Models.Client> FindClientByIdAsync(string clientId)
        {
            string tenantId = _httpContextAccessor.HttpContext.Request.PathBase.Value.Remove(0, 1);

            App app = await _authService.GetAppAsync(tenantId, clientId, default);
            if (app == null)
                _logger.LogError($"App {clientId} not found");

            return AppDao.ToAppDao(app);
        }
    }

    public class CustomResourceStore : IResourceStore
    {
        public Task<ApiResource> FindApiResourceAsync(string name)
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name));

            return Task.FromResult(_allApiResources.FirstOrDefault(ar => ar.Name == name));
        }

        public Task<IEnumerable<ApiResource>> FindApiResourcesByScopeAsync(IEnumerable<string> scopeNames) =>
            Task.FromResult(_allApiResources.Where(ar => scopeNames.Contains(ar.Name)));

        public Task<IEnumerable<IdentityResource>> FindIdentityResourcesByScopeAsync(IEnumerable<string> scopeNames) =>
            Task.FromResult(Enumerable.Empty<IdentityResource>());

        public Task<Resources> GetAllResourcesAsync()
        {
            var result = new Resources(Enumerable.Empty<IdentityResource>(), _allApiResources);
            return Task.FromResult(result);
        }

        private readonly List<ApiResource> _allApiResources = new List<ApiResource>
        {
            new ApiResource
            {
                Enabled = true,
                DisplayName = "custom_profile",
                Name = "custom_profile",
                ApiSecrets = new List<Secret>(),
                Description = null,
                Properties = new Dictionary<string, string>(),
                UserClaims = new List<string>
                {
                    "email",
                    "name",
                    "role",
                    "userId",
                    "userType",
                    "entityId",
                    "entityType",
                    "tenantId",
                    "appId",
                    "memberOf" //for dbs
                },
                Scopes = new List<Scope>
                {
                    new Scope
                    {
                        DisplayName = "custom_profile",
                        Name = "custom_profile",
                        Description = null,
                        Required = false,
                        Emphasize = false,
                        ShowInDiscoveryDocument = true,
                        UserClaims = new List<string>()
                    }
                }
            },
            new ApiResource
            {
                Enabled = true,
                DisplayName = "all_user_claims",
                Name = "all_user_claims",
                ApiSecrets = new List<Secret>(),
                Description = null,
                Properties = new Dictionary<string, string>(),
                Scopes = new List<Scope>
                {
                    new Scope
                    {
                        DisplayName = "all_user_claims",
                        Name = "all_user_claims",
                        Description = null,
                        Required = false,
                        Emphasize = false,
                        ShowInDiscoveryDocument = true,
                        UserClaims = new List<string>()

                    }
                },
                UserClaims = UserClaim.List()
            }
        };
    }
}
