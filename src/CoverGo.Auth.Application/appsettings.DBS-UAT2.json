﻿{
  "Serilog": {
    // When doing development we use normal formating and avoid doing json output
    "Using": [
      "Serilog.Sinks.Console"
    ],

    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore.Server.Kestrel": "Fatal",
        "System": "Debug"
      }
    }
  },
  "serviceUrls": {
    "logging": "http://logging-UAT2.rmhk.ibg.dev.cloudnow.dbs.com/",
    "auth": "http://auth-UAT2.rmhk.ibg.dev.cloudnow.dbs.com/",
    "users": "http://users-UAT2.rmhk.ibg.dev.cloudnow.dbs.com/",
    "notifications": "http://notification-UAT2.rmhk.ibg.dev.cloudnow.dbs.com/",
    "policies": "http://policies-UAT2.rmhk.ibg.dev.cloudnow.dbs.com/",
    "cases": "http://cases-UAT2.rmhk.ibg.dev.cloudnow.dbs.com/",
    "transactions": "http://localhost:60120/",
    "advisor": "http://localhost:60110/",
    "claims": "http://localhost:60080/"
  }
}