<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoFixture.AutoMoq" Version="4.17.0" />
    <PackageReference Include="AutoFixture.Xunit2" Version="4.17.0" />
    <PackageReference Include="FluentAssertions" Version="6.7.0" />
    <PackageReference Include="JunitXml.TestLogger" Version="2.1.81" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
    <PackageReference Include="Objectivity.AutoFixture.XUnit2.AutoMoq" Version="3.3.3" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="3.1.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.16.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.Auth.Application\CoverGo.Auth.Application.csproj" />
    <ProjectReference Include="..\CoverGo.Auth.Domain\CoverGo.Auth.Domain.csproj" />
  </ItemGroup>
</Project>
