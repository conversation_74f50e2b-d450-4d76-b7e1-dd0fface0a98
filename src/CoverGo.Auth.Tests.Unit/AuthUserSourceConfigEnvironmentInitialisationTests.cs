﻿using CoverGo.Auth.Application;
using FluentAssertions;
using System;
using Xunit;

namespace CoverGo.Auth.Tests.Unit
{
    public class AuthUserSourceConfigEnvironmentInitialisationTests
    {
        [Fact]
        public void GIVEN_no_env_variable_WHEN_load_cfg_THEN_it_is_mongoDb()
        {
            UserSourceConfig.Load<Program>();
            UserSourceConfig.Source.Should().Be(UserSource.MongoDb);
        }

        [Fact]
        public void GIVEN_env_variable_WHEN_load_cfg_THEN_it_is_initialized_from_environment_variable()
        {
            Environment.SetEnvironmentVariable("USERSOURCE", "LDAP");

            UserSourceConfig.Load<Program>();
            UserSourceConfig.Source.Should().Be(UserSource.LDAP);
        }

        [Fact]
        public void GIVEN_invalid_env_variable_WHEN_load_cfg_THEN_it_is_mongoDb()
        {
            Environment.SetEnvironmentVariable("USERSOURCE", "invalid");

            UserSourceConfig.Load<Program>();
            UserSourceConfig.Source.Should().Be(UserSource.MongoDb);
        }
    }
}
