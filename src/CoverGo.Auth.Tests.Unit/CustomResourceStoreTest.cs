﻿using CoverGo.Auth.Application;
using FluentAssertions;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Auth.Tests.Unit;

public class CustomResourceStoreTest
{
    [Fact]
    public async Task GIVEN_custom_resource_store_WHEN_get_all_resources_THEN_returns_not_null_resources_object()
    {
        var store = new CustomResourceStore();

        (await store.GetAllResourcesAsync()).Should().NotBeNull();
    }
}