﻿using CoverGo.Auth.Application;
using CoverGo.Auth.Domain;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Xunit;

namespace CoverGo.Auth.Tests.Unit;

public class UserClaimTests
{
    [Fact]
    public void GIVEN_user_claim_WHEN_list_THEN_user_claims_exist_in_list()
    {
        // Arrange
        IEnumerable<string> claims = typeof(UserClaim)
            .GetFields(BindingFlags.Public | BindingFlags.Static)
            .Select(field => ((UserClaim) field.GetValue(null))?.ToString());

        // Action
        ICollection<string> listClaims = UserClaim.List();

        // Assertion
        foreach (string claim in claims)
        {
            listClaims.Should().Contain(claim, $"Claim '{claim}' is not in the {nameof(UserClaim.List)}.");
        }
    }
}
