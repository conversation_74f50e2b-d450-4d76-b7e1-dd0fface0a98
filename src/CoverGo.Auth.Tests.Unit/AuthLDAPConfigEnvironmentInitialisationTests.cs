﻿using CoverGo.Auth.LDAP;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using System;
using Xunit;

namespace CoverGo.Auth.Tests.Unit
{
    public class AuthLDAPConfigEnvironmentInitialisationTests
    {
        [Fact]
        public void GIVEN_config_and_missing_env_variables_WHEN_load_it_THEN_value_from_config_is_retained()
        {
            string environmentVariablesPrefix = Guid.NewGuid().ToString();
            var cfg = new LDAPConfig { Url = "localhost", Port = "389", BindDn = "cn=ldap-ro,dc=contoso,dc=com", BindCredentials = "P@ss1W0Rd", SearchBase = "ou=users,DC=contoso,dc=com", SearchFilter = "(&(objectClass=posixAccount)(objectClass=person)(uid={0}))" };
            cfg.Load<Program>(environmentVariablesPrefix);
            cfg.Url.Should().Be("localhost");
        }

        [Fact]
        public void GIVEN_env_variable_WHEN_load_cfg_THEN_it_is_initialized_from_environment_variable()
        {
            string environmentVariablesPrefix =
                nameof(AuthDbConfigEnvironmentInitialisationTests) + Guid.NewGuid();
            string endpointEnvironmentVariableName = $"{environmentVariablesPrefix}Url";

            Environment.SetEnvironmentVariable(endpointEnvironmentVariableName, "NewUrl");
            var cfg = new LDAPConfig { Url = "localhost", Port = "389", BindDn = "cn=ldap-ro,dc=contoso,dc=com", BindCredentials = "P@ss1W0Rd", SearchBase = "ou=users,DC=contoso,dc=com", SearchFilter = "(&(objectClass=posixAccount)(objectClass=person)(uid={0}))" };

            try
            {
                cfg.Load<Program>(environmentVariablesPrefix);
            }
            finally
            {
                Environment.SetEnvironmentVariable(endpointEnvironmentVariableName, null);
            }
            cfg.Url.Should().Be("NewUrl");
        }

        [Fact]
        public void GIVEN_config_and_missing_env_variables_and_load_into_configuration_section_WHEN_load_it_THEN_value_from_config_is_loaded_into_configuration_section()
        {
            string environmentVariablesPrefix = Guid.NewGuid().ToString();
            var cfg = new LDAPConfig { Url = "localhost", Port = "389", BindDn = "cn=ldap-ro,dc=contoso,dc=com", BindCredentials = "P@ss1W0Rd", SearchBase = "ou=users,DC=contoso,dc=com", SearchFilter = "(&(objectClass=posixAccount)(objectClass=person)(uid={0}))" };
            cfg.Load<Program>(environmentVariablesPrefix);
            var configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();
            cfg.LoadIntoConfigurationSection(configuration);
            configuration.GetSection("LDAPConnection")["Url"].Should().Be("localhost");
        }

        [Fact]
        public void GIVEN_env_variable_WHEN_load_cfg_and_load_into_configuration_section_THEN_configuration_is_initialized_from_environment_variable()
        {
            string environmentVariablesPrefix =
                nameof(AuthDbConfigEnvironmentInitialisationTests) + Guid.NewGuid();
            string endpointEnvironmentVariableName = $"{environmentVariablesPrefix}Url";

            Environment.SetEnvironmentVariable(endpointEnvironmentVariableName, "NewUrl");
            var cfg = new LDAPConfig { Url = "localhost", Port = "389", BindDn = "cn=ldap-ro,dc=contoso,dc=com", BindCredentials = "P@ss1W0Rd", SearchBase = "ou=users,DC=contoso,dc=com", SearchFilter = "(&(objectClass=posixAccount)(objectClass=person)(uid={0}))" };
            var configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();
            try
            {
                cfg.Load<Program>(environmentVariablesPrefix);

                cfg.LoadIntoConfigurationSection(configuration);
            }
            finally
            {
                Environment.SetEnvironmentVariable(endpointEnvironmentVariableName, null);
            }
            configuration.GetSection("LDAPConnection")["Url"].Should().Be("NewUrl");
        }
    }
}
