﻿using System.Collections.Generic;
using CoverGo.Auth.Domain.Utils;
using FluentAssertions;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain.Utils
{
    public class SourceContainsHelperTests
    {
        [Fact]
        public void GIVEN_permissions_list_WHEN_permission_list_contains_sourceContains_THEN_return_true()
        {
            var permission1 = "{caseIdIfSourceContains= A100}";
            var permission2 = "{caseIdIfSourceContains= A100, A200}";
            var permission3 = "{caseIdIfSourceContains= A100, A200, A100}";

            SourceContainsHelper.IsSourceContainsPermission(permission1).Should().BeTrue();
            SourceContainsHelper.IsSourceContainsPermission(permission2).Should().BeTrue();
            SourceContainsHelper.IsSourceContainsPermission(permission3).Should().BeTrue();
        }

        [Fact]
        public void GIVEN_permissions_list_WHEN_permission_list_does_not_contain_sourceContains_THEN_return_false()
        {
            var permission1 = "{caseIdIfSourceContain= A100}";
            var permission2 = "{caseIdIfSourceCotains= A100, A200}";
            var permission3 = "{caseIdIfSourcContains= A100, A200, A100}";

            SourceContainsHelper.IsSourceContainsPermission(permission1).Should().BeFalse();
            SourceContainsHelper.IsSourceContainsPermission(permission2).Should().BeFalse();
            SourceContainsHelper.IsSourceContainsPermission(permission3).Should().BeFalse();
        }

        [Fact]
        public void GIVEN_targetted_id_sourceContains_WHEN_extract_source_id_from_it_THEN_return_source_ids_list()
        {
            var targettedId1 = "{caseIdIfSourceContains= A100}";
            var targettedId2 = "{caseIdIfSourceContains= A100, A200}";
            var targettedId3 = "{caseIdIfSourceContains= A100,    A200,A300}";


            List<string> sourceId1 = SourceContainsHelper.GetSourceIds(targettedId1);
            List<string> sourceId2 = SourceContainsHelper.GetSourceIds(targettedId2);
            List<string> sourceId3 = SourceContainsHelper.GetSourceIds(targettedId3);

            sourceId1.Count.Should().Be(1);
            sourceId1.Should().BeEquivalentTo(new List<string> { "A100" });

            sourceId2.Count.Should().Be(2);
            sourceId2.Should().BeEquivalentTo(new List<string> { "A100", "A200" });

            sourceId3.Count.Should().Be(3);
            sourceId3.Should().BeEquivalentTo(new List<string> { "A100", "A200", "A300" });
        }

        [Fact]
        public void GIVEN_policy_permissions_list_WHEN_permission_list_contains_sourceContains_THEN_return_true()
        {
            var permission1 = "{policyIdIfSourceEquals= A100}";
            var permission2 = "{policyIdIfSourceEquals= A100, A200}";
            var permission3 = "{policyIdIfSourceEquals= A100, A200, A100}";

            SourceContainsHelper.IsPolicySourceEqualsPermission(permission1).Should().BeTrue();
            SourceContainsHelper.IsPolicySourceEqualsPermission(permission2).Should().BeTrue();
            SourceContainsHelper.IsPolicySourceEqualsPermission(permission3).Should().BeTrue();
        }
    }
}
