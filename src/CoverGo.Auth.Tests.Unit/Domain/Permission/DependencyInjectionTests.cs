﻿using AutoFixture.Xunit2;
using CoverGo.Auth.Application.Extensions;
using CoverGo.Auth.Domain.Permission;
using CoverGo.Auth.Tests.Unit.Domain.Permission.Factories;
using CoverGo.Auth.Tests.Unit.Domain.Permission.TestData;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain.Permission;

public class DependencyInjectionTests
{
    [Theory]
    [ClassData(typeof(PermissionTestData))]
    public async Task GIVEN_user_has_claims_WHEN_resolve_THEN_should_resolve(
        IEnumerable<Claim> userClaims, Claim claimToResolve, string[] expectedIds)
    {
        var entityId = Guid.NewGuid().ToString();
        var sut = RegisterAndBuild(expectedIds, entityId);
        var context = PermissionContextFactory.Create(userClaims, entityId);

        var ids = await sut.ResolveAsync(context, claimToResolve);

        foreach (string expectedId in expectedIds) ids.Should().Contain(expectedId);
    }

    [Theory]
    [ClassData(typeof(ReplacersTestData))]
    public async Task GIVEN_user_has_replacer_permission_WHEN_resolve_THEN_should_resolve(
        IEnumerable<Claim> userClaims, Claim claimToResolve, string[] expectedIds)
    {
        var sut = RegisterAndBuild(expectedIds);
        var context = PermissionContextFactory.Create(userClaims);

        var ids = await sut.ResolveAsync(context, claimToResolve);

        ids = ids.Select(id => id[(id.IndexOf("/", StringComparison.Ordinal) + 1)..]);
        foreach (string expectedId in expectedIds) ids.Should().Contain(expectedId);
    }

    [Theory]
    [AutoData]
    public async Task GIVEN_read_files_permission_with_allowed_read_claim_replacer_WHEN_resolve_THEN_resolved_ids_should_be_merged_from_claimIds_and_claims3Ids(string[] expectedIds)
    {
        IPermissionVariableResolver sut = RegisterAndBuild(expectedIds);
        Claim claimToResolve = new("readFiles", "{allowedReadClaims}");
        List<Claim> userClaims = new()
        {
            new("readClaims", "{creatorRights}"),
            claimToResolve,
        };
        PermissionContext context = PermissionContextFactory.Create(userClaims);

        IEnumerable<string> ids = await sut.ResolveAsync(context, claimToResolve);

        foreach (string id in expectedIds)
            ids.Count(x => x == id).Should().Be(2);
    }

    [Theory]
    [AutoData]
    public async Task GIVEN_write_files_permission_with_allowed_write_claim_replacer_WHEN_resolve_THEN_resolved_ids_should_be_merged_from_claimIds_and_claims3Ids(string[] expectedIds)
    {
        IPermissionVariableResolver sut = RegisterAndBuild(expectedIds);
        Claim claimToResolve = new("writeFiles", "{allowedWriteClaims}");
        List<Claim> userClaims = new List<Claim>()
        {
            new("writeClaims", "{creatorRights}"),
            claimToResolve
        };
        PermissionContext context = PermissionContextFactory.Create(userClaims);

        IEnumerable<string> ids = await sut.ResolveAsync(context, claimToResolve);

        foreach (string id in expectedIds)
            ids.Count(x => x == id).Should().Be(2);
    }

    [Theory]
    [AutoData]
    public async Task GIVEN_read_claims_permission_with_allowed_read_policy_replacer_WHEN_resolve_THEN_resolved_ids_should_be_merged_from_claimIds_and_claims3Ids(string[] expectedIds)
    {
        IPermissionVariableResolver sut = RegisterAndBuild(expectedIds);
        Claim claimToResolve = new("readClaims", "{claimIdIf{allowedReadPolicies}}");
        List<Claim> userClaims = new List<Claim>()
        {
            new("readPolicies", Guid.NewGuid().ToString()),
            claimToResolve,
        };
        PermissionContext context = PermissionContextFactory.Create(userClaims);

        IEnumerable<string> ids = await sut.ResolveAsync(context, claimToResolve);

        foreach (string id in expectedIds)
            ids.Count(x => x == id).Should().Be(2);
    }

    private static IPermissionVariableResolver RegisterAndBuild(
        string[] expectedIds, string entityId = "testEntityId")
    {
        var services = new ServiceCollection();
        services.RegisterPermissionTypes(new ConfigurationManager());
        services.AddSingleton(MockFactory.CreateAdvisorMock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreateAuthMock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreateCaseMock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreateEntityMock(expectedIds, entityId).Object);
        services.AddSingleton(MockFactory.CreateClaimMock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreateClaim3Mock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreatePolicyMock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreateGopMock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreateNotificationMock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreateTransactionMock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreateFeatureManagementMock().Object);
        services.AddSingleton(MockFactory.CreateChannelManagementAgentsMock(expectedIds).Object);
        services.AddSingleton(MockFactory.CreatePolicyClientMock(expectedIds).Object);
        services.AddLogging();

        var serviceProvider = services.BuildServiceProvider();

        return serviceProvider.GetRequiredService<IPermissionVariableResolver>();
    }
}
