﻿using AutoFixture.Xunit2;
using CoverGo.Auth.Domain.Permission;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain.Permission;

public class ResolvedPermissionsDictionaryTests
{
    [Theory]
    [AutoData]
    public void Should_Add_And_Get(
        ResolvedPermissionsDictionary sut,
        string permissionType,
        List<string> ids)
    {
        sut.Add(permissionType, ids);

        sut[permissionType].Should().BeEquivalentTo(ids);
    }

    [Theory]
    [AutoData]
    public void Should_Add_To_Existing_Permission(
        ResolvedPermissionsDictionary sut,
        string permissionType,
        List<string> ids,
        List<string> newIds)
    {
        var expectedIds = newIds.Concat(ids).ToList();
        sut.Add(permissionType, ids);
        sut.Add(permissionType, newIds);

        sut[permissionType].Should().BeEquivalentTo(expectedIds);
    }
}