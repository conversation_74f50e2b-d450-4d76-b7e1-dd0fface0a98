﻿using AutoFixture.Xunit2;
using CoverGo.Auth.Domain.Permission;
using CoverGo.Auth.Tests.Unit.Domain.Permission.Factories;
using CoverGo.Auth.Tests.Unit.Domain.Permission.TestData;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain.Permission;

public class CompositePermissionVariableResolverTests
{
    [Theory]
    [ClassData(typeof(ReplacersTestData))]
    public async Task GIVEN_user_has_replacer_permission_WHEN_resolve_THEN_should_resolve(
        IEnumerable<Claim> userClaims, Claim claimToResolve, string[] expectedIds)
    {
        var sut = PermissionVariableResolverFactory.Create(expectedIds);
        var context = PermissionContextFactory.Create(userClaims);

        var ids = await sut.ResolveAsync(context, claimToResolve);

        ids = ids.Select(id => id[(id.IndexOf("/", StringComparison.Ordinal) + 1)..]);
        foreach (string expectedId in expectedIds) ids.Should().Contain(expectedId);
    }

    [Theory]
    [ClassData(typeof(PermissionTestData))]
    public async Task GIVEN_user_has_claims_WHEN_resolve_THEN_should_resolve(
        IEnumerable<Claim> userClaims, Claim claimToResolve, string[] expectedIds)
    {
        var entityId = Guid.NewGuid().ToString();
        var sut = PermissionVariableResolverFactory.Create(expectedIds, entityId);
        var context = PermissionContextFactory.Create(userClaims, entityId);

        var ids = await sut.ResolveAsync(context, claimToResolve);

        foreach (string expectedId in expectedIds) ids.Should().Contain(expectedId);
    }

    [Theory]
    [AutoData]
    public async Task GIVEN_user_with_entity_WHEN_resolve_entity_id_THEN_should_resolve(
        string[] ids, string entityId)
    {
        var sut = PermissionVariableResolverFactory.Create(ids);
        var claim = new Claim("readLogins", "{entityId}");
        var context = PermissionContextFactory.Create(new[] { claim }, entityId);

        var resolveIds = await sut.ResolveAsync(context, claim);

        resolveIds.Should().BeEquivalentTo(new[] { entityId });
    }

    [Theory]
    [AutoData]
    public async Task GIVEN_permission_resolved_to_null_WHEN_resolve_THEN_nulls_are_filtered_out(
        string id)
    {
        var sut = PermissionVariableResolverFactory.Create(
            new[] { id, null, "" });
        var claim = new Claim("readPolicies", "{creatorRights}");
        var context = PermissionContextFactory.Create(new[] { claim });

        var resolveIds = await sut.ResolveAsync(context, claim);

        resolveIds.Should().BeEquivalentTo(new[] { id });
    }

    [Theory]
    [AutoData]
    public async Task GIVEN_permission_value_is_an_id_WHEN_resolve_THEN_ids_are_resolved(
        string id)
    {
        var sut = PermissionVariableResolverFactory.Create(
            new[] { id });
        var claim = new Claim("readPolicies", id);
        var context = PermissionContextFactory.Create(new[] { claim });

        var resolveIds = await sut.ResolveAsync(context, claim);

        resolveIds.Should().BeEquivalentTo(new[] { id });
    }

    [Theory]
    [AutoData]
    public async Task GIVEN_writeFiles_permission_value_caseIdIfStakeHolder_WHEN_resolve_THEN_ids_are_replaced_in_string(
        string[] expectedIds)
    {
        string savedPermissionString = "cases/{caseIdIfStakeholder}";
        var sut = PermissionVariableResolverFactory.Create(expectedIds);
        var claim = new Claim("writeFiles", savedPermissionString);
        var context = PermissionContextFactory.Create(new[] { claim });
        var expectedPermissions = expectedIds.Select(i => savedPermissionString.Replace("{caseIdIfStakeholder}", i));

        var ids = await sut.ResolveAsync(context, claim);

        ids.Should().Contain(expectedPermissions);
    }

    [Theory]
    [AutoData]
    public async Task GIVEN_writeFiles_permission_value_claim_WHEN_resolve_THEN_ids_are_replaced_in_string(
        string[] expectedIds)
    {
        string savedPermissionString = "cases/{caseIdIfStakeholder}";
        var sut = PermissionVariableResolverFactory.Create(expectedIds);
        var claim = new Claim("writeFiles", savedPermissionString);
        var context = PermissionContextFactory.Create(new[] { claim });
        var expectedPermissions = expectedIds.Select(i => savedPermissionString.Replace("{caseIdIfStakeholder}", i));

        var ids = await sut.ResolveAsync(context, claim);

        ids.Should().Contain(expectedPermissions);
    }

    [Theory]
    [AutoData]
    public async Task GIVEN_writeClaims_permission_value_claim_WHEN_resolve_THEN_resolved_ids_should_be_merged_from_claimIds_and_claims3Ids(
        string[] expectedIds)
    {
        IPermissionVariableResolver sut = PermissionVariableResolverFactory.Create(expectedIds);
        Claim claim = new("readClaims", "{creatorRights}");
        PermissionContext context = PermissionContextFactory.Create(new[] { claim });

        IEnumerable<string> ids = await sut.ResolveAsync(context, claim);

        foreach (string id in expectedIds)
            ids.Count(x => x == id).Should().Be(2);
    }
}
