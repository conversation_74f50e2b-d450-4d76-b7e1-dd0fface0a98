﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Security.Claims;
using static CoverGo.Auth.Tests.Unit.Domain.Permission.TestData.DataGenerator;

namespace CoverGo.Auth.Tests.Unit.Domain.Permission.TestData;

public class PermissionTestData : IEnumerable<object[]>
{
    readonly List<object[]> _testData;

    public PermissionTestData()
    {
        _testData = new List<object[]>
        {
            GenerateTestData("readReviews", "{creatorRights}"),
            GenerateTestData("writeReviews", "{creatorRights}"),
            GenerateTestData("readLogins", "{creatorRights}"),
            GenerateTestData("writeLogins", "{creatorRights}"),
            GenerateTestData("readPermissionGroups", "{creatorRights}"),
            GenerateTestData("writePermissionGroups", "{creatorRights}"),
            GenerateTestData("updatePermissionGroups", "{creatorRights}"),
            GenerateTestData("deletePermissionGroups", "{creatorRights}"),
            GenerateTestData("readIndividuals", "{beneficiaryIdsIfCaseHolder}"),
            GenerateTestData("readCases", "{caseIdIfHolder}"),
            GenerateTestData("readCases", "{caseIdIfProposalReferrer}"),
            GenerateTestData("readCases", "{caseIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}"),
            GenerateTestData("readCases", $"{{caseIdIfSourceContains={Guid.NewGuid()}}}"),
            GenerateTestData("writeCases", $"{{caseIdIfSourceContains={Guid.NewGuid()}}}"),
            GenerateTestData("updateCases", $"{{caseIdIfSourceContains={Guid.NewGuid()}}}"),
            GenerateTestData("readCases", "{caseIdIfStakeholder}"),
            GenerateTestData("readCases", "{creatorRights}"),
            GenerateTestData("writeCases", "{creatorRights}"),
            GenerateTestData("updateCases", "{creatorRights}"),
            GenerateTestData("deleteCases", "{creatorRights}"),
            GenerateTestData("overrideOffers", "{creatorRights}"),
            GenerateTestData("readCases", "{caseIdIfCreator}"),
            GenerateTestData("writeCases", "{caseIdIfCreator}"),
            GenerateTestData("updateCases", "{caseIdIfCreator}"),
            GenerateTestData("overrideOffers", "{caseIdIfCreator}"),
            GenerateTestData("readIndividuals", "{insuredIdsIfCaseHolder}"),
            GenerateTestData("readProposals", "{proposalIdIfHolder}"),
            GenerateTestData("writeProposals", "{proposalIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}"),
            GenerateTestData("writeProposals", "{proposalIdIf{fromLinkTarget:hrOf:{entityId}}IsHolder}"),
            GenerateTestData("readClaims", "{creatorRights}"),
            GenerateTestData("writeClaims", "{creatorRights}"),
            GenerateTestData("updateClaims", "{creatorRights}"),
            GenerateTestData("deleteClaims", "{creatorRights}"),
            GenerateTestData("readClaims","readPolicies", "{claimIdIf{allowedReadPolicies}}"),
            GenerateTestData("writeClaims", "{claimIdIfClaimant}"),
            GenerateTestData("readClaims", "{claimIdIfPolicyReferrer}"),
            GenerateTestData("writeClaims", "{claimIdIfProvider}"),
            GenerateTestData("readClaims", "{claimIdIf{fromLinkSource:spouse:{entityId}}IsClaimant}"),
            GenerateTestData("readCompanies", "{companyIdIfTagsContainProvider}"),
            GenerateTestData("readIndividuals", "{creatorRights}"),
            GenerateTestData("writeIndividuals", "{creatorRights}"),
            GenerateTestData("updateIndividuals", "{creatorRights}"),
            GenerateTestData("deleteIndividuals", "{creatorRights}"),
            GenerateTestData("readCompanies", "{creatorRights}"),
            GenerateTestData("writeCompanies", "{creatorRights}"),
            GenerateTestData("updateCompanies", "{creatorRights}"),
            GenerateTestData("deleteCompanies", "{creatorRights}"),
            GenerateTestData("readInternals", "{creatorRights}"),
            GenerateTestData("writeInternals", "{creatorRights}"),
            GenerateTestData("readOrganizations", "{creatorRights}"),
            GenerateTestData("writeOrganizations", "{creatorRights}"),
            GenerateTestData("updateOrganizations", "{creatorRights}"),
            GenerateTestData("deleteOrganizations", "{creatorRights}"),
            GenerateTestData("readObjects", "{creatorRights}"),
            GenerateTestData("writeObjects", "{creatorRights}"),
            GenerateTestData("readIndividuals", $"{{individualIdIfSourceContains:{Guid.NewGuid()}}}"),
            GenerateTestData("readLogins", $"{{loginIdIfAssociatedIndividualSourceContains:{Guid.NewGuid()}}}"),
            GenerateTestData("readGOPs", "{gopIdIfMember}"),
            GenerateTestData("readNotificationSubscriptions", "{notificationIdIfSubscriber}"),
            GenerateTestData("readNotificationSubscriptions", "{notificationSubscriptionIdIfSubscriber}"),
            GenerateTestData("readNotificationSubscriptions", "{creatorRights}"),
            GenerateTestData("writeNotificationSubscriptions", "{creatorRights}"),
            GenerateTestData("readOffers", "{creatorRights}"),
            GenerateTestData("writeOffers", "{creatorRights}"),
            GenerateTestData("updateOffers", "{creatorRights}"),
            GenerateTestData("deleteOffers", "{creatorRights}"),
            GenerateTestData("overrideOffers", "{creatorRights}"),
            GenerateTestData("readPolicies", "{creatorRights}"),
            GenerateTestData("writePolicies", "{creatorRights}"),
            GenerateTestData("updatePolicies", "{creatorRights}"),
            GenerateTestData("deletePolicies", "{creatorRights}"),
            GenerateTestData("cancelPolicies", "{creatorRights}"),
            GenerateTestData("writeMemberMovements:submit", "{creatorRights}"),
            GenerateTestData("writeMemberMovements:draft", "{creatorRights}"),
            GenerateTestData("readMemberMovements:terminationReasons", "{creatorRights}"),
            GenerateTestData("writeMemberMovements:terminationReasons", "{creatorRights}"),
            GenerateTestData("readPolicies", "{policyIdIfInsured}"),
            GenerateTestData("readPolicies", "{policyIdIfReferrer}"),
            GenerateTestData("readPolicies", "{policyIdIfRenewal}"),
            GenerateTestData("readPolicies", "{policyIdIfUninsured}"),
            GenerateTestData("readPolicies", "{policyIdIfHolder}"),
            GenerateTestData("readPolicies", "{policyNumberIfHolder}"),
            GenerateTestData("readPolicies", "{insuredIdsIfHolder}"),
            GenerateTestData("readPolicies", "{policyIdIf{allowedReadIndividuals}IsHolder}"),
            GenerateTestData("writePolicies", "{policyIdIf{allowedWriteIndividuals}IsHolder}"),
            GenerateTestData("writePolicies", "{policyIdIf{allowedUpdateIndividuals}IsHolder}"),
            GenerateTestData("updatePolicies", "{policyIdIf{allowedUpdateIndividuals}IsHolder}"),
            GenerateTestData("deletePolicies", "{policyIdIf{allowedUpdateIndividuals}IsHolder}"),
            GenerateTestData("readPolicies", "{policyIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}"),
            GenerateTestData("readPolicies", "{policyIdIf{fromLinkSource:spouse:{entityId}}IsInsured}"),
            GenerateTestData("readPolicies", "{policyIdIf{fromLinkSource:spouse:{entityId}}IsUninsured}"),
            GenerateTestData("readProposals", "{creatorRights}"),
            GenerateTestData("writeProposals", "{creatorRights}"),
            GenerateTestData("readPaymentMethods", "{creatorRights}"),
            GenerateTestData("writePaymentMethods", "{creatorRights}"),
            GenerateTestData("readTransactions", "{creatorRights}"),
            GenerateTestData("writeTransactions", "{creatorRights}"),
            GenerateTestData("updateTransactions", "{creatorRights}"),
            GenerateTestData("deleteTransactions", "{creatorRights}"),
            GenerateTestData("readTransactions", "readPolicies" ,"{transactionIdIf{allowedReadPolicies}}"),
            GenerateTestData("readTransactions", "{transactionIdForClaimIdIfClaimant}"),
            GenerateTestData("readTransactions", "{transactionIdForPolicyIdIfHolder}"),
            GenerateTestData("readTransactions", "{transactionIdForClaimIdIf{fromLinkSource:spouse:{entityId}}IsClaimant}"),
            GenerateTestData("readIndividuals",
                new []
                {
                    new Claim("readIndividuals",  "{entityId}"),
                    new Claim("readIndividuals",  "{fromLinkSource:hrOf:{entityId}}"),
                },
                "{fromLinkTarget:workingFor:{allowedReadIndividuals}}"),
            GenerateTestData("readCases", "{caseIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}}"),
            GenerateTestData("writeCases", "{caseIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}}"),
            GenerateTestData("updateCases", "{caseIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}}"),
            GenerateTestData("readIndividuals",
                new []
                {
                    new Claim("readIndividuals",  "{entityId}"),
                    new Claim("readIndividuals",  "{fromLinkSource:hrOf:{entityId}}"),
                    new Claim("readIndividuals",  "{fromLinkTarget:workingFor:{allowedReadIndividuals}}"),
                },
                "{fromLinkTarget:dependent of primary insured:{allowedReadIndividuals}}"),
            GenerateTestData("writeIndividuals",
                new []
                {
                    new Claim("writeIndividuals",  "{entityId}"),
                    new Claim("writeIndividuals",  "{fromLinkSource:hrOf:{entityId}}"),
                    new Claim("writeIndividuals",  "{fromLinkTarget:workingFor:{allowedWriteIndividuals}}"),
                },
                "{fromLinkTarget:dependent of primary insured:{allowedWriteIndividuals}}"),
            GenerateTestData("updateIndividuals",
                new []
                {
                    new Claim("updateIndividuals",  "{entityId}"),
                    new Claim("updateIndividuals",  "{fromLinkSource:hrOf:{entityId}}"),
                    new Claim("updateIndividuals",  "{fromLinkTarget:workingFor:{allowedUpdateIndividuals}}"),
                },
                "{fromLinkTarget:dependent of primary insured:{allowedUpdateIndividuals}}"),
            GenerateTestDataWithTargetIds("readIndividuals", "{fromLinkSource:hrOf}", Array.Empty<string>()),
            GenerateTestDataWithTargetIds("readIndividuals", "{fromLinkSource}", Array.Empty<string>())
        };
    }

    public IEnumerator<object[]> GetEnumerator() => _testData.GetEnumerator();

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
}