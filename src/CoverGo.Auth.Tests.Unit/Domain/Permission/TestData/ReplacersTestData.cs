﻿using System.Collections;
using System.Collections.Generic;
using System.Security.Claims;
using static CoverGo.Auth.Tests.Unit.Domain.Permission.TestData.DataGenerator;

namespace CoverGo.Auth.Tests.Unit.Domain.Permission.TestData;

public class ReplacersTestData : IEnumerable<object[]>
{
    readonly List<object[]> _testData;

    public ReplacersTestData()
    {
        _testData = new List<object[]>
        {
            GenerateTestData(
                "readFiles",
                "readCases",
            "cases/{allowedReadCases}"),
            GenerateTestData(
                "readFiles",
                "readClaims",
                "claims/{allowedReadClaims}"),
            GenerateTestData(
                "readFiles",
                new Claim("readGOPs", "{gopIdIfMember}"),
                "gops/{allowedReadGOPs}"),
            GenerateTestData(
            "readFiles",
            "readPolicies",
            "policies/{allowedReadPolicies}"),
            GenerateTestData(
                "readFiles",
                new Claim("readProposals",  "{proposalIdIfHolder}"),
                "proposals/{allowedReadProposals}"),
            GenerateTestData(
                "writeFiles",
                "writeCases",
                "cases/{allowedWriteCases}"),
            GenerateTestData(
                "writeFiles",
                "updateCases",
                "cases/{allowedUpdateCases}"),
            GenerateTestData(
                "writeFiles",
                "writeClaims",
                "claims/{allowedWriteClaims}"),
            GenerateTestData(
                "writeFiles",
                "updateClaims",
                "claims/{allowedUpdateClaims}"),
            GenerateTestData(
                "writeFiles",
                new Claim("writeGOPs", "{gopIdIfMember}"),
                "gops/{allowedWriteGOPs}"),
            GenerateTestData(
                "writeFiles",
                new Claim("updateGOPs", "{gopIdIfMember}"),
                "gops/{allowedUpdateGOPs}"),
            GenerateTestData(
                "writeFiles",
                "writePolicies",
                "policies/{allowedWritePolicies}"),
            GenerateTestData(
                "writeFiles",
                "updatePolicies",
                "policies/{allowedUpdatePolicies}"),
            GenerateTestData(
                "writeFiles",
                new Claim("writeProposals",  "{proposalIdIfHolder}"),
                "proposals/{allowedWriteProposals}"),
        };
    }

    public IEnumerator<object[]> GetEnumerator() => _testData.GetEnumerator();

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
}