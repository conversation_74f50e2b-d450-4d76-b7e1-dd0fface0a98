﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;

namespace CoverGo.Auth.Tests.Unit.Domain.Permission.TestData;

internal static class DataGenerator
{
    public static object[] GenerateTestData(
        string permissionType, string permissionValue)
    {
        return GenerateTestDataWithTargetIds(permissionType, permissionValue, GenerateIds(3));
    }
    
    public static object[] GenerateTestDataWithTargetIds(
        string permissionType, string permissionValue, string[] targetIds)
    {
        return new object[]
        {
            new[]
            {
                new Claim(permissionType, permissionValue)
            },
            new Claim(permissionType, permissionValue),
            targetIds
        };
    }

    public static object[] GenerateTestData(
        string permissionType, string dependentClaimType, string permissionValue)
    {
        return GenerateTestData(
            permissionType,
            new Claim(dependentClaimType, "{creatorRights}"),
            permissionValue);
    }

    public static object[] GenerateTestData(
        string permissionType, Claim dependentClaim, string permissionValue)
    {
        return GenerateTestData(permissionType, new[] { dependentClaim }, permissionValue);
    }

    public static object[] GenerateTestData(
        string permissionType, IEnumerable<Claim> dependentClaims, string permissionValue)
    {
        var claims = dependentClaims.ToList();
        claims.Add(new Claim(permissionType, permissionValue));

        return new object[]
        {
            claims,
            new Claim(permissionType, permissionValue),
            GenerateIds()
        };
    }

    private static string[] GenerateIds(int count = 3) =>
        Enumerable.Range(0, count)
            .Select(_ => Guid.NewGuid().ToString())
            .ToArray();
}