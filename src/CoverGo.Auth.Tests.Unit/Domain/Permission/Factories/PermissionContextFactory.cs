﻿using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.Permission;
using System.Collections.Generic;
using System.Security.Claims;

namespace CoverGo.Auth.Tests.Unit.Domain.Permission.Factories;

internal static class PermissionContextFactory
{
    const string TenantId = "test";

    public static PermissionContext Create(
        IEnumerable<Claim> userClaims, string entityId)
    {
        var user = new MongoLoginDao()
        {
            EntityId = entityId
        };
        return new PermissionContext(
            TenantId,
            user,
            new UserClaimCollection(userClaims));
    }

    public static PermissionContext Create(
        IEnumerable<Claim> userClaims)
    {
        var user = new MongoLoginDao();
        var context = new PermissionContext(
            TenantId,
            user,
            new UserClaimCollection(userClaims));

        return context;
    }
}