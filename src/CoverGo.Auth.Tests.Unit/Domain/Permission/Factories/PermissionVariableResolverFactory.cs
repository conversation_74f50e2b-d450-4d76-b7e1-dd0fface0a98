﻿using CoverGo.Auth.Domain.Permission;
using CoverGo.Auth.Domain.Permission.Advisor;
using CoverGo.Auth.Domain.Permission.Auth;
using CoverGo.Auth.Domain.Permission.Case;
using CoverGo.Auth.Domain.Permission.ChannelManagement;
using CoverGo.Auth.Domain.Permission.Claim;
using CoverGo.Auth.Domain.Permission.Entity;
using CoverGo.Auth.Domain.Permission.Gop;
using CoverGo.Auth.Domain.Permission.Notification;
using CoverGo.Auth.Domain.Permission.Offer;
using CoverGo.Auth.Domain.Permission.Policy;
using CoverGo.Auth.Domain.Permission.Proposal;
using CoverGo.Auth.Domain.Permission.Replacers;
using CoverGo.Auth.Domain.Permission.Transaction;
using Microsoft.Extensions.Logging.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Auth.Tests.Unit.Domain.Permission.Factories;

internal static class PermissionVariableResolverFactory
{
    public static IPermissionVariableResolver Create(
        IEnumerable<string> expectedIds)
    {
        return Create(expectedIds, Guid.NewGuid().ToString());
    }

    public static IPermissionVariableResolver Create(
        IEnumerable<string> expectedIds,
        string entityId)
    {
        var advisorMock = MockFactory.CreateAdvisorMock(expectedIds);
        var authMock = MockFactory.CreateAuthMock(expectedIds);
        var caseMock = MockFactory.CreateCaseMock(expectedIds);
        var entityMock = MockFactory.CreateEntityMock(expectedIds, entityId);
        var claimMock = MockFactory.CreateClaimMock(expectedIds);
        var claim3Mock = MockFactory.CreateClaim3Mock(expectedIds);
        var policyMock = MockFactory.CreatePolicyMock(expectedIds);
        var policyClientMock = MockFactory.CreatePolicyClientMock(expectedIds);
        var gopMock = MockFactory.CreateGopMock(expectedIds);
        var agentsMock = MockFactory.CreateChannelManagementAgentsMock(expectedIds);
        var notificationMock = MockFactory.CreateNotificationMock(expectedIds);
        var transactionMock = MockFactory.CreateTransactionMock(expectedIds);
        var featureManagementMock = MockFactory.CreateFeatureManagementMock();

        return new CompositePermissionVariableResolver(
            new[]
            {
                new ReviewCreatorRightsPermissionVariableResolver(advisorMock.Object) as IPermissionVariableResolver,
                new LoginsCreatorRightsPermissionVariableResolver(authMock.Object),
                new PermissionGroupsCreatorRightsPermissionVariableResolver(authMock.Object),
                new BeneficiaryIdsIfCaseHolderPermissionVariableResolver(caseMock.Object),
                new CaseIdIfHolderPermissionVariableResolver(caseMock.Object),
                new CaseIdIfProposalReferrerPermissionVariableResolver(entityMock.Object ,caseMock.Object),
                new CaseIdIfRelationshipBasedPermissionVariableResolver(caseMock.Object, entityMock.Object),
                new CaseIdIfSourceContainsPermissionVariableResolver(caseMock.Object),
                new CaseIdIfStakeholderPermissionVariableResolver(caseMock.Object),
                new ProposalIdIfStakeholderPermissionVariableResolver(caseMock.Object),
                new CasesCreatorRightsPermissionVariableResolver(caseMock.Object),
                new InsuredIdsIfCaseHolderPermissionVariableResolver(caseMock.Object),
                new ProposalIdIfHolderPermissionVariableResolver(caseMock.Object),
                new ProposalIdIfRelationshipBasedPermissionVariableResolver(caseMock.Object, entityMock.Object),
                new ClaimCreatorRightsPermissionVariableResolver(claimMock.Object, claim3Mock.Object, featureManagementMock.Object),
                new ClaimIdIfAllowedReadPoliciesPermissionVariableResolver(claimMock.Object, claim3Mock.Object, policyMock.Object, featureManagementMock.Object),
                new ClaimIdIfClaimantPermissionVariableResolver(claimMock.Object),
                new ClaimIdIfPolicyReferrerPermissionVariableResolver(claimMock.Object,
                    entityMock.Object, policyMock.Object),
                new ClaimIdIfProviderPermissionVariableResolver(claimMock.Object, entityMock.Object),
                new ClaimIdIfRelationshipBasedPermissionVariableResolver(entityMock.Object, claimMock.Object),
                new CompanyIdIfTagsContainProviderPermissionVariableResolver(entityMock.Object),
                new EntityCreatorRightsPermissionVariableResolver(entityMock.Object),
                new EntityPermissionVariableResolver(),
                new ReadIndividualsSourcePermissionVariableResolver(entityMock.Object),
                new ReadLoginsAssociatedIndividualSourcePermissionVariableResolver(
                    entityMock.Object, authMock.Object ),
                new GopIdIfMemberPermissionVariableResolver(gopMock.Object),
                new NotificationIdIfSubscriberPermissionVariableResolver(notificationMock.Object),
                new NotificationSubscriptionIdIfSubscriberPermissionVariableResolver(
                    notificationMock.Object),
                new NotificationSubscriptionsCreatorRightsPermissionVariableResolver(
                    notificationMock.Object),
                new OfferCreatorRightsPermissionVariableResolver(caseMock.Object),
                new PolicyCreatorRightsPermissionVariableResolver(policyMock.Object),
                new PolicyIdIfInsuredPermissionVariableResolver(policyMock.Object),
                new PolicyIdIfReferrerPermissionVariableResolver(policyMock.Object, entityMock.Object),
                new PolicyIdIfRenewalPermissionVariableResolver(policyMock.Object),
                new PolicyIdIfStakeholderPermissionVariableResolver(policyMock.Object),
                new PolicyIdIfUninsuredPermissionVariableResolver(policyMock.Object),
                new PolicyIfHolderPermissionVariableResolver(policyMock.Object),
                new PolicyIfReadIndividualsPermissionVariableResolver(policyMock.Object),
                new PolicyIfWriteIndividualsPermissionVariableResolver(policyMock.Object),
                new PolicyIfUpdateIndividualsPermissionVariableResolver(policyMock.Object),
                new PolicyIsHolderRelationshipPermissionVariableResolver(policyMock.Object,
                    entityMock.Object),
                new PolicyIsInsuredRelationshipPermissionVariableResolver(policyMock.Object,
                    entityMock.Object),
                new PolicyIsUnInsuredRelationshipPermissionVariableResolver(policyMock.Object,
                    entityMock.Object),
                new ProposalsCreatorRightsPermissionVariableResolver(caseMock.Object),
                new AllowedReadCasesPermissionVariableResolver(),
                new AllowedReadClaimsPermissionVariableResolver(),
                new AllowedReadGopsPermissionVariableResolver(),
                new AllowedReadPoliciesPermissionVariableResolver(),
                new AllowedReadProposalsPermissionVariableResolver(),
                new AllowedWriteCasesPermissionVariableResolver(),
                new AllowedUpdateCasesPermissionVariableResolver(),
                new AllowedWriteClaimsPermissionVariableResolver(),
                new AllowedUpdateClaimsPermissionVariableResolver(),
                new AllowedWriteGopsPermissionVariableResolver(),
                new AllowedUpdateGopsPermissionVariableResolver(),
                new AllowedWritePoliciesPermissionVariableResolver(),
                new AllowedUpdatePoliciesPermissionVariableResolver(),
                new AllowedWriteProposalsPermissionVariableResolver(),
                new PaymentMethodsCreatorRightsPermissionVariableResolver(transactionMock.Object),
                new TransactionCreatorRightsPermissionVariableResolver(transactionMock.Object),
                new TransactionIfAllowedReadPoliciesPermissionVariableResolver(
                    transactionMock.Object),
                new TransactionIfClaimantPermissionVariableResolver(
                    claimMock.Object, transactionMock.Object),
                new TransactionPolicyIdHolderPermissionVariableResolver(
                    policyMock.Object, transactionMock.Object),
                new TransactionRelationshipPermissionVariableResolver(
                    transactionMock.Object, claimMock.Object, entityMock.Object),
                new GenericLinkedPermissionVariableResolver(entityMock.Object),
                new AgentsRightsPermissionVariableResolver(agentsMock.Object),
                new PolicyIdIfSourceEqualsPermissionVariableResolver(policyClientMock.Object)
            },
            NullLogger<CompositePermissionVariableResolver>.Instance,
            1000);
    }
}
