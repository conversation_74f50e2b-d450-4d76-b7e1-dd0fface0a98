﻿using CoverGo.Auth.Domain.Permission;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain.Permission;

public class UserClaimCollectionTests
{
    [Fact]
    public void GIVEN_empty_collection_WHEN_add_claims_by_string_values_THEN_should_add_claims()
    {
        var sut = new UserClaimCollection();
        var claims = new[]
        {
            new KeyValuePair<string, IEnumerable<string>>("p1", new []{"v1","v2","v3","v1"}),
            new KeyValuePair<string, IEnumerable<string>>("p2", new []{"v4","v5"}),
        };

        sut.AddRange(claims);

        ShouldHaveKeys(sut, new[] { "p1", "p2" });
        ShouldHaveValues(sut, "p1", new[] { "v1", "v2", "v3" });
        ShouldHaveValues(sut, "p2", new[] { "v4", "v5" });
    }

    [Fact]
    public void GIVEN_empty_collection_WHEN_add_claims_THEN_should_add_claims()
    {
        var sut = new UserClaimCollection();
        var claims = new[]
        {
            new Claim("p1", "v1"),
            new Claim("p1", "v2"),
            new Claim("p1", "v3"),
            new Claim("p2", "v4"),
            new Claim("p2", "v5")
        };

        sut.AddRange(claims);

        ShouldHaveKeys(sut, new[] { "p1", "p2" });
        ShouldHaveValues(sut, "p1", new[] { "v1", "v2", "v3" });
        ShouldHaveValues(sut, "p2", new[] { "v4", "v5" });
    }

    [Fact]
    public void GIVEN_claim_type_exists_WHEN_add_single_THEN_adds_single()
    {
        var sut = new UserClaimCollection(
            new[]
            {
                new Claim("p1", "v1"),
                new Claim("p2", "v2"),
            });
        sut.Add(new Claim("p1", "v3"));

        ShouldHaveValues(sut, "p1", new[] { "v1", "v3" });
    }

    [Fact]
    public void GIVEN_claim_type_not_exists_WHEN_add_single_THEN_adds_single()
    {
        var sut = new UserClaimCollection(
            new[]
            {
                new Claim("p1", "v1"),
                new Claim("p2", "v2"),
            });
        sut.Add(new Claim("p3", "v3"));

        ShouldHaveValues(sut, "p3", new[] { "v3" });
    }

    [Fact]
    public void GIVEN_claim_type_and_value_exists_THEN_has_claim_returns_true()
    {
        var sut = new UserClaimCollection(
            new[]
            {
                new Claim("p1", "v1"),
                new Claim("p1", "v2"),
            });
        sut.HasClaim("p1", "v2").Should().BeTrue();
    }

    [Fact]
    public void GIVEN_claim_type_and_value_not_exists_THEN_has_claim_returns_false()
    {
        var sut = new UserClaimCollection(
            new[]
            {
                new Claim("p1", "v1")
            });
        sut.HasClaim("p2", "v2").Should().BeFalse();
        sut.HasClaim("p1", "v3").Should().BeFalse();
    }

    private static void ShouldHaveValues(UserClaimCollection sut, string key, string[] values)
    {
        sut.Where(kv => kv.Key == key)
            .SelectMany(kv => kv.Value)
            .Select(c => c.Value)
            .Should()
            .BeEquivalentTo(values);
    }

    private static void ShouldHaveKeys(UserClaimCollection sut, string[] keys)
    {
        sut.Select(kv => kv.Key)
            .Should()
            .BeEquivalentTo(keys);

        sut.PermissionNames.Should().BeEquivalentTo(keys);
    }
}