﻿using AutoFixture.Xunit2;
using CoverGo.Auth.Domain;
using FluentAssertions;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Bson.Serialization.Serializers;
using Moq;
using Objectivity.AutoFixture.XUnit2.AutoMoq.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain.Tokens
{
    public class CustomPasswordResetTokenProviderTests
    {
        [Theory]
        [AutoMockData]
        public async Task GIVEN_an_user_WHEN_calling_GenerateAsync_THEN_Token_passes_validation_only_if_stored_on_login(
            [Frozen] IDataProtectionProvider dataProtectionProvider,
            [Frozen] IOptions<CustomPasswordResetTokenProviderOptions> options,
            [Frozen] ILoggerFactory loggerFactory,
            [Frozen] IUserStore<MongoLoginDao> store,
            [Frozen] IOptions<IdentityOptions> optionsAccessor,
            [Frozen] IPasswordHasher<MongoLoginDao> passwordHasher,
            [Frozen] ILookupNormalizer keyNormalizer,
            [Frozen] IdentityErrorDescriber errors,
            [Frozen] IServiceProvider services,
            [Frozen] ILogger<UserManager<MongoLoginDao>> logger,
            [Frozen] CustomPasswordResetTokenProviderContext customPasswordResetTokenProviderContext,
            MongoLoginDao user
            )
        {
            AppDao app = new AppDao() 
            {
                EmailConfirmationTokenLifespan = new TimeSpan(1, 0, 0, 0)
            };
            Mock.Get(options)
                .Setup(op => op.Value)
                .Returns(new CustomPasswordResetTokenProviderOptions() {
                    AppNameToAppSettings = new Dictionary<string, AppDao>() 
                    {
                        { "test", app },
                    }
                });
            Mock.Get(dataProtectionProvider.CreateProtector(UserManager<MongoLoginDao>.ResetPasswordTokenPurpose))
                .Setup(p => p.Protect(It.IsAny<byte[]>()))
                .Returns((byte[] input) => input);
            Mock.Get(dataProtectionProvider.CreateProtector(UserManager<MongoLoginDao>.ResetPasswordTokenPurpose))
                .Setup(p => p.Unprotect(It.IsAny<byte[]>()))
                .Returns((byte[] input) => input);
            UserManager<MongoLoginDao> userManager = new UserManager<MongoLoginDao>(store, optionsAccessor, passwordHasher, new IUserValidator<MongoLoginDao>[0], new IPasswordValidator<MongoLoginDao>[0], keyNormalizer, errors, services, logger);
            CustomPasswordResetTokenProvider customPasswordResetTokenProvider = new CustomPasswordResetTokenProvider(dataProtectionProvider, options, loggerFactory, customPasswordResetTokenProviderContext);

            string token = await customPasswordResetTokenProvider.GenerateAsync(UserManager<MongoLoginDao>.ResetPasswordTokenPurpose, userManager, user);
            user.PasswordResetToken = token;

            bool isValidToken = await customPasswordResetTokenProvider.ValidateAsync(UserManager<MongoLoginDao>.ResetPasswordTokenPurpose, token, userManager, user);
            isValidToken.Should().BeTrue();

            user.PasswordResetToken = null;
            isValidToken = await customPasswordResetTokenProvider.ValidateAsync(UserManager<MongoLoginDao>.ResetPasswordTokenPurpose, token, userManager, user);
            isValidToken.Should().BeFalse();
        }
    }
}
