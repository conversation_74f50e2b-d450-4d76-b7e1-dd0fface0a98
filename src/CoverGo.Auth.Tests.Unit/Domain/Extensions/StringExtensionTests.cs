﻿using CoverGo.Auth.Domain.Extensions;
using FluentAssertions;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain.Extensions
{
    public class StringExtensionTests
    {
        [Theory]
        [InlineData("{claimIdIf" , "IsClaimant}", "{claimIdIf{fromLinkSource:advise:{entityId}}IsClaimant}", "{fromLinkSource:advise:{entityId}}")]
        [InlineData("{", "}", "{entityId}", "entityId")]
        public void RemovePrefix_AND_RemoveSuffix_Should_Remove_Prefix_AND_Suffix(
            string prefix, string suffix, string variable, string expectedVariable)
        {
            variable.DropPrefixAndSuffix(prefix, suffix)
                .Should().Be(expectedVariable);
        }
    }
}
