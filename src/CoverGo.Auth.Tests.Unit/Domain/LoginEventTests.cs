﻿using CoverGo.Auth.Domain;
using FluentAssertions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System.Collections.Generic;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain
{
    public class LoginEventTests
    {
        private JsonSerializer _jsonSerializer;

        public LoginEventTests()
        {
            //This is same settings used when serializing events
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                TypeNameHandling = TypeNameHandling.Auto,
                NullValueHandling = NullValueHandling.Ignore
            };
            settings.Converters.Add(new StringEnumConverter());

            _jsonSerializer = JsonSerializer.Create(settings);
        }

        [Fact]

        public void GIVEN_create_login_command_WHEN_serializing_event_to_save_THEN_serialized_event_does_not_contain_password()
        {
            var command = new CreateLoginCommand
            {
                ClientId = "admin",
                Username = "test",
                Password = "testPass",
                IgnorePasswordValidation = true,
                IsEmailConfirmed = true,
                AppIdsToBeGrantedAccessTo = new List<string> { "admin" }
            };
            var eventValues = JObject.FromObject(command, _jsonSerializer);

            string serializedPassword = eventValues["password"]?.ToString();
            serializedPassword.Should().BeNullOrEmpty();
        }

        [Fact]

        public void GIVEN_change_password_command_WHEN_serializing_event_to_save_THEN_serialized_event_does_not_contain_passwords()
        {
            var command = new ChangePasswordCommand
            {
                CurrentPassword = "pass1",
                NewPassword = "pass2"
            };
            var eventValues = JObject.FromObject(command, _jsonSerializer);

            string serializedCurrentPassword = eventValues["currentPassword"]?.ToString();
            serializedCurrentPassword.Should().BeNullOrEmpty();
            string serializedNewPassword = eventValues["newPassword"]?.ToString();
            serializedNewPassword.Should().BeNullOrEmpty();
        }

        [Fact]

        public void GIVEN_reset_password_command_WHEN_serializing_event_to_save_THEN_serialized_event_does_not_contain_password()
        {
            var command = new ResetPasswordCommand
            {
                Code = "randomCode",
                Password = "password"
            };
            var eventValues = JObject.FromObject(command, _jsonSerializer);

            string serializedPassword = eventValues["password"]?.ToString();
            serializedPassword.Should().BeNullOrEmpty();
        }
    }
}
