﻿using System;
using System.Security.Cryptography;
using System.Text;
using CoverGo.Auth.Domain.Otp;
using FluentAssertions;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain.Otp
{
    public class OtpHelpersTests
    {
        [Fact]
        public void TestRandomSixDigitsString()
        {
            string code = OtpHelpers.RandomSixDigitsString();

            code.Length.Should().Be(6);
            int.TryParse(code, out _).Should().BeTrue();
        }

        [Fact]
        public void TestBuildTokenHashInputMsg()
        {
            var token = new OtpLoginToken
            {
                ClientId = Guid.NewGuid().ToString(),
                Username = Guid.NewGuid().ToString(),
                Password = Guid.NewGuid().ToString(),
                Email = Guid.NewGuid().ToString(),
                PhoneNumber = Guid.NewGuid().ToString(),
                Timestamp = Guid.NewGuid().ToString(),
                Hash = Guid.NewGuid().ToString()
            };
            var otp = OtpHelpers.RandomSixDigitsString();

            string hashInput = OtpHelpers.BuildTokenHashInputMsg(token, otp);

            string expected = token.ClientId + token.Username + token.Password + token.Email + token.PhoneNumber + token.Timestamp + otp;
            hashInput.Should().Be(expected);
        }

        [Theory]
        [InlineData(0.1, false)]
        [InlineData(-0.1, true)]
        [InlineData(-2.50, true)]
        [InlineData(-4.99, true)]
        [InlineData(-15.01, false)]
        [InlineData(-30.0, false)]
        public void TestIsTokenValid(double minuteOffset, bool expectedResult)
        {
            string timestamp = ((DateTimeOffset)DateTime.UtcNow.AddMinutes(minuteOffset)).ToUnixTimeMilliseconds().ToString();
            var token = new OtpLoginToken { Timestamp = timestamp };
            bool isTokenExpired = OtpHelpers.IsTokenTimestampValid(token);

            isTokenExpired.Should().Be(expectedResult);
        }

        [Theory]
        [InlineData("key", "message", "bp7ym3X//Ft6uuUn1Y/a2y/kLnIZARl2kXNDBl9Y7Uo=")]
        [InlineData("TTaMklQS", "HXDFBdR", "CHSvJmUcyQ1qn6OTq/PYvN3+mMVQXKPj2y5JOv/QYeY=")]
        public void TestComputeHmac(string key, string message, string expectedHash)
        {
            HMAC hmacSha256 = new HMACSHA256(Encoding.UTF8.GetBytes(key));
            string hash = OtpHelpers.ComputeHmac(message, hmacSha256);

            hash.Length.Should().Be(44);
            hash.Should().Be(expectedHash);
        }

        [Theory]
        [InlineData("key", "message", "bp7ym3X//Ft6uuUn1Y/a2y/kLnIZARl2kXNDBl9Y7Uo=", true)]
        [InlineData("TTaMklQS", "HXDFBdR", "CHSvJmUcyQ1qn6OTq/PYvN3+mMVQXKPj2y5JOv/QYeY=", true)]
        [InlineData("key", "messag", "bp7ym3X//Ft6uuUn1Y/a2y/kLnIZARl2kXNDBl9Y7Uo=", false)]
        [InlineData("ke", "message", "bp7ym3X//Ft6uuUn1Y/a2y/kLnIZARl2kXNDBl9Y7Uo=", false)]
        [InlineData("key", "message", "CHSvJmUcyQ1qn6OTq/PYvN3+mMVQXKPj2y5JOv/QYeY=", false)]
        public void TestVerifyHmac(string key, string message, string expectedHash, bool expectedValidity)
        {
            HMAC hmacSha256 = new HMACSHA256(Encoding.UTF8.GetBytes(key));
            bool isValid = OtpHelpers.VerifyHmac(message, expectedHash, hmacSha256);

            isValid.Should().Be(expectedValidity);
        }


        [Theory]
        [InlineData("plainText", "G2skybakXUBfuxAL+Ze2SA==")]
        [InlineData("Covergo is great", "Fok96GmuLJgzcbNcBOCCxmvSgUxch/hpGbGj/Xhb6SQ=")]
        public void TestEncrypt(string plainText, string expectedCipherText)
        {
            Aes aesCipher = OtpHelpers.CreateAesCipher("zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=", "94jCf53NO1acZ3pO7UE+gA==");
            byte[] encryptedBytes = OtpHelpers.Encrypt(plainText, aesCipher);

            string cipherText = Convert.ToBase64String(encryptedBytes);
            cipherText.Should().Be(expectedCipherText);
        }


        [Theory]
        [InlineData("G2skybakXUBfuxAL+Ze2SA==", "plainText")]
        [InlineData("Fok96GmuLJgzcbNcBOCCxmvSgUxch/hpGbGj/Xhb6SQ=", "Covergo is great")]
        public void TestDecrypt(string cipherText, string expectedPlainText)
        {
            Aes aesCipher = OtpHelpers.CreateAesCipher("zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=", "94jCf53NO1acZ3pO7UE+gA==");
            byte[] cipherTextBytes = Convert.FromBase64String(cipherText);

            string plainText = OtpHelpers.Decrypt(cipherTextBytes, aesCipher);

            plainText.Should().Be(expectedPlainText);
        }
    }
}
