﻿using System;
using System.Collections.Generic;
using System.Threading;
using CoverGo.Auth.Domain.Otp;
using CoverGo.Auth.Infrastructure.Adapters.Mongo.Otp;
using Moq;

namespace CoverGo.Auth.Tests.Unit.Domain.Otp
{
    public class MockMongoDbOtpRepo : Mock<IOtpRepository>
    {
        public void MockGetOtpNotificationsAsync(int count, bool hasCooledDown)
        {
            DateTime currentTime = hasCooledDown ? 
                DateTime.UtcNow.AddMinutes(-2 * OtpConstants.DefaultCoolDownDurationInMinute) : 
                DateTime.UtcNow ;
            
            var otps = new List<OtpDao>();
            for (int i = 0; i < count; i++)
            {
                var otpDao = new OtpDao
                {
                    Email = Guid.NewGuid().ToString(),
                    PhoneNumber = Guid.NewGuid().ToString(),
                    CreatedAt = currentTime
                };
                otps.Add(otpDao);
            }
            
            Setup(r => r.GetOtpNotificationsAsync(
                It.IsAny<string>(),
                It.IsAny<OtpWhere>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(()=> otps);
        }

        public void MockGetOtpNotificationsAsync(OtpStatus status)
        {
            var otps = new List<OtpDao> {
                new OtpDao
                {
                    Status = status
                }
            };

            Setup(r => r.GetOtpNotificationsAsync(
                It.IsAny<string>(),
                It.IsAny<OtpWhere>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(() => otps);
        }
    }
}
