﻿using System.Collections.Generic;
using System.Threading;
using CoverGo.Auth.Domain.OtherServices;
using Moq;

namespace CoverGo.Auth.Tests.Unit.Domain.Otp
{
    public class MockNotificationService : Mock<INotificationService>
    {
        List<string> ChannelUsed = new();

        public void MockSendAsync()
        {
            Setup(x => x.SendAsync(
                It.IsAny<string>(),
                It.IsAny<SendNotificationCommand>(),
                It.IsAny<CancellationToken>()))
                .Callback<string, SendNotificationCommand, CancellationToken>((t, c, _) => AddChannelUsed(c));
        }

        void AddChannelUsed(SendNotificationCommand c)
        {
            if (c.EmailMessage != null)
            {
                ChannelUsed.Add("email");
            }
            
            if (c.SmsMessage != null)
            {
                ChannelUsed.Add("sms");
            }
        }

        public string[] GetChannelsUsed() => ChannelUsed.ToArray();
    }
}
