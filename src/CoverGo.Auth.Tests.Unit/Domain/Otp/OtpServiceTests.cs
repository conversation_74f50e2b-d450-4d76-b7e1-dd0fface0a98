﻿using System;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using CoverGo.Auth.Domain.Otp;
using CoverGo.Auth.Domain.Otp.Commands;
using CoverGo.Auth.Domain.Otp.Responses;
using CoverGo.Auth.Infrastructure.Adapters.Mongo.Otp;
using CoverGo.DomainUtils;
using FluentAssertions;
using Newtonsoft.Json;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Domain.Otp
{
    public class OtpServiceTests
    {
        readonly string aesTestKey = "zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=";
        readonly string aesTestIv = "94jCf53NO1acZ3pO7UE+gA==";
        readonly string hashSecret = "secret";
        readonly string clientId = "covergo_crm";

        readonly Aes _aesCipher;
        readonly HMAC _hasher;
        readonly MockNotificationService _mockNotificationService;

        public OtpServiceTests()
        {
            _aesCipher = OtpHelpers.CreateAesCipher(aesTestKey, aesTestIv);
            _hasher = new HMACSHA256(Encoding.UTF8.GetBytes(hashSecret));

            _mockNotificationService = new MockNotificationService();
            _mockNotificationService.MockSendAsync();
        }

        [Theory]
        [InlineData("", "", "valid email or phone number is required")]
        [InlineData("", " " , "valid email or phone number is required")]
        [InlineData("", null, "valid email or phone number is required")]
        [InlineData(" ", "", "valid email or phone number is required")]
        [InlineData(" ", " ", "valid email or phone number is required")]
        [InlineData(" ", null, "valid email or phone number is required")]
        [InlineData(null, "", "valid email or phone number is required")]
        [InlineData(null, " ", "valid email or phone number is required")]
        [InlineData(null, null, "valid email or phone number is required")]
        public void GIVEN_invalid_createPreOtpLoginTokenCommand_WHEN_createPreOtpLoginToken_THEN_return_failure_result(
            string email, string phoneNumber, string expectedErrMsg)
        {
            OtpService otpService = CreateOtpService();
            
            var command = new CreatePreOtpLoginCommand
            {
                ClientId = clientId,
                Username = "username",
                Password = "password",
                Email = email,
                PhoneNumber = phoneNumber,
            };
            Result<PreOtpLogin> result = otpService.CreatePreOtpLogin("test_uat", command);

            result.Status.Should().Be("failure");
            result.Errors.Should().Contain(expectedErrMsg);
            result.Value?.Token.Should().BeNull();
        }

        [Theory]
        [InlineData("<EMAIL>", "")]
        [InlineData("<EMAIL>", " ")]
        [InlineData("<EMAIL>", null)]
        [InlineData("", "+85212345678")]
        [InlineData(" ", "+85212345678")]
        [InlineData(null, "+85212345678")]
        public void GIVEN_valid_createPreOtpLoginTokenCommand_WHEN_createPreOtpLoginToken_THEN_return_success_result(string email, string phoneNumber)
        {
            OtpService otpService = CreateOtpService();
            
            var command = new CreatePreOtpLoginCommand
            {
                ClientId = clientId,
                Username = "username",
                Password = "password",
                Email = email,
                PhoneNumber = phoneNumber,
            };
            Result<PreOtpLogin> result = otpService.CreatePreOtpLogin("test_uat", command);

            result.Status.Should().Be("success");
            result.Errors.Should().BeNull();
            result.Value.Token.Should().NotBeNullOrWhiteSpace();
        }

        [Theory]
        [InlineData("<EMAIL>", "+85212345678", "invalid token")]
        public async Task GIVEN_invalid_preOtpLoginToken_WHEN_request_otp_THEN_return_failure_result(string email, string phoneNumber, string expectedErrMsg)
        {
            OtpService otpService = CreateOtpService();
            
            string testPreOtpToken = GenerateMockToken(_aesCipher, CreateRandomHasher(),
                0, clientId, "username", "password", email, phoneNumber);

            CreateOtpLoginCommand command = new CreateOtpLoginCommand
            {
               Token = testPreOtpToken
            };
            Result<OtpLogin> result = await otpService.CreateOtpLoginAsync("test_uat", command, default);

            result.Status.Should().Be("failure");
            result.Errors.Should().Contain(expectedErrMsg);
            result.Value?.Token.Should().BeNull();
        }
        
        [Fact]
        public async Task GIVEN_invalid_optLoginCommand_WHEN_request_otp_THEN_return_failure_result()
        {
            OtpService otpService = CreateOtpService();
            
            CreatePreOtpLoginCommand preOtpLoginCommand = new CreatePreOtpLoginCommand
            {
                ClientId = clientId,
                Username = "username",
                Password = "password",
                Email = "<EMAIL>",
                PhoneNumber = "+85299912345",
            };
            Result<PreOtpLogin> preOtpResult = otpService.CreatePreOtpLogin("test_uat", preOtpLoginCommand);

            CreateOtpLoginCommand command = new CreateOtpLoginCommand
            {
                Token = preOtpResult.Value.Token
            };
            Result<OtpLogin> result = await otpService.CreateOtpLoginAsync("test_uat", command, default);

            result.Status.Should().Be("failure");
            result.Errors.Should().Contain("unable to send OTP");
            result.Value?.Token.Should().BeNull();
        }

        [Theory]
        [InlineData("emailId", "smsId", new []{"email", "sms"})]
        [InlineData("emailId", "", new []{"email"})]
        [InlineData("emailId", " ", new []{"email"})]
        [InlineData("emailId", null, new []{"email"})]
        [InlineData("", "smsId", new []{"sms"})]
        [InlineData(" ", "smsId", new []{"sms"})]
        [InlineData(null, "smsId", new []{"sms"})]
        public async Task GIVEN_valid_preOtpLoginToken_WHEN_request_otp_THEN_return_success_result(
            string emailTemplateId, string smsTemplateId, string[] channelUsed)
        {
            OtpService otpService = CreateOtpService();
            
            var preOtpLoginCommand = new CreatePreOtpLoginCommand
            {
                ClientId = clientId,
                Username = "username",
                Password = "password",
                Email = "<EMAIL>",
                PhoneNumber = "+85299912345",
            };
            Result<PreOtpLogin> preOtpResult = otpService.CreatePreOtpLogin("test_uat", preOtpLoginCommand);

            var createOtpLoginCommand = new CreateOtpLoginCommand
            {
                Token = preOtpResult.Value.Token,
                EmailTemplateId = emailTemplateId,
                SmsTemplateId = smsTemplateId
            };
            Result<OtpLogin> result = await otpService.CreateOtpLoginAsync("test_uat", createOtpLoginCommand, default);

            result.Status.Should().Be("success");
            result.Errors.Should().BeNull();
            result.Value.Token.Should().NotBeNullOrWhiteSpace();
            _mockNotificationService.GetChannelsUsed().Should().Equal(channelUsed);
        }

        [Theory]
        [InlineData(-15.01, "123456", "123456", "invalid timestamp/token expired")]
        [InlineData(-16.0, "123456", "123456", "invalid timestamp/token expired")]
        [InlineData(-1, "123456", "012345", "invalid token/otp")]
        public async Task GIVEN_invalid_otpLoginToken_WHEN_get_access_token_from_otp_THEN_return_failure_result(double offsetTime, string otp, string inputOtp, string expectedErrMsg)
        {
            OtpService otpService = CreateOtpService();
            
            string testotpToken = GenerateMockToken(_aesCipher, _hasher, offsetTime, "username", "password", "<EMAIL>", "", otp);

            var command = new CreateAccessTokenFromOtpLoginCommand { Token = testotpToken, OneTimePassword = inputOtp };
            Result<Token> result = await otpService.CreateAccessTokenFromOtpLoginAsync("test_uat", command, default);

            result.Status.Should().Be("failure");
            result.Errors.Should().Contain(expectedErrMsg);
        }

        [Theory]
        [InlineData(-0.1, "<EMAIL>", "+85212345678")]
        [InlineData(-0.1, "<EMAIL>", "+85299912345")]
        [InlineData(-2.5, "<EMAIL>", "+85212345678")]
        [InlineData(-4.9, "<EMAIL>", "+85212345678")]
        public async Task GIVEN_valid_otpLoginToken_WHEN_get_access_token_from_otp_THEN_success(double offsetTime, string email, string phoneNumber)
        {
            Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "UnitTest");
            OtpService otpService = CreateOtpService();
            
            string testOtp = "123456";
            string testotpToken = GenerateMockToken(_aesCipher, _hasher, offsetTime,
                clientId, "username", "password", email, phoneNumber, testOtp);

            var command = new CreateAccessTokenFromOtpLoginCommand { Token = testotpToken, OneTimePassword = testOtp };
            Result<Token> result = await otpService.CreateAccessTokenFromOtpLoginAsync("test_uat", command, default);

            result.Status.Should().Be("success");
            result.Errors.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_valid_otpLogin_flow_WHEN_get_access_token_from_otp_THEN_success()
        {
            OtpService otpService = CreateOtpService();
            
            Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "UnitTest");
            Result<PreOtpLogin> preOtpResult = CreatePreOtpLogin(otpService);
            Result<OtpLogin> otpResult = await CreateOptLogin(otpService, preOtpResult);

            CreateAccessTokenFromOtpLoginCommand command = new CreateAccessTokenFromOtpLoginCommand
            {
                Token = otpResult.Value.Token,
                OneTimePassword = "123456"
            };

            Result<Token> result = await otpService.CreateAccessTokenFromOtpLoginAsync("test_uat", command, default);
            result.Status.Should().Be("success");
            result.Errors.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_valid_otpLogin_but_used_WHEN_get_access_token_from_otp_THEN_failed()
        {
            var mockOtpRepo = new MockMongoDbOtpRepo();
            mockOtpRepo.MockGetOtpNotificationsAsync(OtpStatus.Used);

            OtpService otpService = CreateOtpService(mockOtpRepo);

            Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "UnitTest");
            Result<PreOtpLogin> preOtpResult = CreatePreOtpLogin(otpService);
            Result<OtpLogin> otpResult = await CreateOptLogin(otpService, preOtpResult);

            CreateAccessTokenFromOtpLoginCommand command = new CreateAccessTokenFromOtpLoginCommand
            {
                Token = otpResult.Value.Token,
                OneTimePassword = "123456"
            };

            Result<Token> result = await otpService.CreateAccessTokenFromOtpLoginAsync("test_uat", command, default);
            result.Status.Should().Be("failure");
            result.Errors.Should().Equal("invalid or used token/otp");
        }

        [Theory]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        public async Task GIVEN_cooled_down_request_WHEN_createOtpLogin_from_otp_THEN_success(int count)
        {
            var mockOtpRepo = new MockMongoDbOtpRepo();
            mockOtpRepo.MockGetOtpNotificationsAsync(count, true);
            OtpService otpService = CreateOtpService(mockOtpRepo);
            
            Result<PreOtpLogin> preOtpResult = CreatePreOtpLogin(otpService);
            Result<OtpLogin> result = await CreateOptLogin(otpService, preOtpResult);
            
            result.Status.Should().Be("success");
            result.Errors.Should().BeNull();
        }

        [Theory]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        public async Task GIVEN_too_frequent_request_WHEN_createOtpLogin_from_otp_THEN_fail(int count)
        {
            var mockOtpRepo = new MockMongoDbOtpRepo();
            mockOtpRepo.MockGetOtpNotificationsAsync(count, false);
            OtpService otpService = CreateOtpService(mockOtpRepo);
            
            Result<PreOtpLogin> preOtpResult = CreatePreOtpLogin(otpService);
            Result<OtpLogin> result = await CreateOptLogin(otpService, preOtpResult);
            
            result.Status.Should().Be("failure");
            result.Errors.Should().Contain("you are requesting too often, please try again later");
        }
        
        [Theory]
        [InlineData(5)]
        [InlineData(6)]
        [InlineData(7)]
        [InlineData(8)]
        [InlineData(9)]
        [InlineData(10)]
        public async Task GIVEN_exceed_rate_limit_WHEN_createOtpLogin_from_otp_THEN_fail(int count)
        {
            var mockOtpRepo = new MockMongoDbOtpRepo();
            mockOtpRepo.MockGetOtpNotificationsAsync(count, true);
            OtpService otpService = CreateOtpService(mockOtpRepo);
            
            Result<PreOtpLogin> preOtpResult = CreatePreOtpLogin(otpService);
            Result<OtpLogin> result = await CreateOptLogin(otpService, preOtpResult);
            
            result.Status.Should().Be("failure");
            result.Errors.Should().Contain("you are requesting too often, please try again later");
        }
       
        [Fact]
        public async Task GIVEN_email_not_verified_WHEN_createOtpLogin_THEN_fail()
        {
            // Create mock dependencies
            var mockAuthService = new MockAuthService();
            mockAuthService.MockGetAppsAsync();
            mockAuthService.MockGetLoginsEmptyAsync();

            var mockOtpRepo = new MockMongoDbOtpRepo();
            mockOtpRepo.MockGetOtpNotificationsAsync(1, true);

            var otpService = new OtpService(_aesCipher, _hasher, mockAuthService.Object, 
                _mockNotificationService.Object, mockOtpRepo.Object, null);
            
            //Actual test
            Result<PreOtpLogin> preOtpResult = CreatePreOtpLogin(otpService);
            Result<OtpLogin> result = await CreateOptLogin(otpService, preOtpResult);
            
            result.Status.Should().Be("failure");
            result.Errors.Should().Contain("email not confirmed");
        }

        private Result<PreOtpLogin> CreatePreOtpLogin(IOtpService otpService)
        {
            var preOtpLoginCommand = new CreatePreOtpLoginCommand
            {
                ClientId = clientId,
                Username = "username",
                Password = "password",
                Email = "<EMAIL>",
                PhoneNumber = "12345678",
            };
            
            return otpService.CreatePreOtpLogin("test_uat", preOtpLoginCommand);
        }

        private async Task<Result<OtpLogin>> CreateOptLogin(IOtpService otpService, Result<PreOtpLogin> preOtpResult)
        {
            var createOtpLoginCommand = new CreateOtpLoginCommand
            {
                Token = preOtpResult.Value.Token,
                EmailTemplateId = "emailId",
                SmsTemplateId = "smsId"
            };
            
            return await otpService.CreateOtpLoginAsync("test_uat", createOtpLoginCommand, default);
        }

        private OtpService CreateOtpService()
        {
            var mockAuthService = new MockAuthService();
            mockAuthService.MockGetAppsAsync();
            mockAuthService.MockGetLoginsSuccessAsync();

            var mockOtpRepo = new MockMongoDbOtpRepo();
            mockOtpRepo.MockGetOtpNotificationsAsync(1, true);

            return new OtpService(_aesCipher, _hasher, mockAuthService.Object, _mockNotificationService.Object, mockOtpRepo.Object, null);
        }
        
        private OtpService CreateOtpService(MockMongoDbOtpRepo repo)
        {
            var mockAuthService = new MockAuthService();
            mockAuthService.MockGetAppsAsync();
            mockAuthService.MockGetLoginsSuccessAsync();

            return new OtpService(_aesCipher, _hasher, mockAuthService.Object, _mockNotificationService.Object, repo.Object, null);
        }

        private static string GenerateMockToken(Aes aesCipher, HMAC hasher, double minuteOffset,
            string clientId, string username, string password, string email, string phoneNumber, string otp = "")
        {
            string offsettedTime = ((DateTimeOffset)DateTime.UtcNow.AddMinutes(minuteOffset)).ToUnixTimeMilliseconds().ToString();
            var tokenObj = new OtpLoginToken
            {
                ClientId = clientId,
                Username = username,
                Password = password,
                Email = email,
                PhoneNumber = phoneNumber,
                Timestamp = offsettedTime
            };

            string hashInputMsg = OtpHelpers.BuildTokenHashInputMsg(tokenObj, otp);
            string hash = OtpHelpers.ComputeHmac(hashInputMsg, hasher);
            tokenObj.Hash = hash;

            string plainToken = JsonConvert.SerializeObject(tokenObj);

            return Convert.ToBase64String(OtpHelpers.Encrypt(plainToken, aesCipher));
        }

        private static HMAC CreateRandomHasher()
            => new HMACSHA256(Encoding.UTF8.GetBytes(OtpHelpers.RandomSixDigitsString()));
    }
}
