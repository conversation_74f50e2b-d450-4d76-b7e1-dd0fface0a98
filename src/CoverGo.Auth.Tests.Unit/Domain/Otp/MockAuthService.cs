﻿using System.Collections.Generic;
using System.Threading;
using CoverGo.Auth.Domain;
using CoverGo.DomainUtils;
using Moq;
using StackExchange.Redis;

namespace CoverGo.Auth.Tests.Unit.Domain.Otp
{
    public class MockAuthService : Mock<IAuthService>
    {
        public void MockGetAppsAsync()
        {
            Setup(x => x.GetAppsAsync(
                It.IsAny<string>(),
                It.IsAny<AppWhere>(),
                It.IsAny<OrderBy>(),
                It.IsAny<int?>(),
                It.IsAny<int?>(),
                It.IsAny<CancellationToken>())).ReturnsAsync(() => new List<App>{ new App { AppName = "testApp" }});
        }

        public void MockGetLoginsSuccessAsync()
        {
            Setup(x => x.GetLoginsAsync(
                It.IsAny<string>(),
                It.IsAny<LoginWhere>(),
                null,
                null,
                null,
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(() => new List<MongoLoginDao> { new MongoLoginDao  { Id = "hello" } });
        }
        
        public void MockGetLoginsEmptyAsync()
        {
            Setup(x => x.GetLoginsAsync(
                    It.IsAny<string>(),
                    It.IsAny<LoginWhere>(),
                    null,
                    null,
                    null,
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(() => new List<MongoLoginDao>());
        }
    }
}
