using AutoFixture;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Application.Controllers;
using CoverGo.Auth.Application.GraphQl.SSO;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.Proxies.Gateway;
using FluentAssertions;
using IdentityServer4;
using IdentityServer4.Services;
using IdentityServer4.Stores;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.MongoDB;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Moq;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using static CoverGo.Auth.Application.Controllers.AuthController;

namespace CoverGo.Auth.Tests.Unit.Application;

public sealed class AuthControllerTests
{
    private readonly IFixture _fixture = new Fixture();

    [Theory]
    [InlineData(true, "https://valid-redirect-host.com", "https://client-url.com", "https://fake-redirect-host.com", "https://client-url.com")]
    [InlineData(true, "https://valid-redirect-host.com", null, "https://fake-redirect-host.com", "https://valid-redirect-host.com")]
    [InlineData(false, "https://valid-redirect-host.com", "https://client-url.com", "https://fake-redirect-host.com", "https://fake-redirect-host.com")]
    public async Task GIVEN_Valid_User_And_NewForgotPasswordFlow_Feature_Flag_WHEN_ForgotPassword_With_Fake_Link_Host_THEN_Email_Link_Host_Is_Expected(
        bool isFeatureEnabled, string validRedirectUri, string clientUri, string fakeRedirectUri, string expectedRedirectUri)
    {
        string tenantId = _fixture.Create<string>();
        string interceptedRedirectUri = null;
        DbConfig tenantDbConfig = _fixture.Build<DbConfig>().Create();
        DbConfig.AddConfig(tenantId, tenantDbConfig);
        SendNotificationCommand sendNotificationCommand = _fixture.Build<SendNotificationCommand>()
            .Without(c => c.ScheduleToSendAt)
            .Create();
        ForgotPasswordCommand apiRequestBody = _fixture.Build<ForgotPasswordCommand>()
            .Without(c => c.Username)
            .With(c => c.SendNotificationCommand, sendNotificationCommand)
            .Create();
        apiRequestBody.SendNotificationCommand.EmailMessage.TemplateRendering.Input.Content = JToken.Parse($"{{\"link\":\"{fakeRedirectUri}\"}}");
        MongoLoginDao mongoUserDao = _fixture.Build<MongoLoginDao>()
            .With(login => login.Claims, new List<IdentityUserClaim>
            {
                new() {
                    Type = "clientId",
                    Value = apiRequestBody.ClientId
                }
            })
            .Create();
        App app = _fixture.Build<App>()
            .With(a => a.RedirectUris, new List<string>
            {
                $"{validRedirectUri}/new-password",
                $"{validRedirectUri}/forgot-password",
            })
            .Without(a => a.Claims)
            .With(a => a.ClientUri, clientUri)
            .Create();

        Mock<UserManager<MongoLoginDao>> mongoUserManager = new(
            new Mock<IUserStore<MongoLoginDao>>().Object,
            new Mock<IOptions<IdentityOptions>>().Object,
            new Mock<IPasswordHasher<MongoLoginDao>>().Object,
            new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
            new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
            new Mock<ILookupNormalizer>().Object,
            new Mock<IdentityErrorDescriber>().Object,
            new Mock<IServiceProvider>().Object,
            NullLogger<UserManager<MongoLoginDao>>.Instance
        );
        Mock<IRepository> authRepository = new();
        Mock<ITenantRepository> tenantRepository = new();
        Mock<ISendNotificationScheduleRepository> sendNotificationScheduleRepository = new();

        AuthService authService = new(
            new IRepository[] { authRepository.Object },
            new IEventStore[] { new Mock<IEventStore>().Object },
            new ITenantRepository[] { tenantRepository.Object },
            sendNotificationScheduleRepository.Object
        );

        Mock<MongoLoginRepository> mockMongoLoginRepository = new();
        Mock<MongoEventStore> mockMongoEventStore = new();
        PermissionValidator permissionValidator = new(new Mock<Proxies.Auth.IAuthService>().Object);
        Mock<ISchedulerService> mockSchedulerService = new();
        LoginMutation loginMutation = new(
            mockMongoLoginRepository.Object,
            mockMongoEventStore.Object,
            permissionValidator,
            mockSchedulerService.Object,
            authService
        );

        Mock<IMultiTenantFeatureManager> featureManager = new();
        Mock<INotificationService> notificationService = new();
        IdentityServerTools identityServerTools = new(
            new Mock<IHttpContextAccessor>().Object,
            new Mock<ITokenCreationService>().Object,
            new Mock<ISystemClock>().Object
        );
        Mock<HttpContext> httpContext = new();
        ControllerContext controllerContext = new() { HttpContext = httpContext.Object };

        mongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
            .ReturnsAsync(mongoUserDao);
        mongoUserManager.Setup(m => m.GeneratePasswordResetTokenAsync(It.IsAny<MongoLoginDao>()))
            .ReturnsAsync(_fixture.Create<string>());
        mongoUserManager.Setup(m => m.UpdateAsync(It.IsAny<MongoLoginDao>()))
            .ReturnsAsync(IdentityResult.Success);
        authRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        authRepository.Setup(x => x.GetMongoUserManager())
            .Returns(mongoUserManager.Object);
        authRepository.Setup(x => x.GetPasswordValidatorsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.Build<PasswordValidators>()
                .Without(validator => validator.PasswordResetRequireDobVerification)
                .Create());
        tenantRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        tenantRepository.Setup(t => t.GetAppsAsync(tenantId, It.IsAny<AppWhere>(), It.IsAny<OrderBy>(), It.IsAny<int?>(), It.IsAny<int?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<App> { app });
        notificationService.Setup(x => x.SendAsync(It.IsAny<string>(), It.IsAny<SendNotificationCommand>(), default))
            .Callback((string tenantId, SendNotificationCommand command, CancellationToken cancellationToken) =>
            {
                interceptedRedirectUri = command.EmailMessage.TemplateRendering.Input.Content["link"].ToString();
            })
            .ReturnsAsync(Result.Success());
        httpContext.SetupGet(x => x.Request.PathBase).Returns($"/{tenantId}");
        featureManager.Setup(f => f.IsEnabled("NewForgotPasswordFlow", tenantId))
            .ReturnsAsync(isFeatureEnabled);

        AuthController sut = new(
            identityServerTools,
            notificationService.Object,
            new Mock<IEntityService>().Object,
            new Mock<IResourceStore>().Object,
            NullLogger<AuthController>.Instance,
            new Mock<IEventStore>().Object,
            authService,
            new Mock<IPermissionService>().Object,
            new Mock<IFeatureManager>().Object,
            featureManager.Object,
            new Mock<CustomPasswordResetTokenProviderContext>().Object,
            new Mock<IGatewayService>().Object,
            loginMutation
        )
        {
            ControllerContext = controllerContext
        };

        // Act
        await sut.ForgotPasswordAsync(apiRequestBody, default);

        // Assert
        interceptedRedirectUri.Should().Be(expectedRedirectUri);
    }

    [Fact]
    public async Task GIVEN_Valid_User_And_ResetPassword_WITH_DateOfBithValidation_PasswordValidator_WHEN_Invalid_Attempts_Reached_THEN_User_Account_is_Locked()
    {
        // Arrange
        string tenantId = _fixture.Create<string>();
        string loginId = _fixture.Create<string>();
        DbConfig tenantDbConfig = _fixture.Build<DbConfig>().Create();
        DbConfig.AddConfig(tenantId, tenantDbConfig);

        ResetPasswordCommand apiRequestBody = _fixture.Build<ResetPasswordCommand>()
           .Create();

        MongoLoginDao mongoUserDao = _fixture.Build<MongoLoginDao>()
           .With(login => login.AccessFailedCount, 3)
           .Create();

        Mock<UserManager<MongoLoginDao>> mongoUserManager = new(
            new Mock<IUserStore<MongoLoginDao>>().Object,
            new Mock<IOptions<IdentityOptions>>().Object,
            new Mock<IPasswordHasher<MongoLoginDao>>().Object,
            new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
            new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
            new Mock<ILookupNormalizer>().Object,
            new Mock<IdentityErrorDescriber>().Object,
            new Mock<IServiceProvider>().Object,
            NullLogger<UserManager<MongoLoginDao>>.Instance
        );

        Mock<IRepository> authRepository = new();
        Mock<ITenantRepository> tenantRepository = new();
        Mock<ISendNotificationScheduleRepository> sendNotificationScheduleRepository = new();
        AuthService authService = new(
            new IRepository[] { authRepository.Object },
            new IEventStore[] { new Mock<IEventStore>().Object },
            new ITenantRepository[] { tenantRepository.Object },
            sendNotificationScheduleRepository.Object
        );

        Mock<MongoLoginRepository> mockMongoLoginRepository = new();
        Mock<MongoEventStore> mockMongoEventStore = new();
        PermissionValidator permissionValidator = new(new Mock<Proxies.Auth.IAuthService>().Object);
        Mock<ISchedulerService> mockSchedulerService = new();
        LoginMutation loginMutation = new(
            mockMongoLoginRepository.Object,
            mockMongoEventStore.Object,
            permissionValidator,
            mockSchedulerService.Object,
            authService
        );

        Mock<IMultiTenantFeatureManager> featureManager = new();
        Mock<INotificationService> notificationService = new();
        IdentityServerTools identityServerTools = new(
            new Mock<IHttpContextAccessor>().Object,
            new Mock<ITokenCreationService>().Object,
            new Mock<ISystemClock>().Object
        );
        Mock<HttpContext> httpContext = new();
        ControllerContext controllerContext = new() { HttpContext = httpContext.Object };


        mongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
            .ReturnsAsync(mongoUserDao);
        authRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        authRepository.Setup(x => x.GetMongoUserManager())
            .Returns(mongoUserManager.Object);
        authRepository.Setup(x => x.GetPasswordValidatorsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.Build<PasswordValidators>()
                .With(validator => validator.PasswordResetRequireDobVerification,true)
                .With(validator => validator.MaxDobVerificationAttempts, 3)
                .Create());
        authRepository.Setup(x => x.FindLoginByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoUserDao);
        httpContext.SetupGet(x => x.Request.PathBase).Returns($"/{tenantId}");

        AuthController sut = new(
            identityServerTools,
            notificationService.Object,
            new Mock<IEntityService>().Object,
            new Mock<IResourceStore>().Object,
            NullLogger<AuthController>.Instance,
            new Mock<IEventStore>().Object,
            authService,
            new Mock<IPermissionService>().Object,
            new Mock<IFeatureManager>().Object,
            featureManager.Object,
            new Mock<CustomPasswordResetTokenProviderContext>().Object,
            new Mock<IGatewayService>().Object,
            loginMutation
        )
        {
            ControllerContext = controllerContext
        };

        // Act
        ActionResult<Result> response = await sut.ResetPasswordAsync(loginId,apiRequestBody, default);
        Result result = response.Value;
        result.IsSuccess.Should().BeFalse();
        result.Errors.First().Should().Be("Too many unsuccessful reset password attempts with incorrect date of birth, the account is suspended.");


    }

    [Fact]
    public async Task GIVEN_Valid_User_And_ResetPassword_WITH_DateOfBithValidation_PasswordValidator_WHEN_Invalid_Attempt_Done_THEN_Reset_Password_FAILS()
    {
        // Arrange
        string tenantId = _fixture.Create<string>();
        string loginId = _fixture.Create<string>();
        DbConfig tenantDbConfig = _fixture.Build<DbConfig>().Create();
        DbConfig.AddConfig(tenantId, tenantDbConfig);

        ResetPasswordCommand apiRequestBody = _fixture.Build<ResetPasswordCommand>()
           .Create();

        MongoLoginDao mongoUserDao = _fixture.Build<MongoLoginDao>()
           .With(login => login.AccessFailedCount, 0)
           .Create();

        Mock<UserManager<MongoLoginDao>> mongoUserManager = new(
            new Mock<IUserStore<MongoLoginDao>>().Object,
            new Mock<IOptions<IdentityOptions>>().Object,
            new Mock<IPasswordHasher<MongoLoginDao>>().Object,
            new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
            new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
            new Mock<ILookupNormalizer>().Object,
            new Mock<IdentityErrorDescriber>().Object,
            new Mock<IServiceProvider>().Object,
            NullLogger<UserManager<MongoLoginDao>>.Instance
        );

        Mock<IRepository> authRepository = new();
        Mock<ITenantRepository> tenantRepository = new();
        Mock<ISendNotificationScheduleRepository> sendNotificationScheduleRepository = new();
        AuthService authService = new(
            new IRepository[] { authRepository.Object },
            new IEventStore[] { new Mock<IEventStore>().Object },
            new ITenantRepository[] { tenantRepository.Object },
            sendNotificationScheduleRepository.Object
        );

        Mock<MongoLoginRepository> mockMongoLoginRepository = new();
        Mock<MongoEventStore> mockMongoEventStore = new();
        PermissionValidator permissionValidator = new(new Mock<Proxies.Auth.IAuthService>().Object);
        Mock<ISchedulerService> mockSchedulerService = new();
        LoginMutation loginMutation = new(
            mockMongoLoginRepository.Object,
            mockMongoEventStore.Object,
            permissionValidator,
            mockSchedulerService.Object,
            authService
        );

        Mock<IMultiTenantFeatureManager> featureManager = new();
        Mock<INotificationService> notificationService = new();
        IdentityServerTools identityServerTools = new(
            new Mock<IHttpContextAccessor>().Object,
            new Mock<ITokenCreationService>().Object,
            new Mock<ISystemClock>().Object
        );
        Mock<HttpContext> httpContext = new();
        ControllerContext controllerContext = new() { HttpContext = httpContext.Object };


        mongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
            .ReturnsAsync(mongoUserDao);
        authRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        authRepository.Setup(x => x.GetMongoUserManager())
            .Returns(mongoUserManager.Object);
        authRepository.Setup(x => x.GetPasswordValidatorsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.Build<PasswordValidators>()
                .With(validator => validator.PasswordResetRequireDobVerification, true)
                .With(validator => validator.MaxDobVerificationAttempts, 3)
                .Create());
        authRepository.Setup(x => x.FindLoginByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoUserDao);
        httpContext.SetupGet(x => x.Request.PathBase).Returns($"/{tenantId}");

        AuthController sut = new(
            identityServerTools,
            notificationService.Object,
            new Mock<IEntityService>().Object,
            new Mock<IResourceStore>().Object,
            NullLogger<AuthController>.Instance,
            new Mock<IEventStore>().Object,
            authService,
            new Mock<IPermissionService>().Object,
            new Mock<IFeatureManager>().Object,
            featureManager.Object,
            new Mock<CustomPasswordResetTokenProviderContext>().Object,
            new Mock<IGatewayService>().Object,
            loginMutation
        )
        {
            ControllerContext = controllerContext
        };

        // Act
        ActionResult<Result> response = await sut.ResetPasswordAsync(loginId, apiRequestBody, default);
        Result result = response.Value;
        result.IsSuccess.Should().BeFalse();
        result.Errors.First().Should().Be("Incorrect/Invalid date of birth provided. Password reset requires correct date of birth.");


    }

    [Fact]
    public async Task GIVEN_Valid_User_And_ResetPassword_WITH_DateOfBithValidation_PasswordValidator_DISABLED_WHEN_Requested_THEN_SHOULD_Reset_the_Password()
    {
        // Arrange
        string tenantId = _fixture.Create<string>();
        string loginId = _fixture.Create<string>();
        DbConfig tenantDbConfig = _fixture.Build<DbConfig>().Create();
        DbConfig.AddConfig(tenantId, tenantDbConfig);

        ResetPasswordCommand apiRequestBody = _fixture.Build<ResetPasswordCommand>()
           .Create();

        MongoLoginDao mongoUserDao = _fixture.Build<MongoLoginDao>()
           .With(login => login.AccessFailedCount, 0)
           .Create();

        Mock<UserManager<MongoLoginDao>> mongoUserManager = new(
            new Mock<IUserStore<MongoLoginDao>>().Object,
            new Mock<IOptions<IdentityOptions>>().Object,
            new Mock<IPasswordHasher<MongoLoginDao>>().Object,
            new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
            new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
            new Mock<ILookupNormalizer>().Object,
            new Mock<IdentityErrorDescriber>().Object,
            new Mock<IServiceProvider>().Object,
            NullLogger<UserManager<MongoLoginDao>>.Instance
        );

        Mock<IRepository> authRepository = new();
        Mock<ITenantRepository> tenantRepository = new();
        Mock<ISendNotificationScheduleRepository> sendNotificationScheduleRepository = new();
        AuthService authService = new(
            new IRepository[] { authRepository.Object },
            new IEventStore[] { new Mock<IEventStore>().Object },
            new ITenantRepository[] { tenantRepository.Object },
            sendNotificationScheduleRepository.Object
        );

        Mock<MongoLoginRepository> mockMongoLoginRepository = new();
        Mock<MongoEventStore> mockMongoEventStore = new();
        PermissionValidator permissionValidator = new(new Mock<Proxies.Auth.IAuthService>().Object);
        Mock<ISchedulerService> mockSchedulerService = new();
        LoginMutation loginMutation = new(
            mockMongoLoginRepository.Object,
            mockMongoEventStore.Object,
            permissionValidator,
            mockSchedulerService.Object,
            authService
        );

        Mock<IMultiTenantFeatureManager> featureManager = new();
        Mock<INotificationService> notificationService = new();
        IdentityServerTools identityServerTools = new(
            new Mock<IHttpContextAccessor>().Object,
            new Mock<ITokenCreationService>().Object,
            new Mock<ISystemClock>().Object
        );
        Mock<HttpContext> httpContext = new();
        ControllerContext controllerContext = new() { HttpContext = httpContext.Object };


        mongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
            .ReturnsAsync(mongoUserDao);
        mongoUserManager.Setup(m => m.ResetPasswordAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(_fixture.Build<IdentityResult>()
            .Create());
        authRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        authRepository.Setup(x => x.GetMongoUserManager())
            .Returns(mongoUserManager.Object);
        authRepository.Setup(x => x.GetPasswordValidatorsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.Build<PasswordValidators>()
                .Without(validator => validator.PasswordResetRequireDobVerification)
                .Without(validator => validator.MaxDobVerificationAttempts)
                .Create());
        authRepository.Setup(x => x.FindLoginByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoUserDao);
        httpContext.SetupGet(x => x.Request.PathBase).Returns($"/{tenantId}");

        AuthController sut = new(
            identityServerTools,
            notificationService.Object,
            new Mock<IEntityService>().Object,
            new Mock<IResourceStore>().Object,
            NullLogger<AuthController>.Instance,
            new Mock<IEventStore>().Object,
            authService,
            new Mock<IPermissionService>().Object,
            new Mock<IFeatureManager>().Object,
            featureManager.Object,
            new Mock<CustomPasswordResetTokenProviderContext>().Object,
            new Mock<IGatewayService>().Object,
            loginMutation
        )
        {
            ControllerContext = controllerContext
        };

        // Act
        ActionResult<Result> response = await sut.ResetPasswordAsync(loginId, apiRequestBody, default);
        Result result = response.Value;
        // unable to mock IdentityResult to return true, so validating only that there is no DateofBirth validation error or account lock out
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().BeEmpty();

    }

    [Fact]
    public async Task GIVEN_Valid_User_And_ConfirmEmail_WITH_DateOfBithValidation_PasswordValidator_WHEN_Invalid_Attempts_Reached_THEN_User_Account_is_Locked()
    {
        // Arrange
        string tenantId = _fixture.Create<string>();
        string loginId = _fixture.Create<string>();
        DbConfig tenantDbConfig = _fixture.Build<DbConfig>().Create();
        DbConfig.AddConfig(tenantId, tenantDbConfig);

        ConfirmEmailCommand apiRequestBody = _fixture.Build<ConfirmEmailCommand>()
           .Create();

        MongoLoginDao mongoUserDao = _fixture.Build<MongoLoginDao>()
           .With(login => login.AccessFailedCount, 3)
           .Create();

        Mock<UserManager<MongoLoginDao>> mongoUserManager = new(
            new Mock<IUserStore<MongoLoginDao>>().Object,
            new Mock<IOptions<IdentityOptions>>().Object,
            new Mock<IPasswordHasher<MongoLoginDao>>().Object,
            new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
            new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
            new Mock<ILookupNormalizer>().Object,
            new Mock<IdentityErrorDescriber>().Object,
            new Mock<IServiceProvider>().Object,
            NullLogger<UserManager<MongoLoginDao>>.Instance
        );

        Mock<IRepository> authRepository = new();
        Mock<ITenantRepository> tenantRepository = new();
        Mock<ISendNotificationScheduleRepository> sendNotificationScheduleRepository = new();
        AuthService authService = new(
            new IRepository[] { authRepository.Object },
            new IEventStore[] { new Mock<IEventStore>().Object },
            new ITenantRepository[] { tenantRepository.Object },
            sendNotificationScheduleRepository.Object
        );

        Mock<MongoLoginRepository> mockMongoLoginRepository = new();
        Mock<MongoEventStore> mockMongoEventStore = new();
        PermissionValidator permissionValidator = new(new Mock<Proxies.Auth.IAuthService>().Object);
        Mock<ISchedulerService> mockSchedulerService = new();
        LoginMutation loginMutation = new(
            mockMongoLoginRepository.Object,
            mockMongoEventStore.Object,
            permissionValidator,
            mockSchedulerService.Object,
            authService
        );

        Mock<IMultiTenantFeatureManager> featureManager = new();
        Mock<INotificationService> notificationService = new();
        IdentityServerTools identityServerTools = new(
            new Mock<IHttpContextAccessor>().Object,
            new Mock<ITokenCreationService>().Object,
            new Mock<ISystemClock>().Object
        );
        Mock<HttpContext> httpContext = new();
        ControllerContext controllerContext = new() { HttpContext = httpContext.Object };


        mongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
            .ReturnsAsync(mongoUserDao);
        authRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        authRepository.Setup(x => x.GetMongoUserManager())
            .Returns(mongoUserManager.Object);
        authRepository.Setup(x => x.GetPasswordValidatorsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.Build<PasswordValidators>()
                .With(validator => validator.PasswordResetRequireDobVerification, true)
                .With(validator => validator.MaxDobVerificationAttempts, 3)
                .Create());
        authRepository.Setup(x => x.FindLoginByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoUserDao);
        httpContext.SetupGet(x => x.Request.PathBase).Returns($"/{tenantId}");

        AuthController sut = new(
            identityServerTools,
            notificationService.Object,
            new Mock<IEntityService>().Object,
            new Mock<IResourceStore>().Object,
            NullLogger<AuthController>.Instance,
            new Mock<IEventStore>().Object,
            authService,
            new Mock<IPermissionService>().Object,
            new Mock<IFeatureManager>().Object,
            featureManager.Object,
            new Mock<CustomPasswordResetTokenProviderContext>().Object,
            new Mock<IGatewayService>().Object,
            loginMutation
        )
        {
            ControllerContext = controllerContext
        };

        // Act
        ActionResult<Result<Token>> response = await sut.ConfirmEmailAsync2(loginId, apiRequestBody, default);
        Result<Token> result = response.Value;
        result.IsSuccess.Should().BeFalse();
        result.Errors.First().Should().Be("Too many unsuccessful reset password attempts with incorrect date of birth, the account is suspended.");


    }

    [Fact]
    public async Task GIVEN_Valid_User_And_ConfirmEmail_WITH_DateOfBithValidation_PasswordValidator_WHEN_Invalid_Attempt_Done_THEN_Reset_Password_FAILS()
    {
        // Arrange
        string tenantId = _fixture.Create<string>();
        string loginId = _fixture.Create<string>();
        DbConfig tenantDbConfig = _fixture.Build<DbConfig>().Create();
        DbConfig.AddConfig(tenantId, tenantDbConfig);

        ConfirmEmailCommand apiRequestBody = _fixture.Build<ConfirmEmailCommand>()
           .Create();

        MongoLoginDao mongoUserDao = _fixture.Build<MongoLoginDao>()
           .With(login => login.AccessFailedCount, 0)
           .Create();

        Mock<UserManager<MongoLoginDao>> mongoUserManager = new(
            new Mock<IUserStore<MongoLoginDao>>().Object,
            new Mock<IOptions<IdentityOptions>>().Object,
            new Mock<IPasswordHasher<MongoLoginDao>>().Object,
            new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
            new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
            new Mock<ILookupNormalizer>().Object,
            new Mock<IdentityErrorDescriber>().Object,
            new Mock<IServiceProvider>().Object,
            NullLogger<UserManager<MongoLoginDao>>.Instance
        );

        Mock<IRepository> authRepository = new();
        Mock<ITenantRepository> tenantRepository = new();
        Mock<ISendNotificationScheduleRepository> sendNotificationScheduleRepository = new();
        AuthService authService = new(
            new IRepository[] { authRepository.Object },
            new IEventStore[] { new Mock<IEventStore>().Object },
            new ITenantRepository[] { tenantRepository.Object },
            sendNotificationScheduleRepository.Object
        );

        Mock<MongoLoginRepository> mockMongoLoginRepository = new();
        Mock<MongoEventStore> mockMongoEventStore = new();
        PermissionValidator permissionValidator = new(new Mock<Proxies.Auth.IAuthService>().Object);
        Mock<ISchedulerService> mockSchedulerService = new();
        LoginMutation loginMutation = new(
            mockMongoLoginRepository.Object,
            mockMongoEventStore.Object,
            permissionValidator,
            mockSchedulerService.Object,
            authService
        );

        Mock<IMultiTenantFeatureManager> featureManager = new();
        Mock<INotificationService> notificationService = new();
        IdentityServerTools identityServerTools = new(
            new Mock<IHttpContextAccessor>().Object,
            new Mock<ITokenCreationService>().Object,
            new Mock<ISystemClock>().Object
        );
        Mock<HttpContext> httpContext = new();
        ControllerContext controllerContext = new() { HttpContext = httpContext.Object };


        mongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
            .ReturnsAsync(mongoUserDao);
        authRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        authRepository.Setup(x => x.GetMongoUserManager())
            .Returns(mongoUserManager.Object);
        authRepository.Setup(x => x.GetPasswordValidatorsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.Build<PasswordValidators>()
                .With(validator => validator.PasswordResetRequireDobVerification, true)
                .With(validator => validator.MaxDobVerificationAttempts, 3)
                .Create());
        authRepository.Setup(x => x.FindLoginByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoUserDao);
        httpContext.SetupGet(x => x.Request.PathBase).Returns($"/{tenantId}");

        AuthController sut = new(
            identityServerTools,
            notificationService.Object,
            new Mock<IEntityService>().Object,
            new Mock<IResourceStore>().Object,
            NullLogger<AuthController>.Instance,
            new Mock<IEventStore>().Object,
            authService,
            new Mock<IPermissionService>().Object,
            new Mock<IFeatureManager>().Object,
            featureManager.Object,
            new Mock<CustomPasswordResetTokenProviderContext>().Object,
            new Mock<IGatewayService>().Object,
            loginMutation
        )
        {
            ControllerContext = controllerContext
        };

        // Act
        ActionResult<Result<Token>> response = await sut.ConfirmEmailAsync2(loginId, apiRequestBody, default);
        Result<Token> result = response.Value;
        result.IsSuccess.Should().BeFalse();
        result.Errors.First().Should().Be("Incorrect/Invalid date of birth provided. Password reset requires correct date of birth.");


    }

    [Fact]
    public async Task GIVEN_Valid_User_And_Confirm_WITH_DateOfBithValidation_PasswordValidator_DISABLED_WHEN_Requested_THEN_Should_not_return_error()
    {
        // Arrange
        string tenantId = _fixture.Create<string>();
        string loginId = _fixture.Create<string>();
        DbConfig tenantDbConfig = _fixture.Build<DbConfig>().Create();
        DbConfig.AddConfig(tenantId, tenantDbConfig);

        ConfirmEmailCommand apiRequestBody = _fixture.Build<ConfirmEmailCommand>()
           .Create();

        MongoLoginDao mongoUserDao = _fixture.Build<MongoLoginDao>()
           .With(login => login.AccessFailedCount, 0)
           .Create();

        Mock<UserManager<MongoLoginDao>> mongoUserManager = new(
            new Mock<IUserStore<MongoLoginDao>>().Object,
            new Mock<IOptions<IdentityOptions>>().Object,
            new Mock<IPasswordHasher<MongoLoginDao>>().Object,
            new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
            new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
            new Mock<ILookupNormalizer>().Object,
            new Mock<IdentityErrorDescriber>().Object,
            new Mock<IServiceProvider>().Object,
            NullLogger<UserManager<MongoLoginDao>>.Instance
        );

        Mock<IRepository> authRepository = new();
        Mock<ITenantRepository> tenantRepository = new();
        Mock<ISendNotificationScheduleRepository> sendNotificationScheduleRepository = new();
        AuthService authService = new(
            new IRepository[] { authRepository.Object },
            new IEventStore[] { new Mock<IEventStore>().Object },
            new ITenantRepository[] { tenantRepository.Object },
            sendNotificationScheduleRepository.Object
        );

        Mock<MongoLoginRepository> mockMongoLoginRepository = new();
        Mock<MongoEventStore> mockMongoEventStore = new();
        PermissionValidator permissionValidator = new(new Mock<Proxies.Auth.IAuthService>().Object);
        Mock<ISchedulerService> mockSchedulerService = new();
        LoginMutation loginMutation = new(
            mockMongoLoginRepository.Object,
            mockMongoEventStore.Object,
            permissionValidator,
            mockSchedulerService.Object,
            authService
        );

        Mock<IMultiTenantFeatureManager> featureManager = new();
        Mock<INotificationService> notificationService = new();
        IdentityServerTools identityServerTools = new(
            new Mock<IHttpContextAccessor>().Object,
            new Mock<ITokenCreationService>().Object,
            new Mock<ISystemClock>().Object
        );
        Mock<HttpContext> httpContext = new();
        ControllerContext controllerContext = new() { HttpContext = httpContext.Object };


        mongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
            .ReturnsAsync(mongoUserDao);
        mongoUserManager.Setup(m => m.ConfirmEmailAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>()))
            .ReturnsAsync(_fixture.Build<IdentityResult>()
            .Create());
        authRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        authRepository.Setup(x => x.GetMongoUserManager())
            .Returns(mongoUserManager.Object);
        authRepository.Setup(x => x.GetPasswordValidatorsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.Build<PasswordValidators>()
                .Without(validator => validator.PasswordResetRequireDobVerification)
                .Without(validator => validator.MaxDobVerificationAttempts)
                .Create());
        authRepository.Setup(x => x.FindLoginByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoUserDao);
        httpContext.SetupGet(x => x.Request.PathBase).Returns($"/{tenantId}");

        AuthController sut = new(
            identityServerTools,
            notificationService.Object,
            new Mock<IEntityService>().Object,
            new Mock<IResourceStore>().Object,
            NullLogger<AuthController>.Instance,
            new Mock<IEventStore>().Object,
            authService,
            new Mock<IPermissionService>().Object,
            new Mock<IFeatureManager>().Object,
            featureManager.Object,
            new Mock<CustomPasswordResetTokenProviderContext>().Object,
            new Mock<IGatewayService>().Object,
            loginMutation
        )
        {
            ControllerContext = controllerContext
        };

        // Act
        ActionResult<Result<Token>> response = await sut.ConfirmEmailAsync2(loginId, apiRequestBody, default);
        Result<Token> result = response.Value;
        // unable to mock IdentityResult to return true, so validating only that there is no DateofBirth validation error or account lock out
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().BeEmpty();

    }

    [Fact]
    public async Task GIVEN_valid_forgot_password_command_AND_feature_disable_generic_forgot_password_email_is_enabled_WHEN_forgot_password_THEN_send_notification_command_should_be_overriden_by_forgot_password_email_settings()
    {
        string tenantId = _fixture.Create<string>();
        EmailMessage interceptedEmailMessage = null;
        DbConfig tenantDbConfig = _fixture.Build<DbConfig>().Create();
        DbConfig.AddConfig(tenantId, tenantDbConfig);
        SendNotificationCommand sendNotificationCommand = _fixture.Build<SendNotificationCommand>()
            .Without(c => c.ScheduleToSendAt)
            .Create();
        ForgotPasswordCommand forgotPasswordCommand = _fixture.Build<ForgotPasswordCommand>()
            .Without(c => c.Username)
            .With(c => c.SendNotificationCommand, sendNotificationCommand)
            .Create();
        forgotPasswordCommand.SendNotificationCommand.EmailMessage.TemplateRendering.Input.Content = JToken.Parse($"{{\"link\":\"test\"}}");
        MongoLoginDao mongoUserDao = _fixture.Build<MongoLoginDao>()
            .With(login => login.Claims, new List<IdentityUserClaim>
            {
                new IdentityUserClaim
                {
                    Type = "clientId",
                    Value = forgotPasswordCommand.ClientId
                }
            })
            .Create();
        App app = _fixture.Build<App>()
            .With(a => a.RedirectUris, new List<string>
            {
                $"http://localhost/new-password",
                $"http://localhost/forgot-password",
            })
            .Without(a => a.Claims)
            .Without(a => a.Email)
            .Without(a => a.UrlRouting)
            .Without(a => a.EmailSenderName)
            .With(a => a.ForgotPasswordEmailSettings, new ForgotPasswordEmailSettings()
            {
                From = "<EMAIL>",
                FromName = "Test",
                Link = "http://localhost",
                Subject = "Override Forgot Password",
                TemplateId = "Override TemplateId"
            })
            .Create();

        Mock<UserManager<MongoLoginDao>> mongoUserManager = new(
            new Mock<IUserStore<MongoLoginDao>>().Object,
            new Mock<IOptions<IdentityOptions>>().Object,
            new Mock<IPasswordHasher<MongoLoginDao>>().Object,
            new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
            new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
            new Mock<ILookupNormalizer>().Object,
            new Mock<IdentityErrorDescriber>().Object,
            new Mock<IServiceProvider>().Object,
            NullLogger<UserManager<MongoLoginDao>>.Instance
        );
        Mock<IRepository> authRepository = new();
        Mock<ITenantRepository> tenantRepository = new();
        Mock<ISendNotificationScheduleRepository> sendNotificationScheduleRepository = new();
        AuthService authService = new(
            new IRepository[] { authRepository.Object },
            new IEventStore[] { new Mock<IEventStore>().Object },
            new ITenantRepository[] { tenantRepository.Object },
            sendNotificationScheduleRepository.Object
        );

        Mock<MongoLoginRepository> mockMongoLoginRepository = new();
        Mock<MongoEventStore> mockMongoEventStore = new();
        PermissionValidator permissionValidator = new(new Mock<Proxies.Auth.IAuthService>().Object);
        Mock<ISchedulerService> mockSchedulerService = new();
        LoginMutation loginMutation = new(
            mockMongoLoginRepository.Object,
            mockMongoEventStore.Object,
            permissionValidator,
            mockSchedulerService.Object,
            authService
        );

        Mock<IMultiTenantFeatureManager> featureManager = new();
        Mock<INotificationService> notificationService = new();
        IdentityServerTools identityServerTools = new(
            new Mock<IHttpContextAccessor>().Object,
            new Mock<ITokenCreationService>().Object,
            new Mock<ISystemClock>().Object
        );
        Mock<HttpContext> httpContext = new();
        ControllerContext controllerContext = new() { HttpContext = httpContext.Object };

        mongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
            .ReturnsAsync(mongoUserDao);
        mongoUserManager.Setup(m => m.GeneratePasswordResetTokenAsync(It.IsAny<MongoLoginDao>()))
            .ReturnsAsync(_fixture.Create<string>());
        mongoUserManager.Setup(m => m.UpdateAsync(It.IsAny<MongoLoginDao>()))
            .ReturnsAsync(IdentityResult.Success);
        authRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        authRepository.Setup(x => x.GetMongoUserManager())
            .Returns(mongoUserManager.Object);
        authRepository.Setup(x => x.GetPasswordValidatorsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.Build<PasswordValidators>()
                .Without(validator => validator.PasswordResetRequireDobVerification)
                .Create());
        tenantRepository.SetupGet(x => x.ProviderId).Returns(tenantDbConfig.ProviderId);
        tenantRepository.Setup(t => t.GetAppsAsync(tenantId, It.IsAny<AppWhere>(), It.IsAny<OrderBy>(), It.IsAny<int?>(), It.IsAny<int?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<App> { app });
        notificationService.Setup(x => x.SendAsync(It.IsAny<string>(), It.IsAny<SendNotificationCommand>(), default))
            .Callback((string tenantId, SendNotificationCommand command, CancellationToken cancellationToken) =>
            {
                interceptedEmailMessage = command.EmailMessage;
            })
            .ReturnsAsync(Result.Success());
        httpContext.SetupGet(x => x.Request.PathBase).Returns($"/{tenantId}");
        featureManager.Setup(f => f.IsEnabled("DisableGenericForgotPasswordEmail", tenantId))
            .ReturnsAsync(true);

        AuthController sut = new(
            identityServerTools,
            notificationService.Object,
            new Mock<IEntityService>().Object,
            new Mock<IResourceStore>().Object,
            NullLogger<AuthController>.Instance,
            new Mock<IEventStore>().Object,
            authService,
            new Mock<IPermissionService>().Object,
            new Mock<IFeatureManager>().Object,
            featureManager.Object,
            new Mock<CustomPasswordResetTokenProviderContext>().Object,
            new Mock<IGatewayService>().Object,
            loginMutation
        )
        {
            ControllerContext = controllerContext
        };

        // Act
        await sut.ForgotPasswordAsync(forgotPasswordCommand, default);

        // Assert
        interceptedEmailMessage.From.Should().Be("<EMAIL>");
        interceptedEmailMessage.FromName.Should().Be("Test");
        interceptedEmailMessage.Subject.Should().Be("Override Forgot Password");
        interceptedEmailMessage.TemplateRendering.TemplateId.Should().Be("Override TemplateId");
    }
}
