﻿using CoverGo.Auth.Application.GraphQl.SSO;
using CoverGo.Auth.Domain.SSO;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using Xunit;
using Moq;
using FluentAssertions;
using IdentityModel.Client;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Threading;
using System.Threading.Tasks;
using HttpStatusCode = System.Net.HttpStatusCode;
using SSOConfig = CoverGo.Auth.Domain.SSOConfig;
using TokenResponse = IdentityModel.Client.TokenResponse;

namespace CoverGo.Auth.Tests.Unit.Application.GraphQl.SSO;

public class SSOAccessTokenQueryTests
{
    readonly Mock<MongoSSOConfigsRepository> _repoMock;
    readonly Mock<ITokenService> _tokenService;
    readonly SSOAccessTokenQuery _ssoAccessTokenQuery;
    readonly Mock<ILogger<SSOAccessTokenQuery>> _loggerMock;

    public SSOAccessTokenQueryTests()
    {
        _tokenService = new Mock<ITokenService>();
        _repoMock = new Mock<MongoSSOConfigsRepository>();
        _loggerMock = new Mock<ILogger<SSOAccessTokenQuery>>();

        Mock<MongoLoginRepository> loginRepoMock = new();

        _ssoAccessTokenQuery =
            new SSOAccessTokenQuery(_repoMock.Object, _loggerMock.Object, loginRepoMock.Object, _tokenService.Object);
    }

    [Fact]
    public async Task GIVEN_ValidInput_WHEN_GetSSOAccessToken_THEN_ReturnsValidTokenResponse()
    {
        // Arrange
        SSOAuthCodeInput ssoAuthCodeInput = new()
        {
            Id = "TestId",
            TokenEndpointUrl = "https://test.com/token",
            RedirectUrl = "https://test.com",
            AuthorizationCode = "TestCode"
        };
        CancellationToken cancellationToken = CancellationToken.None;

        SSOConfig ssoConfig = new()
        {
            ClientId = "TestClientId",
            ClientSecret = "TestClientSecret",
        };
        _repoMock.Setup(r => r.GetConfigByIdAsync(It.IsAny<string>(), false, cancellationToken))
            .ReturnsAsync(ssoConfig);

        TokenResponse tokenResponse = new(HttpStatusCode.OK, "OK",
            "{ \"access_token\": \"test_access_token\", \"refresh_token\": \"test_refresh_token\", \"expires_in\": 3600 }");
        _tokenService
            .Setup(c => c.RequestAuthorizationCodeTokenAsync(It.IsAny<AuthorizationCodeTokenRequest>(), cancellationToken))
            .ReturnsAsync(tokenResponse);

        // Act
        SSOTokenReponse result = await _ssoAccessTokenQuery.GetSSOAccessToken(ssoAuthCodeInput, cancellationToken);

        // Assert
        result.Should().NotBeNull();
        result.AccessToken.Should().Be("test_access_token");
        result.RefreshToken.Should().Be("test_refresh_token");
        result.ExpiresIn.Should().Be(3600);

        _repoMock.Verify(r => r.GetConfigByIdAsync(It.IsAny<string>(), false, cancellationToken), Times.Once());
        _tokenService.Verify(
            c => c.RequestAuthorizationCodeTokenAsync(It.IsAny<AuthorizationCodeTokenRequest>(), cancellationToken),
            Times.Once());
    }

    [Fact]
    public async Task GIVEN_ValidInputAndUseIdentityToken_WHEN_GetSSOAccessToken_THEN_ReturnsIdentityTokenAsAccessToken()
    {
        // Arrange
        SSOAuthCodeInput ssoAuthCodeInput = new()
        {
            Id = "TestId",
            TokenEndpointUrl = "https://test.com/token",
            RedirectUrl = "https://test.com",
            AuthorizationCode = "TestCode"
        };
        CancellationToken cancellationToken = CancellationToken.None;

        SSOConfig ssoConfig = new()
        {
            ClientId = "TestClientId",
            ClientSecret = "TestClientSecret",
            UseIdentityToken = true
        };
        _repoMock.Setup(r => r.GetConfigByIdAsync(It.IsAny<string>(), false, cancellationToken)).ReturnsAsync(ssoConfig);

        TokenResponse tokenResponse = new(HttpStatusCode.OK, "OK",
            "{ \"access_token\": \"test_access_token\", \"id_token\": \"test_id_token\", \"refresh_token\": \"test_refresh_token\", \"expires_in\": 3600 }");
        _tokenService
            .Setup(c => c.RequestAuthorizationCodeTokenAsync(It.IsAny<AuthorizationCodeTokenRequest>(), cancellationToken))
            .ReturnsAsync(tokenResponse);

        // Act
        SSOTokenReponse result = await _ssoAccessTokenQuery.GetSSOAccessToken(ssoAuthCodeInput, cancellationToken);

        // Assert
        result.Should().NotBeNull();
        result.AccessToken.Should().Be("test_id_token");
        result.RefreshToken.Should().Be("test_refresh_token");
        result.ExpiresIn.Should().Be(3600);
    }

    [Fact]
    public async Task GIVEN_ValidInputAndNotUseIdentityToken_WHEN_GetSSOAccessToken_THEN_ReturnsAccessToken()
    {
        // Arrange
        SSOAuthCodeInput ssoAuthCodeInput = new()
        {
            Id = "TestId",
            TokenEndpointUrl = "https://test.com/token",
            RedirectUrl = "https://test.com",
            AuthorizationCode = "TestCode"
        };
        CancellationToken cancellationToken = CancellationToken.None;

        SSOConfig ssoConfig = new()
        {
            ClientId = "TestClientId",
            ClientSecret = "TestClientSecret",
            UseIdentityToken = false
        };
        _repoMock.Setup(r => r.GetConfigByIdAsync(It.IsAny<string>(), false, cancellationToken)).ReturnsAsync(ssoConfig);

        TokenResponse tokenResponse = new(HttpStatusCode.OK, "OK",
            "{ \"access_token\": \"test_access_token\", \"id_token\": \"test_id_token\", \"refresh_token\": \"test_refresh_token\", \"expires_in\": 3600 }");
        _tokenService
            .Setup(c => c.RequestAuthorizationCodeTokenAsync(It.IsAny<AuthorizationCodeTokenRequest>(), cancellationToken))
            .ReturnsAsync(tokenResponse);

        // Act
        SSOTokenReponse result = await _ssoAccessTokenQuery.GetSSOAccessToken(ssoAuthCodeInput, cancellationToken);

        // Assert
        result.Should().NotBeNull();
        result.AccessToken.Should().Be("test_access_token");
        result.RefreshToken.Should().Be("test_refresh_token");
        result.ExpiresIn.Should().Be(3600);
    }

    [Fact]
    public async Task GIVEN_ErrorTokenResponse_WHEN_GetSSOAccessToken_THEN_ThrowsExceptionAndLogsError()
    {
        // Arrange
        SSOAuthCodeInput ssoAuthCodeInput = new()
        {
            Id = "TestId",
            TokenEndpointUrl = "https://test.com/token",
            RedirectUrl = "https://test.com",
            AuthorizationCode = "TestCode"
        };
        CancellationToken cancellationToken = CancellationToken.None;

        SSOConfig ssoConfig = new()
        {
            ClientId = "TestClientId",
            ClientSecret = "TestClientSecret",
        };
        _repoMock.Setup(r => r.GetConfigByIdAsync(It.IsAny<string>(), false, cancellationToken)).ReturnsAsync(ssoConfig);

        Exception exception = new("Test exception");
        TokenResponse errorTokenResponse = new(exception);
        _tokenService
            .Setup(c => c.RequestAuthorizationCodeTokenAsync(It.IsAny<AuthorizationCodeTokenRequest>(), cancellationToken))
            .ReturnsAsync(errorTokenResponse);

        // Act
        await Assert.ThrowsAsync<SecurityTokenException>(() =>
            _ssoAccessTokenQuery.GetSSOAccessToken(ssoAuthCodeInput, cancellationToken));

        // Assert
        _loggerMock.Verify(x => x.Log(LogLevel.Error, It.IsAny<EventId>(), It.Is<It.IsAnyType>((v, t) => true),
            It.IsAny<Exception>(), It.IsAny<Func<It.IsAnyType, Exception, string>>()));
    }

    [Fact]
    public async Task GIVEN_ValidInput_WHEN_GetSSORefreshToken_THEN_ReturnsValidTokenResponse()
    {
        // Arrange
        SSORefreshTokenInput ssoRefreshTokenInput = new()
        {
            Id = "TestId",
            TokenEndpointUrl = "https://test.com/token",
            RefreshToken = "TestRefreshToken"
        };
        CancellationToken cancellationToken = CancellationToken.None;

        SSOConfig ssoConfig = new()
        {
            ClientId = "TestClientId",
            ClientSecret = "TestClientSecret",
        };
        _repoMock.Setup(r => r.GetConfigByIdAsync(It.IsAny<string>(), false, cancellationToken)).ReturnsAsync(ssoConfig);

        TokenResponse tokenResponse = new(HttpStatusCode.OK, "OK",
            "{ \"access_token\": \"test_access_token\", \"refresh_token\": \"test_refresh_token\", \"expires_in\": 3600 }");
        _tokenService.Setup(c => c.RequestRefreshTokenAsync(It.IsAny<RefreshTokenRequest>(), cancellationToken))
            .ReturnsAsync(tokenResponse);

        // Act
        SSOTokenReponse result = await _ssoAccessTokenQuery.GetSSORefreshToken(ssoRefreshTokenInput, cancellationToken);

        // Assert
        result.Should().NotBeNull();
        result.AccessToken.Should().Be("test_access_token");
        result.RefreshToken.Should().Be("test_refresh_token");
        result.ExpiresIn.Should().Be(3600);

        _repoMock.Verify(r => r.GetConfigByIdAsync(It.IsAny<string>(), false, cancellationToken), Times.Once());
        _tokenService.Verify(c => c.RequestRefreshTokenAsync(It.IsAny<RefreshTokenRequest>(), cancellationToken),
            Times.Once());
    }
}
