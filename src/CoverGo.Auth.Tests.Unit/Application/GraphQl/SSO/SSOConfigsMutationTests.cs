﻿using AutoFixture;
using AutoFixture.AutoMoq;
using AutoFixture.Xunit2;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Application;
using CoverGo.Auth.Application.GraphQl.SSO;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using CoverGo.DomainUtils;
using FluentAssertions;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using ClaimsIdentity = System.Security.Claims.ClaimsIdentity;
using IAuthService = CoverGo.Proxies.Auth.IAuthService;
using SSOConfig = CoverGo.Auth.Domain.SSOConfig;

namespace CoverGo.Auth.Tests.Unit.Application.GraphQl.SSO;

public class SSOConfigsMutationTests
{
    readonly IFixture _fixture = new Fixture().Customize(new AutoMoqCustomization());

    [Theory]
    [InlineAutoData("Id")]
    [InlineAutoData("12345")]
    public async Task GIVEN_ValidConfig_WHEN_AddSSOConfig_THEN_AddsSSOConfig(string tenantId)
    {
        // Arrange
        CancellationToken cancellationToken = CancellationToken.None;

        Mock<IAuthService> mockAuthService = _fixture.Freeze<Mock<IAuthService>>();
        Mock<MongoSSOConfigsRepository> repository = _fixture.Freeze<Mock<MongoSSOConfigsRepository>>();
        Mock<ClaimsIdentity> mockClaimsIdentity = _fixture.Freeze<Mock<ClaimsIdentity>>();

        repository
            .Setup(r => r.AddSSOConfigToTenantSettingsAsync(tenantId, It.IsAny<SSOConfig>(), cancellationToken))
            .ReturnsAsync(new Result { Status = "success" });
        mockClaimsIdentity
            .Setup(i => i.IsAuthenticated)
            .Returns(true);
        mockAuthService
            .Setup(a => a.GetPermissionTargetIdsAsync(mockClaimsIdentity.Object,
                new[] { UserClaim.CreateSSOConfig.ToString() },
                cancellationToken))
            .ReturnsAsync(new Dictionary<string, List<string>>
                { { UserClaim.CreateSSOConfig.ToString()!, new List<string>() } });

        // Action
        PermissionValidator permissionValidator = new(mockAuthService.Object);
        SSOConfigsMutation ssoConfigsMutation = new(repository.Object, permissionValidator);
        Result result =
            await ssoConfigsMutation.AddSSOConfig(tenantId, mockClaimsIdentity.Object, new SSOConfig(),
                cancellationToken);

        // Assertions
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        repository.Verify(r => r.AddSSOConfigToTenantSettingsAsync(tenantId, It.IsAny<SSOConfig>(), cancellationToken),
            Times.Once());
    }

    [Theory]
    [InlineAutoData("980r76643")]
    [InlineAutoData("456464576")]
    public async Task GIVEN_ValidConfig_WHEN_AddSSOConfig_PermissionsNotAllowed_THEN_ReturnsException(string tenantId)
    {
        // Arrange
        CancellationToken cancellationToken = CancellationToken.None;

        Mock<IAuthService> mockAuthService = _fixture.Freeze<Mock<IAuthService>>();
        Mock<MongoSSOConfigsRepository> repository = _fixture.Freeze<Mock<MongoSSOConfigsRepository>>();
        Mock<ClaimsIdentity> mockClaimsIdentity = _fixture.Freeze<Mock<ClaimsIdentity>>();

        repository
            .Setup(r => r.AddSSOConfigToTenantSettingsAsync(tenantId, It.IsAny<SSOConfig>(), cancellationToken))
            .ReturnsAsync(new Result { Status = "success" });
        mockClaimsIdentity
            .Setup(i => i.IsAuthenticated)
            .Returns(true);
        mockAuthService
            .Setup(a => a.GetPermissionTargetIdsAsync(mockClaimsIdentity.Object,
                new[] { UserClaim.CreateSSOConfig.ToString() },
                cancellationToken))
            .ReturnsAsync(new Dictionary<string, List<string>>());

        // Action
        PermissionValidator permissionValidator = new(mockAuthService.Object);
        SSOConfigsMutation ssoConfigsMutation = new(repository.Object, permissionValidator);
        Func<Task<Result>> act = () =>
            ssoConfigsMutation.AddSSOConfig(tenantId, mockClaimsIdentity.Object, new SSOConfig(),
                cancellationToken);

        // Assertions
        await act.Should().ThrowAsync<CoverGoGraphQlAuthorizationException>().WithMessage("Access denied");
    }

    [Theory]
    [InlineAutoData("34545768")]
    [InlineAutoData("54764564356")]
    public async Task GIVEN_ValidId_WHEN_RemoveSSOConfig_THEN_RemovesSSOConfig(string tenantId)
    {
        // Arrange
        CancellationToken cancellationToken = CancellationToken.None;

        Mock<IAuthService> mockAuthService = _fixture.Freeze<Mock<IAuthService>>();
        Mock<MongoSSOConfigsRepository> repository = _fixture.Freeze<Mock<MongoSSOConfigsRepository>>();
        Mock<ClaimsIdentity> mockClaimsIdentity = _fixture.Freeze<Mock<ClaimsIdentity>>();

        repository
            .Setup(r => r.RemoveSSOConfigFromTenantSettingsAsync(tenantId, It.IsAny<string>(), cancellationToken))
            .ReturnsAsync(new Result { Status = "success" });
        mockClaimsIdentity
            .Setup(i => i.IsAuthenticated)
            .Returns(true);
        mockAuthService
            .Setup(a => a.GetPermissionTargetIdsAsync(mockClaimsIdentity.Object,
                new[] { UserClaim.DeleteSSOConfig.ToString() },
                cancellationToken))
            .ReturnsAsync(new Dictionary<string, List<string>>
                { { UserClaim.DeleteSSOConfig.ToString()!, new List<string>() } });

        // Action
        PermissionValidator permissionValidator = new(mockAuthService.Object);
        SSOConfigsMutation ssoConfigsMutation = new(repository.Object, permissionValidator);
        Result result =
            await ssoConfigsMutation.RemoveSSOConfig(tenantId, mockClaimsIdentity.Object, Guid.NewGuid().ToString(),
                cancellationToken);

        // Assertions
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        repository.Verify(r => r.RemoveSSOConfigFromTenantSettingsAsync(tenantId, It.IsAny<string>(), cancellationToken),
            Times.Once());
    }

    [Theory]
    [InlineAutoData("23409432058")]
    [InlineAutoData("23490567456")]
    public async Task GIVEN_ValidId_WHEN_RemoveSSOConfig_PermissionsNotAllowed_THEN_ReturnsException(string tenantId)
    {
        // Arrange
        CancellationToken cancellationToken = CancellationToken.None;

        Mock<IAuthService> mockAuthService = _fixture.Freeze<Mock<IAuthService>>();
        Mock<MongoSSOConfigsRepository> repository = _fixture.Freeze<Mock<MongoSSOConfigsRepository>>();
        Mock<ClaimsIdentity> mockClaimsIdentity = _fixture.Freeze<Mock<ClaimsIdentity>>();

        repository
            .Setup(r => r.RemoveSSOConfigFromTenantSettingsAsync(tenantId, It.IsAny<string>(), cancellationToken))
            .ReturnsAsync(new Result { Status = "success" });
        mockClaimsIdentity
            .Setup(i => i.IsAuthenticated)
            .Returns(true);
        mockAuthService
            .Setup(a => a.GetPermissionTargetIdsAsync(mockClaimsIdentity.Object,
                new[] { UserClaim.CreateSSOConfig.ToString() },
                cancellationToken))
            .ReturnsAsync(new Dictionary<string, List<string>>());

        // Action
        PermissionValidator permissionValidator = new(mockAuthService.Object);
        SSOConfigsMutation ssoConfigsMutation = new(repository.Object, permissionValidator);
        Func<Task<Result>> act = () =>
            ssoConfigsMutation.RemoveSSOConfig(tenantId, mockClaimsIdentity.Object, Guid.NewGuid().ToString(),
                cancellationToken);

        // Assertions
        await act.Should().ThrowAsync<CoverGoGraphQlAuthorizationException>().WithMessage("Access denied");
    }
}
