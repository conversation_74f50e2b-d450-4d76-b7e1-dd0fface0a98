﻿using AutoFixture;
using AutoFixture.AutoMoq;
using AutoFixture.Xunit2;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Application.GraphQl.SSO;
using CoverGo.Auth.Domain;
using Xunit;
using Moq;
using System.Threading;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using FluentAssertions;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;

namespace CoverGo.Auth.Tests.Unit.Application.GraphQl.SSO
{
    public class SSOConfigsQueryTests
    {
        readonly IFixture _fixture = new Fixture().Customize(new AutoMoqCustomization());

        [Theory]
        [InlineAutoData("Id")]
        [InlineAutoData("12345")]
        public async Task GIVEN_ValidId_WHEN_GetSSOConfigById_THEN_ReturnsCorrectSSOConfig(string testId)
        {
            // Arrange
            CancellationToken cancellationToken = CancellationToken.None;
            SSOConfig testConfig = new() { Id = testId };

            Mock<MongoSSOConfigsRepository> repository = _fixture.Freeze<Mock<MongoSSOConfigsRepository>>();

            repository
                .Setup(r => r.GetConfigByIdAsync(testId, true, cancellationToken))
                .ReturnsAsync(testConfig);

            // Action
            SSOConfigsQuery ssoConfigsQuery = new(repository.Object, NullLogger<SSOConfigsQuery>.Instance);
            SSOConfig result =
                await ssoConfigsQuery.GetSSOConfigById(testId, cancellationToken);

            // Assertions
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(testConfig);

            repository.Verify(r => r.GetConfigByIdAsync(testId, true, cancellationToken), Times.Once());
        }

        [Theory]
        [InlineAutoData("Id", "ClientId")]
        [InlineAutoData("123", "3456")]
        public async Task GIVEN_ValidId_WHEN_GetConfigByIdAndClientId_THEN_ReturnsCorrectSSOConfig(string testId,
            string clientId)
        {
            // Arrange
            CancellationToken cancellationToken = CancellationToken.None;
            SSOConfig testConfig = new() { Id = testId };

            Mock<MongoSSOConfigsRepository> repository = _fixture.Freeze<Mock<MongoSSOConfigsRepository>>();

            repository
                .Setup(r => r.GetConfigByIdAndClientIdAsync(testId, clientId, true, cancellationToken))
                .ReturnsAsync(testConfig);

            // Action
            SSOConfigsQuery ssoConfigsQuery = new(repository.Object, NullLogger<SSOConfigsQuery>.Instance);
            SSOConfig result =
                await ssoConfigsQuery.GetConfigByIdAndClientId(testId, clientId, cancellationToken);

            // Assertions
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(testConfig);

            repository.Verify(r => r.GetConfigByIdAndClientIdAsync(testId, clientId, true, cancellationToken),
                Times.Once());
        }
    }
}
