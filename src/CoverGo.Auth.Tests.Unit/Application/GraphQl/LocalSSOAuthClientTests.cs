﻿using CoverGo.Applications.Http.GraphQl.Services.SSO;
using CoverGo.Auth.Application.GraphQl;
using Xunit;
using Moq;
using System.Threading.Tasks;
using FluentAssertions;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using System.Collections.Generic;
using System.Threading;

namespace CoverGo.Auth.Tests.Unit.Application.GraphQl
{
    public class LocalSsoAuthClientTests
    {
        readonly Mock<MongoSSOConfigsRepository> _ssoConfigRepoMock;
        readonly Mock<MongoLoginRepository> _loginRepoMock;
        readonly LocalSSOAuthClient _client;

        public LocalSsoAuthClientTests()
        {
            _ssoConfigRepoMock = new Mock<MongoSSOConfigsRepository>();
            _loginRepoMock = new Mock<MongoLoginRepository>();
            _client = new LocalSSOAuthClient(_ssoConfigRepoMock.Object, _loginRepoMock.Object);
        }

        [Fact]
        public async Task GIVEN_ValidId_WHEN_GetSSOConfiguration_THEN_ReturnsCorrectConfiguration()
        {
            // Arrange
            const string TestId = "TestId";
            SSOConfig testConfig = new() { Id = TestId };
            _ssoConfigRepoMock.Setup(r => r.GetConfigByIdAsync(TestId, true, CancellationToken.None)).ReturnsAsync(testConfig);

            // Act
            SSOConfiguration result = await _client.GetSSOConfiguration(TestId);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(testConfig.Id);
            _ssoConfigRepoMock.Verify(r => r.GetConfigByIdAsync(TestId, true, CancellationToken.None), Times.Once());
        }

        [Fact]
        public async Task GIVEN_ValidTenantIdAndEmail_WHEN_DoesActiveLoginExistByEmail_THEN_ReturnsCorrectValue()
        {
            // Arrange
            const string TenantId = "TenantId";
            const string Email = "Email";
            _loginRepoMock.Setup(r => r.DoesActiveLoginExistByEmail(TenantId, Email, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);

            // Act
            bool result = await _client.DoesActiveLoginExistByEmail(TenantId, Email);

            // Assert
            result.Should().BeTrue();
            _loginRepoMock.Verify(r => r.DoesActiveLoginExistByEmail(TenantId, Email, It.IsAny<CancellationToken>()),
                Times.Once());
        }

        [Fact]
        public void GIVEN_NullConfig_WHEN_MapSSOConfig_THEN_ReturnsNull()
        {
            // Arrange// Act
            SSOConfiguration result = _client.MapSSOConfig(null);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public void GIVEN_ValidConfig_WHEN_MapSSOConfig_THEN_ReturnsCorrectConfiguration()
        {
            // Arrange
            SSOConfig testConfig = new SSOConfig
            {
                Id = "TestId",
                IdClaim = "IdClaim",
                KeyUrl = "KeyUrl",
                KeyUrlClaim = "KeyUrlClaim",
                ClaimsMap = new Dictionary<string, string> { { "claim1", "value1" } },
                AdditionalClaims = new Dictionary<string, List<string>> { { "additionalClaim1", new List<string> { "value1" } } },
                TenantId = "TenantId",
                ClientId = "ClientId",
                ValidateExistingLoginByEmail = true
            };

            // Act
            SSOConfiguration result = _client.MapSSOConfig(testConfig);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(testConfig.Id);
            result.IdClaim.Should().Be(testConfig.IdClaim);
            result.KeyUrl.Should().Be(testConfig.KeyUrl);
            result.KeyUrlClaim.Should().Be(testConfig.KeyUrlClaim);
            result.ClaimsMap["claim1"].Should().Be(testConfig.ClaimsMap["claim1"]);
            result.AdditionalClaims["additionalClaim1"][0].Should().Be(testConfig.AdditionalClaims["additionalClaim1"][0]);
            result.TenantId.Should().Be(testConfig.TenantId);
            result.ClientId.Should().Be(testConfig.ClientId);
            result.ValidateExistingLoginByEmail.Should().Be((bool)testConfig.ValidateExistingLoginByEmail);
        }

    }
}
