using AutoFixture;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Application.Controllers;
using CoverGo.Auth.Application.GraphQl.SSO;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.Otp;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.Proxies.Gateway;
using FluentAssertions;
using IdentityServer4;
using IdentityServer4.Services;
using IdentityServer4.Stores;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Moq;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Security.Principal;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using static CoverGo.Auth.Application.GraphQl.SSO.LoginMutation;

namespace CoverGo.Auth.Tests.Unit.Application
{
    // Wrapper class to track calls to LoginMutation methods
    public class LoginMutationTracker
    {
        public bool DeactivateCalled { get; private set; }
        public bool ReactivateCalled { get; private set; }
        public string DeactivatedTenantId { get; private set; }
        public string DeactivatedLoginId { get; private set; }
        public string ReactivatedTenantId { get; private set; }
        public string ReactivatedLoginId { get; private set; }
        public DateTime? ReactivatedActiveFrom { get; private set; }

        public void TrackDeactivate(string tenantId, ClaimsIdentity claimsIdentity, string loginId)
        {
            DeactivateCalled = true;
            DeactivatedTenantId = tenantId;
            DeactivatedLoginId = loginId;
        }

        public void TrackReactivate(string tenantId, ClaimsIdentity claimsIdentity, string loginId, DateTime? activeFrom)
        {
            ReactivateCalled = true;
            ReactivatedTenantId = tenantId;
            ReactivatedLoginId = loginId;
            ReactivatedActiveFrom = activeFrom;
        }
    }

    // Custom LoginMutation that tracks calls to Deactivate and Reactivate
    public class TestLoginMutation : LoginMutation
    {
        private readonly LoginMutationTracker _tracker;

        public TestLoginMutation(
            MongoLoginRepository repo,
            MongoEventStore mongoEventStore,
            PermissionValidator permissionValidator,
            ISchedulerService schedulerService,
            AuthService authService,
            LoginMutationTracker tracker)
            : base(repo, mongoEventStore, permissionValidator, schedulerService, authService)
        {
            _tracker = tracker;
        }

        // Use new instead of override since the base methods are not virtual
        public new Task<Result> Deactivate(
            string tenantId,
            ClaimsIdentity identity,
            string loginId,
            DateTime? scheduleFor,
            CancellationToken cancellationToken)
        {
            _tracker.TrackDeactivate(tenantId, identity, loginId);
            return Task.FromResult(Result.Success());
        }

        // Use new instead of override since the base methods are not virtual
        public new Task<Result> Reactivate(
            string tenantId,
            ClaimsIdentity identity,
            string loginId,
            DateTime? scheduleFor,
            SendReactivateNotificationCommand reactivateNotificationCommand,
            CancellationToken cancellationToken)
        {
            _tracker.TrackReactivate(tenantId, identity, loginId, scheduleFor);
            return Task.FromResult(Result.Success());
        }
    }

    public class AuthControllerCreateLoginTests
    {
        private readonly IFixture _fixture = new Fixture();
        private readonly LoginMutationTracker _loginMutationTracker;
        private readonly Mock<UserManager<MongoLoginDao>> _mockMongoUserManager;
        private readonly Mock<HttpContext> _mockHttpContext;
        private readonly Mock<ClaimsIdentity> _mockClaimsIdentity;
        private readonly AuthController _sut;
        private readonly string _tenantId;
        private readonly string _userId;
        private readonly string _providerId;
        private readonly AuthService _authService;
        private readonly Mock<IRepository> _mockRepository;
        private readonly TestLoginMutation _testLoginMutation;

        public AuthControllerCreateLoginTests()
        {
            _tenantId = _fixture.Create<string>();
            _userId = _fixture.Create<string>();
            _providerId = "TestProviderId";

            // Setup DbConfig with our specific ProviderId
            DbConfig tenantDbConfig = _fixture.Build<DbConfig>()
                .With(c => c.ProviderId, _providerId)
                .Create();
            DbConfig.AddConfig(_tenantId, tenantDbConfig);

            // Setup UserManager mock
            _mockMongoUserManager = new Mock<UserManager<MongoLoginDao>>(
                new Mock<IUserStore<MongoLoginDao>>().Object,
                new Mock<IOptions<IdentityOptions>>().Object,
                new Mock<IPasswordHasher<MongoLoginDao>>().Object,
                new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
                new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
                new Mock<ILookupNormalizer>().Object,
                new Mock<IdentityErrorDescriber>().Object,
                new Mock<IServiceProvider>().Object,
                NullLogger<UserManager<MongoLoginDao>>.Instance);

            // Create a mock repository with the correct ProviderId
            _mockRepository = new Mock<IRepository>();
            _mockRepository.Setup(r => r.ProviderId).Returns(_providerId);
            _mockRepository.Setup(r => r.GetMongoUserManager()).Returns(_mockMongoUserManager.Object);
            
            // Create a test user
            var user = new MongoLoginDao
            {
                Id = _userId,
                UserName = "testuser",
                Email = "<EMAIL>"
            };
            
            // Setup the repository to find the login by ID (using the correct 2-parameter signature)
            _mockRepository.Setup(r => r.FindLoginByIdAsync(_userId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(user);
            
            // Setup the AuthService to find the login by ID
            var mockEventStore = new Mock<IEventStore>();
            var mockTenantRepository = new Mock<ITenantRepository>();
            var mockSendNotificationScheduleRepository = new Mock<ISendNotificationScheduleRepository>();
            
            // Create a mock IOtpRepository as required by the memory
            var mockOtpRepository = new Mock<IOtpRepository>();
            
            // Create our AuthService with the mocked repository
            _authService = new AuthService(
                new[] { _mockRepository.Object },
                new[] { mockEventStore.Object },
                new[] { mockTenantRepository.Object },
                mockSendNotificationScheduleRepository.Object);
            
            // Create a proper PermissionValidator with a mocked IAuthService
            var mockProxiesAuthService = new Mock<CoverGo.Proxies.Auth.IAuthService>();
            var permissionValidator = new PermissionValidator(mockProxiesAuthService.Object);
            
            // Create our tracker for LoginMutation calls
            _loginMutationTracker = new LoginMutationTracker();
            
            // Create LoginMutation with real constructor parameters
            var mockMongoLoginRepository = new Mock<MongoLoginRepository>();
            var mockMongoEventStore = new Mock<MongoEventStore>();
            var mockSchedulerService = new Mock<ISchedulerService>();
            
            // Create our test LoginMutation that tracks calls
            _testLoginMutation = new TestLoginMutation(
                mockMongoLoginRepository.Object,
                mockMongoEventStore.Object,
                permissionValidator,
                mockSchedulerService.Object,
                _authService, // Use our AuthService here
                _loginMutationTracker);

            // Setup HttpContext with ClaimsIdentity - properly set up the claims identity
            _mockClaimsIdentity = new Mock<ClaimsIdentity>();
            _mockClaimsIdentity.Setup(ci => ci.IsAuthenticated).Returns(true);
            _mockClaimsIdentity.Setup(ci => ci.AuthenticationType).Returns("TestAuth");
            
            // Add some claims to make it a valid ClaimsIdentity
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, "TestUser"),
                new Claim(ClaimTypes.NameIdentifier, "TestUserId")
            };
            _mockClaimsIdentity.Setup(ci => ci.Claims).Returns(claims);
            
            _mockHttpContext = new Mock<HttpContext>();
            var mockHttpRequest = new Mock<HttpRequest>();
            mockHttpRequest.SetupGet(r => r.PathBase).Returns($"/{_tenantId}");
            _mockHttpContext.SetupGet(c => c.Request).Returns(mockHttpRequest.Object);
            
            // Set up the User property with our ClaimsIdentity
            var claimsPrincipal = new ClaimsPrincipal(_mockClaimsIdentity.Object);
            _mockHttpContext.SetupGet(c => c.User).Returns(claimsPrincipal);

            // Setup controller
            var identityServerTools = new IdentityServerTools(
                new Mock<IHttpContextAccessor>().Object,
                new Mock<ITokenCreationService>().Object,
                new Mock<ISystemClock>().Object
            );
            
            _sut = new AuthController(
                identityServerTools,
                new Mock<INotificationService>().Object,
                new Mock<IEntityService>().Object,
                new Mock<IResourceStore>().Object,
                NullLogger<AuthController>.Instance,
                new Mock<IEventStore>().Object,
                _authService, // Use our AuthService here
                new Mock<IPermissionService>().Object,
                new Mock<IFeatureManager>().Object,
                new Mock<IMultiTenantFeatureManager>().Object,
                new Mock<CustomPasswordResetTokenProviderContext>().Object,
                new Mock<IGatewayService>().Object,
                _testLoginMutation) // Use our test login mutation
            {
                ControllerContext = new ControllerContext { HttpContext = _mockHttpContext.Object }
            };
        }

        [Fact]
        public async Task GIVEN_CreateLoginCommand_WITH_FutureActiveFrom_WHEN_CreateLogin_THEN_DeactivateAndReactivateAreCalled()
        {
            // Arrange
            var futureDate = DateTime.UtcNow.AddDays(7);
            var command = _fixture.Build<CreateLoginCommand>()
                .With(c => c.ActiveFrom, futureDate)
                .Create();

            var user = _fixture.Build<MongoLoginDao>()
                .With(u => u.Id, _userId)
                .Create();

            // Setup UserManager to return success for CreateAsync
            _mockMongoUserManager.Setup(m => m.CreateAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>()))
                .ReturnsAsync(IdentityResult.Success)
                .Callback<MongoLoginDao, string>((u, p) => u.Id = _userId);
            
            // Setup UserManager to return success for AddClaimAsync
            _mockMongoUserManager.Setup(m => m.AddClaimAsync(It.IsAny<MongoLoginDao>(), It.IsAny<Claim>()))
                .ReturnsAsync(IdentityResult.Success);

            // Setup UserManager to return success for UpdateAsync
            _mockMongoUserManager.Setup(m => m.UpdateAsync(It.IsAny<MongoLoginDao>()))
                .ReturnsAsync(IdentityResult.Success);
            
            // Setup FindByNameAsync to return the user with the correct ID
            _mockMongoUserManager.Setup(m => m.FindByNameAsync(command.Username))
                .ReturnsAsync(user);

            // Make sure EmailAlreadyExists and UsernameAlreadyExists return false
            _mockMongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
                .ReturnsAsync((MongoLoginDao)null);

            // Act
            var result = await _sut.CreateLogin(command, CancellationToken.None);

            // Assert
            _loginMutationTracker.DeactivateCalled.Should().BeTrue("Deactivate should be called");
            _loginMutationTracker.ReactivateCalled.Should().BeTrue("Reactivate should be called");
            _loginMutationTracker.DeactivatedTenantId.Should().Be(_tenantId);
            _loginMutationTracker.DeactivatedLoginId.Should().Be(_userId);
            _loginMutationTracker.ReactivatedTenantId.Should().Be(_tenantId);
            _loginMutationTracker.ReactivatedLoginId.Should().Be(_userId);
            _loginMutationTracker.ReactivatedActiveFrom.Should().Be(futureDate);
        }

        [Fact]
        public async Task GIVEN_CreateLoginCommand_WITH_FutureActiveFrom_WHEN_ClaimsIdentityIsNull_THEN_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            var futureDate = DateTime.UtcNow.AddDays(7);
            var command = _fixture.Build<CreateLoginCommand>()
                .With(c => c.ActiveFrom, futureDate)
                .Create();

            var user = _fixture.Build<MongoLoginDao>()
                .With(u => u.Id, _userId)
                .Create();

            // Setup user creation to succeed
            _mockMongoUserManager.Setup(m => m.CreateAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>()))
                .ReturnsAsync(IdentityResult.Success)
                .Callback<MongoLoginDao, string>((u, p) => u.Id = _userId);
            
            _mockMongoUserManager.Setup(m => m.AddClaimAsync(It.IsAny<MongoLoginDao>(), It.IsAny<Claim>()))
                .ReturnsAsync(IdentityResult.Success);
                
            // Setup FindByNameAsync to return the user with the correct ID
            _mockMongoUserManager.Setup(m => m.FindByNameAsync(command.Username))
                .ReturnsAsync(user);

            // Make sure EmailAlreadyExists and UsernameAlreadyExists return false
            _mockMongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
                .ReturnsAsync((MongoLoginDao)null);

            // This is the key part - replace the ClaimsIdentity with a non-ClaimsIdentity
            var mockIdentity = new Mock<IIdentity>();
            mockIdentity.SetupGet(i => i.IsAuthenticated).Returns(true);
            mockIdentity.SetupGet(i => i.Name).Returns("TestUser");
            mockIdentity.SetupGet(i => i.AuthenticationType).Returns("TestAuth");
            var claimsPrincipal = new ClaimsPrincipal(mockIdentity.Object);
            _mockHttpContext.SetupGet(c => c.User).Returns(claimsPrincipal);

            // Act & Assert
            await Assert.ThrowsAsync<UnauthorizedAccessException>(() => _sut.CreateLogin(command, CancellationToken.None));
        }

        [Fact]
        public async Task GIVEN_CreateLoginCommand_WITH_CurrentOrPastActiveFrom_WHEN_CreateLogin_THEN_DeactivateAndReactivateAreNotCalled()
        {
            // Arrange
            var currentDate = DateTime.UtcNow;
            var command = _fixture.Build<CreateLoginCommand>()
                .With(c => c.ActiveFrom, currentDate)
                .Create();

            var user = _fixture.Build<MongoLoginDao>()
                .With(u => u.Id, _userId)
                .Create();

            _mockMongoUserManager.Setup(m => m.CreateAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>()))
                .ReturnsAsync(IdentityResult.Success)
                .Callback<MongoLoginDao, string>((u, p) => u.Id = _userId);
            
            _mockMongoUserManager.Setup(m => m.AddClaimAsync(It.IsAny<MongoLoginDao>(), It.IsAny<Claim>()))
                .ReturnsAsync(IdentityResult.Success);

            _mockMongoUserManager.Setup(m => m.UpdateAsync(It.IsAny<MongoLoginDao>()))
                .ReturnsAsync(IdentityResult.Success);
            
            // Setup FindByNameAsync to return the user with the correct ID
            _mockMongoUserManager.Setup(m => m.FindByNameAsync(command.Username))
                .ReturnsAsync(user);

            // Make sure EmailAlreadyExists and UsernameAlreadyExists return false
            _mockMongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
                .ReturnsAsync((MongoLoginDao)null);

            // Act
            var result = await _sut.CreateLogin(command, CancellationToken.None);

            // Assert
            _loginMutationTracker.DeactivateCalled.Should().BeFalse("Deactivate should not be called");
            _loginMutationTracker.ReactivateCalled.Should().BeFalse("Reactivate should not be called");
        }

        [Fact]
        public async Task GIVEN_CreateLoginCommand_WITH_NullActiveFrom_WHEN_CreateLogin_THEN_DeactivateAndReactivateAreNotCalled()
        {
            // Arrange
            var command = _fixture.Build<CreateLoginCommand>()
                .Without(c => c.ActiveFrom)
                .Create();

            var user = _fixture.Build<MongoLoginDao>()
                .With(u => u.Id, _userId)
                .Create();

            _mockMongoUserManager.Setup(m => m.CreateAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>()))
                .ReturnsAsync(IdentityResult.Success)
                .Callback<MongoLoginDao, string>((u, p) => u.Id = _userId);
            
            _mockMongoUserManager.Setup(m => m.AddClaimAsync(It.IsAny<MongoLoginDao>(), It.IsAny<Claim>()))
                .ReturnsAsync(IdentityResult.Success);

            _mockMongoUserManager.Setup(m => m.UpdateAsync(It.IsAny<MongoLoginDao>()))
                .ReturnsAsync(IdentityResult.Success);
            
            // Setup FindByNameAsync to return the user with the correct ID
            _mockMongoUserManager.Setup(m => m.FindByNameAsync(command.Username))
                .ReturnsAsync(user);

            // Make sure EmailAlreadyExists and UsernameAlreadyExists return false
            _mockMongoUserManager.Setup(m => m.FindByEmailAsync(It.IsAny<string>()))
                .ReturnsAsync((MongoLoginDao)null);

            // Act
            var result = await _sut.CreateLogin(command, CancellationToken.None);

            // Assert
            _loginMutationTracker.DeactivateCalled.Should().BeFalse("Deactivate should not be called");
            _loginMutationTracker.ReactivateCalled.Should().BeFalse("Reactivate should not be called");
        }
    }
}
