using System;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Adapters.Mongo.Repository;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Moq;
using Xunit;

namespace CoverGo.Auth.Tests.Unit.Application
{
    public class StartupTests
    {
        #region Constructor Tests
        
        [Fact]
        public void Constructor_WithDefaultEnvironmentVariables_InitializesRsaSecurityKey()
        {
            // Arrange
            var mockConfiguration = new Mock<IConfiguration>();
            
            // Act
            var startup = new Startup(mockConfiguration.Object);
            
            // Assert
            Assert.NotNull(Startup.RsaSecurityKey);
            Assert.IsType<RsaSecurityKey>(Startup.RsaSecurityKey);
            Assert.NotNull(Startup.Configuration);
        }

        [Fact]
        public void Constructor_WithCustomEnvironmentVariables_ShouldUseDefaultValuesWhenInvalidEnvironmentVariablesAreProvided()
        {
            // Arrange
            var mockConfiguration = new Mock<IConfiguration>();
            
            // Set custom environment variables for the test with invalid values
            string testValue = "testValue123";
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_D", testValue);
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_DP", testValue);
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_DQ", testValue);
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_EXPONENT", testValue);
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_INVERSEQ", testValue);
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_MODULUS", testValue);
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_P", testValue);
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_Q", testValue);
            
            try
            {
                // Act
                var startup = new Startup(mockConfiguration.Object);
                
                // Assert
                Assert.NotNull(Startup.RsaSecurityKey);
                Assert.IsType<RsaSecurityKey>(Startup.RsaSecurityKey);
            }
            finally
            {
                // Clean up environment variables
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_D", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_DP", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_DQ", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_EXPONENT", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_INVERSEQ", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_MODULUS", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_P", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_Q", null);
            }
        }

        [Fact]
        public void Constructor_WithValidCustomRsaParameters_InitializesCorrectly()
        {
            // Arrange
            var mockConfiguration = new Mock<IConfiguration>();
            using var rsa = RSA.Create();
            var parameters = rsa.ExportParameters(true);
            
            // Set environment variables with valid Base64Url encoded values
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_D", Base64UrlEncoder.Encode(parameters.D));
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_DP", Base64UrlEncoder.Encode(parameters.DP));
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_DQ", Base64UrlEncoder.Encode(parameters.DQ));
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_EXPONENT", Base64UrlEncoder.Encode(parameters.Exponent));
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_INVERSEQ", Base64UrlEncoder.Encode(parameters.InverseQ));
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_MODULUS", Base64UrlEncoder.Encode(parameters.Modulus));
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_P", Base64UrlEncoder.Encode(parameters.P));
            Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_Q", Base64UrlEncoder.Encode(parameters.Q));
            
            try
            {
                // Act
                var startup = new Startup(mockConfiguration.Object);
                
                // Assert
                Assert.NotNull(Startup.RsaSecurityKey);
                Assert.IsType<RsaSecurityKey>(Startup.RsaSecurityKey);
            }
            finally
            {
                // Clean up environment variables
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_D", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_DP", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_DQ", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_EXPONENT", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_INVERSEQ", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_MODULUS", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_P", null);
                Environment.SetEnvironmentVariable("RSA_ENCODEDCONFIG_Q", null);
            }
        }

        #endregion

        #region ConfigureServices Tests

        [Fact]
        public void ConfigureServices_RegistersRequiredServices()
        {
            // Arrange
            var mockConfiguration = new Mock<IConfiguration>();
            var mockConfigSection = new Mock<IConfigurationSection>();
            mockConfigSection.Setup(x => x.Value).Returns("http://scheduler-url");
            mockConfiguration.Setup(x => x["serviceUrls:scheduler"]).Returns("http://scheduler-url");
            
            var startup = new Startup(mockConfiguration.Object);
            var services = new ServiceCollection();

            // Act
            startup.ConfigureServices(services);

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            
            // Verify essential services are registered
            Assert.NotNull(serviceProvider.GetService<IHttpContextAccessor>());
            Assert.NotNull(serviceProvider.GetService<ISchedulerService>());
            
            // Verify AuthService and its required dependencies are registered
            Assert.NotNull(serviceProvider.GetService<AuthService>());
            Assert.NotNull(serviceProvider.GetService<ISendNotificationScheduleRepository>());
            
            // Verify Mongo repositories are registered
            Assert.NotNull(serviceProvider.GetService<IMongoClientFactory>());
            Assert.NotNull(serviceProvider.GetService<ITenantRepository>());
            Assert.NotNull(serviceProvider.GetService<IBranchRequestDelegateProvider>());
        }

        [Fact]
        public void ConfigureServices_RegistersIdentityServer()
        {
            // Arrange
            var mockConfiguration = new Mock<IConfiguration>();
            mockConfiguration.Setup(x => x["serviceUrls:scheduler"]).Returns("http://scheduler-url");
            
            var startup = new Startup(mockConfiguration.Object);
            var services = new ServiceCollection();

            // Act
            startup.ConfigureServices(services);

            // Assert
            // Check for IdentityServer4 related services
            var identityServerService = services.FirstOrDefault(s => 
                s.ServiceType.FullName != null && 
                s.ServiceType.FullName.Contains("IdentityServer4"));
            Assert.NotNull(identityServerService);
        }

        [Fact]
        public void ConfigureServices_ConfiguresHttpClient_WithCorrectBaseAddress()
        {
            // Arrange
            var mockConfiguration = new Mock<IConfiguration>();
            mockConfiguration.Setup(x => x["serviceUrls:scheduler"]).Returns("http://scheduler-url");
            
            var startup = new Startup(mockConfiguration.Object);
            var services = new ServiceCollection();

            // Act
            startup.ConfigureServices(services);

            // Assert
            var httpClientFactory = services.BuildServiceProvider().GetRequiredService<IHttpClientFactory>();
            Assert.NotNull(httpClientFactory);
            
            var httpClientFactoryRegistration = services.FirstOrDefault(s => 
                s.ServiceType == typeof(IHttpClientFactory));
            Assert.NotNull(httpClientFactoryRegistration);
        }

        [Fact]
        public void ConfigureServices_SetsAppNameEnvironmentVariable_WhenNotAlreadySet()
        {
            // Arrange
            var mockConfiguration = new Mock<IConfiguration>();
            mockConfiguration.Setup(x => x["serviceUrls:scheduler"]).Returns("http://scheduler-url");
            
            Environment.SetEnvironmentVariable("appName", null);
            
            var startup = new Startup(mockConfiguration.Object);
            var services = new ServiceCollection();

            // Act
            startup.ConfigureServices(services);

            // Assert
            Assert.Equal("covergo-auth", Environment.GetEnvironmentVariable("appName"));
            
            // Clean up
            Environment.SetEnvironmentVariable("appName", null);
        }

        [Fact]
        public void ConfigureServices_DoesNotChangeAppNameEnvironmentVariable_WhenAlreadySet()
        {
            // Arrange
            var mockConfiguration = new Mock<IConfiguration>();
            mockConfiguration.Setup(x => x["serviceUrls:scheduler"]).Returns("http://scheduler-url");
            
            Environment.SetEnvironmentVariable("appName", "custom-app-name");
            
            var startup = new Startup(mockConfiguration.Object);
            var services = new ServiceCollection();

            // Act
            startup.ConfigureServices(services);

            // Assert
            Assert.Equal("custom-app-name", Environment.GetEnvironmentVariable("appName"));
            
            // Clean up
            Environment.SetEnvironmentVariable("appName", null);
        }

        [Fact]
        public void ConfigureServices_AddsSwaggerGen()
        {
            // Arrange
            var mockConfiguration = new Mock<IConfiguration>();
            mockConfiguration.Setup(x => x["serviceUrls:scheduler"]).Returns("http://scheduler-url");
            
            var startup = new Startup(mockConfiguration.Object);
            var services = new ServiceCollection();

            // Act
            startup.ConfigureServices(services);

            // Assert
            var swaggerGenService = services.FirstOrDefault(s => s.ServiceType.Name.Contains("ISwaggerProvider"));
            Assert.NotNull(swaggerGenService);
        }

        #endregion
    }
}
