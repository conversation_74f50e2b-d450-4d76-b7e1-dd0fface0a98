using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Auth.Tests.Unit.Application
{
    // Custom AuthService for testing that allows us to control the behavior of non-virtual methods
    public class TestAuthService : AuthService
    {
        private readonly Dictionary<string, UserManager<MongoLoginDao>> _userManagers = new Dictionary<string, UserManager<MongoLoginDao>>();
        private readonly Dictionary<string, Dictionary<string, MongoLoginDao>> _users = new Dictionary<string, Dictionary<string, MongoLoginDao>>();

        public TestAuthService(
            IEnumerable<IRepository> repositories,
            IEnumerable<IEventStore> eventStores,
            IEnumerable<ITenantRepository> tenantRepositories,
            ISendNotificationScheduleRepository sendNotificationScheduleRepository)
            : base(repositories, eventStores, tenantRepositories, sendNotificationScheduleRepository)
        {
        }

        // Register a UserManager for a specific tenant
        public void RegisterUserManager(string tenantId, UserManager<MongoLoginDao> userManager)
        {
            _userManagers[tenantId] = userManager;
        }

        // Register a user for a specific tenant
        public void RegisterUser(string tenantId, string userId, MongoLoginDao user)
        {
            if (!_users.ContainsKey(tenantId))
            {
                _users[tenantId] = new Dictionary<string, MongoLoginDao>();
            }
            
            _users[tenantId][userId] = user;
        }

        // Override GetMongoUserManager to return our registered UserManager
        public new UserManager<MongoLoginDao> GetMongoUserManager(string tenantId)
        {
            if (_userManagers.TryGetValue(tenantId, out var userManager))
            {
                return userManager;
            }
            
            return base.GetMongoUserManager(tenantId);
        }

        // Since we can't override FindLoginByIdAsync, we'll use a different approach
        // We'll create a new method that will be used by our tests
        public Task<MongoLoginDao> GetTestUser(string tenantId, string loginId)
        {
            if (_users.TryGetValue(tenantId, out var tenantUsers) && 
                tenantUsers.TryGetValue(loginId, out var user))
            {
                return Task.FromResult(user);
            }
            
            return Task.FromResult<MongoLoginDao>(null);
        }
    }
}
