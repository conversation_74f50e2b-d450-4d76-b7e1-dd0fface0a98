using AutoFixture;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Auth.Application.Controllers;
using CoverGo.Auth.Application.GraphQl.SSO;
using CoverGo.Auth.Domain;
using CoverGo.Auth.Domain.OtherServices;
using CoverGo.Auth.Infrastructure.Adapters.Mongo;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.Proxies.Gateway;
using FluentAssertions;
using IdentityServer4;
using IdentityServer4.Services;
using IdentityServer4.Stores;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Moq;
using System;
using System.Security.Claims;
using System.Security.Principal;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using static CoverGo.Auth.Application.GraphQl.SSO.LoginMutation;

namespace CoverGo.Auth.Tests.Unit.Application
{
    // Wrapper class for LoginMutation that we can use for testing
    public class LoginMutationWrapper
    {
        public bool DeactivateCalled { get; private set; }
        public bool ReactivateCalled { get; private set; }
        public string DeactivatedTenantId { get; private set; }
        public string DeactivatedLoginId { get; private set; }
        public string ReactivatedTenantId { get; private set; }
        public string ReactivatedLoginId { get; private set; }
        public DateTime? ReactivatedActiveFrom { get; private set; }
        
        public async Task<Result> Deactivate(
            string tenantId,
            ClaimsIdentity claimsIdentity,
            string loginId,
            string reason,
            CancellationToken cancellationToken)
        {
            DeactivateCalled = true;
            DeactivatedTenantId = tenantId;
            DeactivatedLoginId = loginId;
            
            await Task.CompletedTask;
            return Result.Success();
        }
        
        public async Task<Result> Reactivate(
            string tenantId,
            ClaimsIdentity claimsIdentity,
            string loginId,
            DateTime? activeFrom,
            string reason,
            CancellationToken cancellationToken)
        {
            ReactivateCalled = true;
            ReactivatedTenantId = tenantId;
            ReactivatedLoginId = loginId;
            ReactivatedActiveFrom = activeFrom;
            
            await Task.CompletedTask;
            return Result.Success();
        }
    }
    
    // Test implementation of LoginMutation that uses our wrapper
    public class TestLoginMutation : LoginMutation
    {
        private readonly LoginMutationWrapper _wrapper;
        
        public TestLoginMutation(LoginMutationWrapper wrapper) 
            : base(
                new Mock<MongoLoginRepository>().Object,
                new Mock<MongoEventStore>().Object,
                new PermissionValidator(new Mock<Proxies.Auth.IAuthService>().Object),
                new Mock<ISchedulerService>().Object,
                new AuthService(
                    new IRepository[] { new Mock<IRepository>().Object },
                    new IEventStore[] { new Mock<IEventStore>().Object },
                    new ITenantRepository[] { new Mock<ITenantRepository>().Object },
                    new Mock<ISendNotificationScheduleRepository>().Object))
        {
            _wrapper = wrapper;
        }
        
        public Task<Result> Deactivate(
            string tenantId,
            ClaimsIdentity claimsIdentity,
            string loginId,
            string reason,
            CancellationToken cancellationToken)
        {
            return _wrapper.Deactivate(tenantId, claimsIdentity, loginId, reason, cancellationToken);
        }
        
        public Task<Result> Reactivate(
            string tenantId,
            ClaimsIdentity claimsIdentity,
            string loginId,
            DateTime? activeFrom,
            string reason,
            CancellationToken cancellationToken)
        {
            return _wrapper.Reactivate(tenantId, claimsIdentity, loginId, activeFrom, reason, cancellationToken);
        }
    }
    
    public class AuthControllerCreateLoginTests
    {
        private readonly IFixture _fixture = new Fixture();
        private readonly LoginMutationWrapper _loginMutationWrapper;
        private readonly AuthService _authService;
        private readonly Mock<UserManager<MongoLoginDao>> _mockMongoUserManager;
        private readonly Mock<HttpContext> _mockHttpContext;
        private readonly Mock<ClaimsIdentity> _mockClaimsIdentity;
        private readonly AuthController _sut;
        private readonly string _tenantId;
        private readonly string _userId;

        public AuthControllerCreateLoginTests()
        {
            _tenantId = _fixture.Create<string>();
            _userId = _fixture.Create<string>();

            // Setup DbConfig
            DbConfig tenantDbConfig = _fixture.Build<DbConfig>().Create();
            DbConfig.AddConfig(_tenantId, tenantDbConfig);

            // Setup mocks
            // Create AuthService first
            _authService = new(
                new IRepository[] { new Mock<IRepository>().Object },
                new IEventStore[] { new Mock<IEventStore>().Object },
                new ITenantRepository[] { new Mock<ITenantRepository>().Object },
                new Mock<ISendNotificationScheduleRepository>().Object
            );
            
            // Create our wrapper for LoginMutation
            _loginMutationWrapper = new LoginMutationWrapper();

            _mockMongoUserManager = new Mock<UserManager<MongoLoginDao>>(
                new Mock<IUserStore<MongoLoginDao>>().Object,
                new Mock<IOptions<IdentityOptions>>().Object,
                new Mock<IPasswordHasher<MongoLoginDao>>().Object,
                new IUserValidator<MongoLoginDao>[] { new Mock<IUserValidator<MongoLoginDao>>().Object },
                new IPasswordValidator<MongoLoginDao>[] { new Mock<IPasswordValidator<MongoLoginDao>>().Object },
                new Mock<ILookupNormalizer>().Object,
                new Mock<IdentityErrorDescriber>().Object,
                new Mock<IServiceProvider>().Object,
                NullLogger<UserManager<MongoLoginDao>>.Instance);

            // Setup HttpContext with ClaimsIdentity
            _mockClaimsIdentity = new Mock<ClaimsIdentity>();
            _mockHttpContext = new Mock<HttpContext>();
            var mockHttpRequest = new Mock<HttpRequest>();
            mockHttpRequest.SetupGet(r => r.PathBase).Returns($"/{_tenantId}");
            _mockHttpContext.SetupGet(c => c.Request).Returns(mockHttpRequest.Object);
            _mockHttpContext.SetupGet(c => c.User).Returns(new ClaimsPrincipal(_mockClaimsIdentity.Object));

            // Setup controller
            var identityServerTools = new IdentityServerTools(
                new Mock<IHttpContextAccessor>().Object,
                new Mock<ITokenCreationService>().Object,
                new Mock<ISystemClock>().Object
            );
            
            _sut = new AuthController(
                identityServerTools,
                new Mock<INotificationService>().Object,
                new Mock<IEntityService>().Object,
                new Mock<IResourceStore>().Object,
                NullLogger<AuthController>.Instance,
                new Mock<IEventStore>().Object,
                _authService,
                new Mock<IPermissionService>().Object,
                new Mock<IFeatureManager>().Object,
                new Mock<IMultiTenantFeatureManager>().Object,
                new Mock<CustomPasswordResetTokenProviderContext>().Object,
                new Mock<IGatewayService>().Object,
                new TestLoginMutation(_loginMutationWrapper))
            {
                ControllerContext = new ControllerContext { HttpContext = _mockHttpContext.Object }
            };
        }

        [Fact]
        public async Task GIVEN_CreateLoginCommand_WITH_FutureActiveFrom_WHEN_CreateLogin_THEN_DeactivateAndReactivateAreCalled()
        {
            // Arrange
            var futureDate = DateTime.UtcNow.AddDays(7);
            var command = _fixture.Build<CreateLoginCommand>()
                .With(c => c.ActiveFrom, futureDate)
                .Create();

            var user = _fixture.Build<MongoLoginDao>()
                .With(u => u.Id, _userId)
                .Create();

            _mockMongoUserManager.Setup(m => m.CreateAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>()))
                .ReturnsAsync(IdentityResult.Success);
            
            _mockMongoUserManager.Setup(m => m.AddClaimAsync(It.IsAny<MongoLoginDao>(), It.IsAny<Claim>()))
                .ReturnsAsync(IdentityResult.Success);

            _mockMongoUserManager.Setup(m => m.UpdateAsync(It.IsAny<MongoLoginDao>()))
                .ReturnsAsync(IdentityResult.Success);
            
            // Setup FindByNameAsync to return the user with the correct ID
            _mockMongoUserManager.Setup(m => m.FindByNameAsync(command.Username))
                .ReturnsAsync(user);

            // No need to set up expectations, our wrapper will track the calls

            // Act
            var result = await _sut.CreateLogin(command, CancellationToken.None);

            // Assert
            _loginMutationWrapper.DeactivateCalled.Should().BeTrue("Deactivate should be called");
            _loginMutationWrapper.ReactivateCalled.Should().BeTrue("Reactivate should be called");
            _loginMutationWrapper.DeactivatedTenantId.Should().Be(_tenantId);
            _loginMutationWrapper.DeactivatedLoginId.Should().Be(_userId);
            _loginMutationWrapper.ReactivatedTenantId.Should().Be(_tenantId);
            _loginMutationWrapper.ReactivatedLoginId.Should().Be(_userId);
            _loginMutationWrapper.ReactivatedActiveFrom.Should().Be(futureDate);
        }

        [Fact]
        public async Task GIVEN_CreateLoginCommand_WITH_FutureActiveFrom_WHEN_ClaimsIdentityIsNull_THEN_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            var futureDate = DateTime.UtcNow.AddDays(7);
            var command = _fixture.Build<CreateLoginCommand>()
                .With(c => c.ActiveFrom, futureDate)
                .Create();

            var user = _fixture.Build<MongoLoginDao>()
                .With(u => u.Id, _userId)
                .Create();

            // Setup user creation to succeed
            _mockMongoUserManager.Setup(m => m.CreateAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>()))
                .ReturnsAsync(IdentityResult.Success)
                .Callback<MongoLoginDao, string>((u, p) => u.Id = _userId);
            
            _mockMongoUserManager.Setup(m => m.AddClaimAsync(It.IsAny<MongoLoginDao>(), It.IsAny<Claim>()))
                .ReturnsAsync(IdentityResult.Success);

            // This is the key part - ensure HttpContext.User.Identity is not a ClaimsIdentity
            // but still exists as a different type of identity
            var mockIdentity = new Mock<IIdentity>();
            mockIdentity.SetupGet(i => i.IsAuthenticated).Returns(true);
            mockIdentity.SetupGet(i => i.Name).Returns("TestUser");
            mockIdentity.SetupGet(i => i.AuthenticationType).Returns("TestAuth");
            var claimsPrincipal = new ClaimsPrincipal(mockIdentity.Object);
            _mockHttpContext.SetupGet(c => c.User).Returns(claimsPrincipal);

            // Act
            var result = await _sut.CreateLogin(command, CancellationToken.None);

            // Assert
            result.Value.Status.Should().Be("failure");
            result.Value.Errors.Should().Contain("Unauthorized");
        }

        [Fact]
        public async Task GIVEN_CreateLoginCommand_WITH_CurrentOrPastActiveFrom_WHEN_CreateLogin_THEN_DeactivateAndReactivateAreNotCalled()
        {
            // Arrange
            var currentDate = DateTime.UtcNow;
            var command = _fixture.Build<CreateLoginCommand>()
                .With(c => c.ActiveFrom, currentDate)
                .Create();

            var user = _fixture.Build<MongoLoginDao>()
                .With(u => u.Id, _userId)
                .Create();

            _mockMongoUserManager.Setup(m => m.CreateAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>()))
                .ReturnsAsync(IdentityResult.Success);
            
            _mockMongoUserManager.Setup(m => m.AddClaimAsync(It.IsAny<MongoLoginDao>(), It.IsAny<Claim>()))
                .ReturnsAsync(IdentityResult.Success);

            _mockMongoUserManager.Setup(m => m.UpdateAsync(It.IsAny<MongoLoginDao>()))
                .ReturnsAsync(IdentityResult.Success);

            // Act
            var result = await _sut.CreateLogin(command, CancellationToken.None);

            // Assert
            _mockLoginMutation.Verify(m => m.Deactivate(
                It.IsAny<string>(),
                It.IsAny<ClaimsIdentity>(),
                It.IsAny<string>(),
                It.IsAny<DateTime?>(),
                It.IsAny<CancellationToken>()), Times.Never);

            _mockLoginMutation.Verify(m => m.Reactivate(
                It.IsAny<string>(),
                It.IsAny<ClaimsIdentity>(),
                It.IsAny<string>(),
                It.IsAny<DateTime?>(),
                It.IsAny<SendReactivateNotificationCommand>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GIVEN_CreateLoginCommand_WITH_NullActiveFrom_WHEN_CreateLogin_THEN_DeactivateAndReactivateAreNotCalled()
        {
            // Arrange
            var command = _fixture.Build<CreateLoginCommand>()
                .Without(c => c.ActiveFrom)
                .Create();

            var user = _fixture.Build<MongoLoginDao>()
                .With(u => u.Id, _userId)
                .Create();

            _mockMongoUserManager.Setup(m => m.CreateAsync(It.IsAny<MongoLoginDao>(), It.IsAny<string>()))
                .ReturnsAsync(IdentityResult.Success);
            
            _mockMongoUserManager.Setup(m => m.AddClaimAsync(It.IsAny<MongoLoginDao>(), It.IsAny<Claim>()))
                .ReturnsAsync(IdentityResult.Success);

            _mockMongoUserManager.Setup(m => m.UpdateAsync(It.IsAny<MongoLoginDao>()))
                .ReturnsAsync(IdentityResult.Success);

            // Act
            var result = await _sut.CreateLogin(command, CancellationToken.None);

            // Assert
            _mockLoginMutation.Verify(m => m.Deactivate(
                It.IsAny<string>(),
                It.IsAny<ClaimsIdentity>(),
                It.IsAny<string>(),
                It.IsAny<DateTime?>(),
                It.IsAny<CancellationToken>()), Times.Never);

            _mockLoginMutation.Verify(m => m.Reactivate(
                It.IsAny<string>(),
                It.IsAny<ClaimsIdentity>(),
                It.IsAny<string>(),
                It.IsAny<DateTime?>(),
                It.IsAny<SendReactivateNotificationCommand>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }
    }
}
