// <auto-generated> This file has been auto generated. </auto-generated>

using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace CoverGo.Auth.GraphQL_Client
{
    #region base classes
    public struct FieldMetadata
    {
        public string Name { get; set; }
        public bool IsComplex { get; set; }
        public Type QueryBuilderType { get; set; }
    }
    
    public enum Formatting
    {
        None,
        Indented
    }
    
    public class GraphQlObjectTypeAttribute : Attribute
    {
        public string TypeName { get; }
    
        public GraphQlObjectTypeAttribute(string typeName) => TypeName = typeName;
    }
    
    internal static class GraphQlQueryHelper
    {
        private static readonly Regex RegexWhiteSpace = new Regex(@"\s", RegexOptions.Compiled);
        private static readonly Regex RegexGraphQlIdentifier = new Regex(@"^[_A-Za-z][_0-9A-Za-z]*$", RegexOptions.Compiled);
    
        public static string GetIndentation(int level, byte indentationSize)
        {
            return new String(' ', level * indentationSize);
        }
    
        public static string BuildArgumentValue(object value, string formatMask, Formatting formatting, int level, byte indentationSize)
        {
            if (value is null)
                return "null";
    
            var enumerable = value as IEnumerable;
            if (!String.IsNullOrEmpty(formatMask) && enumerable == null)
                return
                    value is IFormattable formattable
                        ? "\"" + formattable.ToString(formatMask, CultureInfo.InvariantCulture) + "\""
                        : throw new ArgumentException($"Value must implement {nameof(IFormattable)} interface to use a format mask. ", nameof(value));
    
            if (value is Enum @enum)
                return ConvertEnumToString(@enum);
    
            if (value is bool @bool)
                return @bool ? "true" : "false";
    
            if (value is DateTime dateTime)
                return "\"" + dateTime.ToString("O") + "\"";
    
            if (value is DateTimeOffset dateTimeOffset)
                return "\"" + dateTimeOffset.ToString("O") + "\"";
    
            if (value is IGraphQlInputObject inputObject)
                return BuildInputObject(inputObject, formatting, level + 2, indentationSize);
    
            if (value is String || value is Guid)
                return "\"" + value + "\"";
    
            if (enumerable != null)
                return BuildEnumerableArgument(enumerable, formatMask, formatting, level, indentationSize, '[', ']');
    
            if (value is short || value is ushort || value is byte || value is int || value is uint || value is long || value is ulong || value is float || value is double || value is decimal)
                return Convert.ToString(value, CultureInfo.InvariantCulture);
    
            var argumentValue = Convert.ToString(value, CultureInfo.InvariantCulture);
            return "\"" + argumentValue + "\"";
        }
    
        private static string BuildEnumerableArgument(IEnumerable enumerable, string formatMask, Formatting formatting, int level, byte indentationSize, char openingSymbol, char closingSymbol)
        {
            var builder = new StringBuilder();
            builder.Append(openingSymbol);
            var delimiter = String.Empty;
            foreach (var item in enumerable)
            {
                builder.Append(delimiter);
    
                if (formatting == Formatting.Indented)
                {
                    builder.AppendLine();
                    builder.Append(GetIndentation(level + 1, indentationSize));
                }
    
                builder.Append(BuildArgumentValue(item, formatMask, formatting, level, indentationSize));
                delimiter = ",";
            }
    
            builder.Append(closingSymbol);
            return builder.ToString();
        }
    
        public static string BuildInputObject(IGraphQlInputObject inputObject, Formatting formatting, int level, byte indentationSize)
        {
            var builder = new StringBuilder();
            builder.Append("{");
    
            var isIndentedFormatting = formatting == Formatting.Indented;
            string valueSeparator;
            if (isIndentedFormatting)
            {
                builder.AppendLine();
                valueSeparator = ": ";
            }
            else
                valueSeparator = ":";
    
            var separator = String.Empty;
            foreach (var propertyValue in inputObject.GetPropertyValues())
            {
                var queryBuilderParameter = propertyValue.Value as QueryBuilderParameter;
                var value =
                    queryBuilderParameter?.Name != null
                        ? "$" + queryBuilderParameter.Name
                        : BuildArgumentValue(queryBuilderParameter?.Value ?? propertyValue.Value, propertyValue.FormatMask, formatting, level, indentationSize);
    
                builder.Append(isIndentedFormatting ? GetIndentation(level, indentationSize) : separator);
                builder.Append(propertyValue.Name);
                builder.Append(valueSeparator);
                builder.Append(value);
    
                separator = ",";
    
                if (isIndentedFormatting)
                    builder.AppendLine();
            }
    
            if (isIndentedFormatting)
                builder.Append(GetIndentation(level - 1, indentationSize));
    
            builder.Append("}");
    
            return builder.ToString();
        }
    
        public static void ValidateGraphQlIdentifier(string name, string identifier)
        {
            if (identifier != null && !RegexGraphQlIdentifier.IsMatch(identifier))
                throw new ArgumentException("value must match [_A-Za-z][_0-9A-Za-z]*", name);
        }
    
        private static string ConvertEnumToString(Enum @enum)
        {
            var enumMember = @enum.GetType().GetField(@enum.ToString());
                if (enumMember == null)
                    throw new InvalidOperationException("enum member resolution failed");
    
            var enumMemberAttribute = (EnumMemberAttribute)enumMember.GetCustomAttribute(typeof(EnumMemberAttribute));
    
            return enumMemberAttribute == null
                ? @enum.ToString()
                : enumMemberAttribute.Value;
        }
    }
    
    internal struct InputPropertyInfo
    {
        public string Name { get; set; }
        public object Value { get; set; }
        public string FormatMask { get; set; }
    }
    
    internal interface IGraphQlInputObject
    {
        IEnumerable<InputPropertyInfo> GetPropertyValues();
    }
    
    public interface IGraphQlQueryBuilder
    {
        void Clear();
        void IncludeAllFields();
        string Build(Formatting formatting = Formatting.None, byte indentationSize = 2);
    }
    
    public struct QueryBuilderArgumentInfo
    {
        public string ArgumentName { get; set; }
        public QueryBuilderParameter ArgumentValue { get; set; }
        public string FormatMask { get; set; }
    }
    
    public abstract class QueryBuilderParameter
    {
        private string _name;
    
        internal string GraphQlTypeName { get; }
        internal object Value { get; set; }
    
        public string Name
        {
            get => _name;
            set
            {
                GraphQlQueryHelper.ValidateGraphQlIdentifier(nameof(Name), value);
                _name = value;
            }
        }
    
        protected QueryBuilderParameter(string name, string graphQlTypeName, object value)
        {
            Name = name?.Trim();
            GraphQlTypeName = graphQlTypeName?.Replace(" ", null).Replace("\t", null).Replace("\n", null).Replace("\r", null);
            Value = value;
        }
    
        protected QueryBuilderParameter(object value) => Value = value;
    }
    
    public class QueryBuilderParameter<T> : QueryBuilderParameter
    {
        public new T Value
        {
            get => (T)base.Value;
            set => base.Value = value;
        }
    
        protected QueryBuilderParameter(string name, string graphQlTypeName, T value) : base(name, graphQlTypeName, value)
        {
            if (String.IsNullOrWhiteSpace(graphQlTypeName))
                throw new ArgumentException("value required", nameof(graphQlTypeName));
        }
    
        private QueryBuilderParameter(T value) : base(value)
        {
        }
    
        public static implicit operator QueryBuilderParameter<T>(T value) => new QueryBuilderParameter<T>(value);
    
        public static implicit operator T(QueryBuilderParameter<T> parameter) => parameter.Value;
    }
    
    public class GraphQlQueryParameter<T> : QueryBuilderParameter<T>
    {
        private string _formatMask;
    
        public string FormatMask
        {
            get => _formatMask;
            set => _formatMask =
                typeof(IFormattable).IsAssignableFrom(typeof(T))
                    ? value
                    : throw new InvalidOperationException($"Value must be of {nameof(IFormattable)} type. ");
        }
    
        public GraphQlQueryParameter(string name, string graphQlTypeName, T value) : base(name, graphQlTypeName, value)
        {
        }
    
        public GraphQlQueryParameter(string name, T value, bool isNullable = true) : base(name, GetGraphQlTypeName(value, isNullable), value)
        {
        }
    
        private static string GetGraphQlTypeName(T value, bool isNullable)
        {
            var graphQlTypeName = GetGraphQlTypeName(typeof(T));
            if (!isNullable)
                graphQlTypeName += "!";
    
            return graphQlTypeName;
        }
    
        private static string GetGraphQlTypeName(Type valueType)
        {
            valueType = Nullable.GetUnderlyingType(valueType) ?? valueType;
    
            if (valueType.IsArray)
            {
                var arrayItemType = GetGraphQlTypeName(valueType.GetElementType());
                return arrayItemType == null ? null : "[" + arrayItemType + "]";
            }
    
            if (typeof(IEnumerable).IsAssignableFrom(valueType))
            {
                var genericArguments = valueType.GetGenericArguments();
                if (genericArguments.Length == 1)
                {
                    var listItemType = GetGraphQlTypeName(valueType.GetGenericArguments()[0]);
                    return listItemType == null ? null : "[" + listItemType + "]";
                }
            }
    
            if (GraphQlTypes.ReverseMapping.TryGetValue(valueType, out var graphQlTypeName))
                return graphQlTypeName;
    
            if (valueType == typeof(bool))
                return "Boolean";
    
            if (valueType == typeof(float) || valueType == typeof(double) || valueType == typeof(decimal))
                return "Float";
    
            if (valueType == typeof(Guid))
                return "ID";
    
            if (valueType == typeof(sbyte) || valueType == typeof(byte) || valueType == typeof(short) || valueType == typeof(ushort) || valueType == typeof(int) || valueType == typeof(uint) ||
                valueType == typeof(long) || valueType == typeof(ulong))
                return "Int";
    
            if (valueType == typeof(string))
                return "String";
    
            return null;
        }
    }
    
    public abstract class GraphQlDirective
    {
        private readonly Dictionary<string, QueryBuilderParameter> _arguments = new Dictionary<string, QueryBuilderParameter>();
    
        internal IEnumerable<KeyValuePair<string, QueryBuilderParameter>> Arguments => _arguments;
    
        public string Name { get; }
    
        protected GraphQlDirective(string name)
        {
            GraphQlQueryHelper.ValidateGraphQlIdentifier(nameof(name), name);
            Name = name;
        }
    
        protected void AddArgument(string name, QueryBuilderParameter value)
        {
            if (value != null)
                _arguments[name] = value;
        }
    }
    
    public abstract class GraphQlQueryBuilder : IGraphQlQueryBuilder
    {
        private readonly Dictionary<string, GraphQlFieldCriteria> _fieldCriteria = new Dictionary<string, GraphQlFieldCriteria>();
    
        private readonly string _operationType;
        private readonly string _operationName;
        private Dictionary<string, GraphQlFragmentCriteria> _fragments;
        private List<QueryBuilderArgumentInfo> _queryParameters;
    
        protected abstract string TypeName { get; }
    
        public abstract IReadOnlyList<FieldMetadata> AllFields { get; }
    
        protected GraphQlQueryBuilder(string operationType, string operationName)
        {
            GraphQlQueryHelper.ValidateGraphQlIdentifier(nameof(operationName), operationName);
            _operationType = operationType;
            _operationName = operationName;
        }
    
        public virtual void Clear()
        {
            _fieldCriteria.Clear();
            _fragments?.Clear();
            _queryParameters?.Clear();
        }
    
        void IGraphQlQueryBuilder.IncludeAllFields()
        {
            IncludeAllFields();
        }
    
        public string Build(Formatting formatting = Formatting.Indented, byte indentationSize = 2)
        {
            return Build(formatting, 1, indentationSize);
        }
    
        protected void IncludeAllFields()
        {
            IncludeFields(AllFields);
        }
    
        protected virtual string Build(Formatting formatting, int level, byte indentationSize)
        {
            var isIndentedFormatting = formatting == Formatting.Indented;
            var separator = String.Empty;
            var indentationSpace = isIndentedFormatting ? " " : String.Empty;
            var builder = new StringBuilder();
    
            if (!String.IsNullOrEmpty(_operationType))
            {
                builder.Append(_operationType);
    
                if (!String.IsNullOrEmpty(_operationName))
                {
                    builder.Append(" ");
                    builder.Append(_operationName);
                }
    
                if (_queryParameters?.Count > 0)
                {
                    builder.Append(indentationSpace);
                    builder.Append("(");
    
                    foreach (var queryParameterInfo in _queryParameters)
                    {
                        if (isIndentedFormatting)
                        {
                            builder.AppendLine(separator);
                            builder.Append(GraphQlQueryHelper.GetIndentation(level, indentationSize));
                        }
                        else
                            builder.Append(separator);
                        
                        builder.Append("$");
                        builder.Append(queryParameterInfo.ArgumentValue.Name);
                        builder.Append(":");
                        builder.Append(indentationSpace);
    
                        builder.Append(queryParameterInfo.ArgumentValue.GraphQlTypeName);
    
                        if (!queryParameterInfo.ArgumentValue.GraphQlTypeName.EndsWith("!"))
                        {
                            builder.Append(indentationSpace);
                            builder.Append("=");
                            builder.Append(indentationSpace);
                            builder.Append(GraphQlQueryHelper.BuildArgumentValue(queryParameterInfo.ArgumentValue.Value, queryParameterInfo.FormatMask, formatting, 0, indentationSize));
                        }
    
                        separator = ",";
                    }
    
                    builder.Append(")");
                }
            }
    
            builder.Append(indentationSpace);
            builder.Append("{");
    
            if (isIndentedFormatting)
                builder.AppendLine();
    
            separator = String.Empty;
            
            foreach (var criteria in _fieldCriteria.Values.Concat(_fragments?.Values ?? Enumerable.Empty<GraphQlFragmentCriteria>()))
            {
                var fieldCriteria = criteria.Build(formatting, level, indentationSize);
                if (isIndentedFormatting)
                    builder.AppendLine(fieldCriteria);
                else if (!String.IsNullOrEmpty(fieldCriteria))
                {
                    builder.Append(separator);
                    builder.Append(fieldCriteria);
                }
    
                separator = ",";
            }
    
            if (isIndentedFormatting)
                builder.Append(GraphQlQueryHelper.GetIndentation(level - 1, indentationSize));
            
            builder.Append("}");
    
            return builder.ToString();
        }
    
        protected void IncludeScalarField(string fieldName, IList<QueryBuilderArgumentInfo> args)
        {
            _fieldCriteria[fieldName] = new GraphQlScalarFieldCriteria(fieldName, args);
        }
    
        protected void IncludeObjectField(string fieldName, GraphQlQueryBuilder objectFieldQueryBuilder, IList<QueryBuilderArgumentInfo> args)
        {
            _fieldCriteria[fieldName] = new GraphQlObjectFieldCriteria(fieldName, objectFieldQueryBuilder, args);
        }
    
        protected void IncludeFragment(GraphQlQueryBuilder objectFieldQueryBuilder)
        {
            _fragments = _fragments ?? new Dictionary<string, GraphQlFragmentCriteria>();
            _fragments[objectFieldQueryBuilder.TypeName] = new GraphQlFragmentCriteria(objectFieldQueryBuilder);
        }
    
        protected void ExcludeField(string fieldName)
        {
            if (fieldName == null)
                throw new ArgumentNullException(nameof(fieldName));
    
            _fieldCriteria.Remove(fieldName);
        }
    
        protected void IncludeFields(IEnumerable<FieldMetadata> fields)
        {
            IncludeFields(fields, null);
        }
    
        private void IncludeFields(IEnumerable<FieldMetadata> fields, List<Type> parentTypes)
        {
            foreach (var field in fields)
            {
                if (field.QueryBuilderType == null)
                    IncludeScalarField(field.Name, null);
                else
                {
                    var builderType = GetType();
    
                    if (parentTypes != null && parentTypes.Any(t => t.IsAssignableFrom(field.QueryBuilderType)))
                        continue;
    
                    parentTypes?.Add(builderType);
    
                    var queryBuilder = InitializeChildBuilder(builderType, field.QueryBuilderType, parentTypes);
    
                    var includeFragmentMethods = field.QueryBuilderType.GetMethods().Where(IsIncludeFragmentMethod);
    
                    foreach (var includeFragmentMethod in includeFragmentMethods)
                        includeFragmentMethod.Invoke(queryBuilder, new object[] { InitializeChildBuilder(builderType, includeFragmentMethod.GetParameters()[0].ParameterType, parentTypes) });
    
                    IncludeObjectField(field.Name, queryBuilder, null);
                }
            }
        }
    
        private static GraphQlQueryBuilder InitializeChildBuilder(Type parentQueryBuilderType, Type queryBuilderType, List<Type> parentTypes)
        {
            var queryBuilder = (GraphQlQueryBuilder)Activator.CreateInstance(queryBuilderType);
            queryBuilder.IncludeFields(queryBuilder.AllFields, parentTypes ?? new List<Type> { parentQueryBuilderType });
            return queryBuilder;
        }
    
        private static bool IsIncludeFragmentMethod(MethodInfo methodInfo)
        {
            if (!methodInfo.Name.StartsWith("With") || !methodInfo.Name.EndsWith("Fragment"))
                return false;
    
            var parameters = methodInfo.GetParameters();
            return parameters.Length == 1 && parameters[0].ParameterType.IsSubclassOf(typeof(GraphQlQueryBuilder));
        }
    
        protected void AddParameter<T>(GraphQlQueryParameter<T> parameter)
        {
            if (_queryParameters == null)
                _queryParameters = new List<QueryBuilderArgumentInfo>();
            
            _queryParameters.Add(new QueryBuilderArgumentInfo { ArgumentValue = parameter, FormatMask = parameter.FormatMask });
        }
    
        private abstract class GraphQlFieldCriteria
        {
            private readonly IList<QueryBuilderArgumentInfo> _args;
            private readonly GraphQlDirective[] _directives;
    
            protected readonly string FieldName;
    
            protected static string GetIndentation(Formatting formatting, int level, byte indentationSize) =>
                formatting == Formatting.Indented ? GraphQlQueryHelper.GetIndentation(level, indentationSize) : null;
    
            protected GraphQlFieldCriteria(string fieldName, IList<QueryBuilderArgumentInfo> args)
            {
                FieldName = fieldName;
                _args = args;
            }
    
            public abstract string Build(Formatting formatting, int level, byte indentationSize);
    
            protected string BuildArgumentClause(Formatting formatting, int level, byte indentationSize)
            {
                var separator = formatting == Formatting.Indented ? " " : null;
                var argumentCount = _args?.Count ?? 0;
                if (argumentCount == 0)
                    return String.Empty;
    
                var arguments =
                    _args.Select(
                        a => $"{a.ArgumentName}:{separator}{(a.ArgumentValue.Value != null ? GraphQlQueryHelper.BuildArgumentValue(a.ArgumentValue.Value, a.FormatMask, formatting, level, indentationSize) : "$" + a.ArgumentValue.Name)}");
    
                return $"({String.Join($",{separator}", arguments)})";
            }
        }
    
        private class GraphQlScalarFieldCriteria : GraphQlFieldCriteria
        {
            public GraphQlScalarFieldCriteria(string fieldName, IList<QueryBuilderArgumentInfo> args)
                : base(fieldName, args)
            {
            }
    
            public override string Build(Formatting formatting, int level, byte indentationSize) =>
                GetIndentation(formatting, level, indentationSize) +
                FieldName +
                BuildArgumentClause(formatting, level, indentationSize);
        }
    
        private class GraphQlObjectFieldCriteria : GraphQlFieldCriteria
        {
            private readonly GraphQlQueryBuilder _objectQueryBuilder;
    
            public GraphQlObjectFieldCriteria(string fieldName, GraphQlQueryBuilder objectQueryBuilder, IList<QueryBuilderArgumentInfo> args)
                : base(fieldName, args)
            {
                _objectQueryBuilder = objectQueryBuilder;
            }
    
            public override string Build(Formatting formatting, int level, byte indentationSize) =>
                _objectQueryBuilder._fieldCriteria.Count > 0 || _objectQueryBuilder._fragments?.Count > 0
                    ? GetIndentation(formatting, level, indentationSize) + FieldName +
                      BuildArgumentClause(formatting, level, indentationSize) + _objectQueryBuilder.Build(formatting, level + 1, indentationSize)
                    : null;
        }
    
        private class GraphQlFragmentCriteria : GraphQlFieldCriteria
        {
            private readonly GraphQlQueryBuilder _objectQueryBuilder;
    
            public GraphQlFragmentCriteria(GraphQlQueryBuilder objectQueryBuilder) : base(objectQueryBuilder.TypeName, null)
            {
                _objectQueryBuilder = objectQueryBuilder;
            }
    
            public override string Build(Formatting formatting, int level, byte indentationSize) =>
                _objectQueryBuilder._fieldCriteria.Count == 0
                    ? null
                    : GetIndentation(formatting, level, indentationSize) + "..." + (formatting == Formatting.Indented ? " " : null) + "on " +
                      FieldName + BuildArgumentClause(formatting, level, indentationSize) + _objectQueryBuilder.Build(formatting, level + 1, indentationSize);
        }
    }
    
    public abstract class GraphQlQueryBuilder<TQueryBuilder> : GraphQlQueryBuilder where TQueryBuilder : GraphQlQueryBuilder<TQueryBuilder>
    {
        protected GraphQlQueryBuilder(string operationType = null, string operationName = null) : base(operationType, operationName)
        {
        }
    
        public TQueryBuilder WithAllFields()
        {
            IncludeAllFields();
            return (TQueryBuilder)this;
        }
    
        public TQueryBuilder WithAllScalarFields()
        {
            IncludeFields(AllFields.Where(f => !f.IsComplex));
            return (TQueryBuilder)this;
        }
    
        public TQueryBuilder ExceptField(string fieldName)
        {
            ExcludeField(fieldName);
            return (TQueryBuilder)this;
        }
    
        public TQueryBuilder WithTypeName()
        {
            IncludeScalarField("__typename", null);
            return (TQueryBuilder)this;
        }
    
        protected TQueryBuilder WithScalarField(string fieldName, IList<QueryBuilderArgumentInfo> args = null)
        {
            IncludeScalarField(fieldName, args);
            return (TQueryBuilder)this;
        }
    
        protected TQueryBuilder WithObjectField(string fieldName, GraphQlQueryBuilder queryBuilder, IList<QueryBuilderArgumentInfo> args = null)
        {
            IncludeObjectField(fieldName, queryBuilder, args);
            return (TQueryBuilder)this;
        }
    
        protected TQueryBuilder WithFragment(GraphQlQueryBuilder queryBuilder)
        {
            IncludeFragment(queryBuilder);
            return (TQueryBuilder)this;
        }
    
        protected TQueryBuilder WithParameterInternal<T>(GraphQlQueryParameter<T> parameter)
        {
            AddParameter(parameter);
            return (TQueryBuilder)this;
        }
    }
    
    public abstract class GraphQlResponse<TDataContract>
    {
        public TDataContract Data { get; set; }
        public ICollection<QueryError> Errors { get; set; }
    }
    
    public class QueryError
    {
        public string Message { get; set; }
        public ICollection<ErrorLocation> Locations { get; set; }
    }
    
    public class ErrorLocation
    {
        public int Line { get; set; }
        public int Column { get; set; }
    }
    
    public class AbstractConverter<TReal, TAbstract> : JsonConverter<TReal> where TReal : TAbstract {
        public override void WriteJson(JsonWriter writer, TReal value, JsonSerializer serializer) =>
            throw new NotImplementedException();
    
        public override TReal ReadJson(JsonReader reader, Type objectType, TReal existingValue, bool hasExistingValue,
            JsonSerializer serializer) {
            if (objectType != typeof(TAbstract)) {
                serializer.ContractResolver.ResolveContract(objectType).Converter = null;
            }
    
            return serializer.Deserialize<TReal>(reader);
        }
    }
    
    public class ConcreteConverter<TReal> : JsonConverter<TReal> {
        public override void WriteJson(JsonWriter writer, TReal value, JsonSerializer serializer) =>
            throw new NotImplementedException();
    
        public override TReal ReadJson(JsonReader reader, Type objectType, TReal existingValue, bool hasExistingValue,
            JsonSerializer serializer) {
    
            serializer.ContractResolver.ResolveContract(objectType).Converter = null;
            return serializer.Deserialize<TReal>(reader);
        }
    }
    #endregion

    #region GraphQL type helpers
    public static class GraphQlTypes
    {
        public const string Boolean = "Boolean";
        public const string DateTime = "DateTime";
        public const string Int = "Int";
        public const string String = "String";

        public const string ApplyPolicy = "ApplyPolicy";

        public const string _SchemaDefinition = "_SchemaDefinition";
        public const string _service_CoverGoMutationsRoot = "_service_CoverGoMutationsRoot";
        public const string _service_CoverGoQueriesRoot = "_service_CoverGoQueriesRoot";
        public const string AppUrl = "AppUrl";
        public const string auth_AppConfiguration = "auth_AppConfiguration";
        public const string auth_CreatedStatus = "auth_CreatedStatus";
        public const string auth_Error = "auth_Error";
        public const string auth_KeyValuePairOfStringAndListOfString = "auth_KeyValuePairOfStringAndListOfString";
        public const string auth_KeyValuePairOfStringAndString = "auth_KeyValuePairOfStringAndString";
        public const string auth_Result = "auth_Result";
        public const string auth_ResultOfCreatedStatus = "auth_ResultOfCreatedStatus";
        public const string auth_SSOConfig = "auth_SSOConfig";
        public const string auth_SSOTokenReponse = "auth_SSOTokenReponse";
        public const string auth_UrlRouting = "auth_UrlRouting";

        public const string auth_KeyValuePairOfStringAndListOfStringInput = "auth_KeyValuePairOfStringAndListOfStringInput";
        public const string auth_KeyValuePairOfStringAndStringInput = "auth_KeyValuePairOfStringAndStringInput";
        public const string auth_SendReactivateEmailMessageInput = "auth_SendReactivateEmailMessageInput";
        public const string auth_SendReactivateNotificationCommandInput = "auth_SendReactivateNotificationCommandInput";
        public const string auth_SendReactivateRenderParametersInput = "auth_SendReactivateRenderParametersInput";
        public const string auth_SendReactivateTemplateRenderingInput = "auth_SendReactivateTemplateRenderingInput";
        public const string auth_SSOAuthCodeInput = "auth_SSOAuthCodeInput";
        public const string auth_SSOConfigInput = "auth_SSOConfigInput";
        public const string auth_SSORefreshTokenInput = "auth_SSORefreshTokenInput";
        public const string auth_UpdatePasswordValidatorsCommandInput = "auth_UpdatePasswordValidatorsCommandInput";

        public static readonly IReadOnlyDictionary<Type, string> ReverseMapping =
            new Dictionary<Type, string>
            {
                { typeof(ApplyPolicy), "ApplyPolicy" },
                { typeof(string), "String" },
                { typeof(bool), "Boolean" },
                { typeof(int), "Int" },
                { typeof(auth_KeyValuePairOfStringAndListOfStringInput), "auth_KeyValuePairOfStringAndListOfStringInput" },
                { typeof(auth_KeyValuePairOfStringAndStringInput), "auth_KeyValuePairOfStringAndStringInput" },
                { typeof(auth_SendReactivateEmailMessageInput), "auth_SendReactivateEmailMessageInput" },
                { typeof(auth_SendReactivateNotificationCommandInput), "auth_SendReactivateNotificationCommandInput" },
                { typeof(auth_SendReactivateRenderParametersInput), "auth_SendReactivateRenderParametersInput" },
                { typeof(auth_SendReactivateTemplateRenderingInput), "auth_SendReactivateTemplateRenderingInput" },
                { typeof(auth_SSOAuthCodeInput), "auth_SSOAuthCodeInput" },
                { typeof(auth_SSOConfigInput), "auth_SSOConfigInput" },
                { typeof(auth_SSORefreshTokenInput), "auth_SSORefreshTokenInput" },
                { typeof(auth_UpdatePasswordValidatorsCommandInput), "auth_UpdatePasswordValidatorsCommandInput" }
            };
}
    #endregion

    #region enums
    public enum ApplyPolicy
    {
        BEFORE_RESOLVER,
        AFTER_RESOLVER
    }
    #endregion

    #nullable enable
    #region builder classes
    public class _SchemaDefinitionBuilder : GraphQlQueryBuilder<_SchemaDefinitionBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "name" },
            new FieldMetadata { Name = "document" },
            new FieldMetadata { Name = "extensionDocuments", IsComplex = true }
        };

        protected override string TypeName { get; } = "_SchemaDefinition";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public _SchemaDefinitionBuilder name() => WithScalarField("name");

        public _SchemaDefinitionBuilder document() => WithScalarField("document");

        public _SchemaDefinitionBuilder extensionDocuments() => WithScalarField("extensionDocuments");
    }

    public class _service_CoverGoQueriesRootBuilder : GraphQlQueryBuilder<_service_CoverGoQueriesRootBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "_schemaDefinition", IsComplex = true, QueryBuilderType = typeof(_SchemaDefinitionBuilder) },
            new FieldMetadata { Name = "sSOAccessTokenQuerySSOAccessToken", IsComplex = true, QueryBuilderType = typeof(auth_SSOTokenReponseBuilder) },
            new FieldMetadata { Name = "sSOAccessTokenQuerySSORefreshToken", IsComplex = true, QueryBuilderType = typeof(auth_SSOTokenReponseBuilder) },
            new FieldMetadata { Name = "sSOConfigsQuerySSOConfigById", IsComplex = true, QueryBuilderType = typeof(auth_SSOConfigBuilder) },
            new FieldMetadata { Name = "sSOConfigsQueryConfigByIdAndClientId", IsComplex = true, QueryBuilderType = typeof(auth_SSOConfigBuilder) },
            new FieldMetadata { Name = "loginInternalQueryDoesActiveLoginExistByEmail" },
            new FieldMetadata { Name = "appConfigurationQueryAppConfigurationFromUrl", IsComplex = true, QueryBuilderType = typeof(auth_AppConfigurationBuilder) }
        };

        protected override string TypeName { get; } = "_service_CoverGoQueriesRoot";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public _service_CoverGoQueriesRootBuilder(string? operationName = null) : base("query", operationName)
        {
        }

        public _service_CoverGoQueriesRootBuilder WithParameter<T>(GraphQlQueryParameter<T> parameter) => WithParameterInternal(parameter);

        public record _schemaDefinitionArgs(string configuration);
        public _service_CoverGoQueriesRootBuilder _schemaDefinition(_schemaDefinitionArgs arguments, _SchemaDefinitionBuilder _SchemaDefinitionBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            args.Add(new QueryBuilderArgumentInfo { ArgumentName = "configuration", ArgumentValue = new GraphQlQueryParameter<string>("configuration", arguments.configuration) } );
            return WithObjectField("_schemaDefinition", _SchemaDefinitionBuilder, args);
        }

        public record sSOAccessTokenQuerySSOAccessTokenArgs(auth_SSOAuthCodeInput? ssoAuthCodeinput = null);
        public _service_CoverGoQueriesRootBuilder sSOAccessTokenQuerySSOAccessToken(sSOAccessTokenQuerySSOAccessTokenArgs arguments, auth_SSOTokenReponseBuilder auth_SSOTokenReponseBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.ssoAuthCodeinput != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "ssoAuthCodeinput", ArgumentValue = new GraphQlQueryParameter<auth_SSOAuthCodeInput?>("ssoAuthCodeinput", arguments.ssoAuthCodeinput) } );

            return WithObjectField("sSOAccessTokenQuerySSOAccessToken", auth_SSOTokenReponseBuilder, args);
        }

        public record sSOAccessTokenQuerySSORefreshTokenArgs(auth_SSORefreshTokenInput? ssoRefreshTokeninput = null);
        public _service_CoverGoQueriesRootBuilder sSOAccessTokenQuerySSORefreshToken(sSOAccessTokenQuerySSORefreshTokenArgs arguments, auth_SSOTokenReponseBuilder auth_SSOTokenReponseBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.ssoRefreshTokeninput != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "ssoRefreshTokeninput", ArgumentValue = new GraphQlQueryParameter<auth_SSORefreshTokenInput?>("ssoRefreshTokeninput", arguments.ssoRefreshTokeninput) } );

            return WithObjectField("sSOAccessTokenQuerySSORefreshToken", auth_SSOTokenReponseBuilder, args);
        }

        public record sSOConfigsQuerySSOConfigByIdArgs(string? id = null);
        public _service_CoverGoQueriesRootBuilder sSOConfigsQuerySSOConfigById(sSOConfigsQuerySSOConfigByIdArgs arguments, auth_SSOConfigBuilder auth_SSOConfigBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.id != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "id", ArgumentValue = new GraphQlQueryParameter<string?>("id", arguments.id) } );

            return WithObjectField("sSOConfigsQuerySSOConfigById", auth_SSOConfigBuilder, args);
        }

        public record sSOConfigsQueryConfigByIdAndClientIdArgs(string? id = null, string? clientId = null);
        public _service_CoverGoQueriesRootBuilder sSOConfigsQueryConfigByIdAndClientId(sSOConfigsQueryConfigByIdAndClientIdArgs arguments, auth_SSOConfigBuilder auth_SSOConfigBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.id != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "id", ArgumentValue = new GraphQlQueryParameter<string?>("id", arguments.id) } );

            if (arguments.clientId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "clientId", ArgumentValue = new GraphQlQueryParameter<string?>("clientId", arguments.clientId) } );

            return WithObjectField("sSOConfigsQueryConfigByIdAndClientId", auth_SSOConfigBuilder, args);
        }

        public _service_CoverGoQueriesRootBuilder loginInternalQueryDoesActiveLoginExistByEmail(string? tenantId = null, string? email = null)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (tenantId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "tenantId", ArgumentValue = new GraphQlQueryParameter<string?>("tenantId", tenantId) } );

            if (email != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "email", ArgumentValue = new GraphQlQueryParameter<string?>("email", email) } );

            return WithScalarField("loginInternalQueryDoesActiveLoginExistByEmail", args);
        }

        public record appConfigurationQueryAppConfigurationFromUrlArgs(string? appUrl = null);
        public _service_CoverGoQueriesRootBuilder appConfigurationQueryAppConfigurationFromUrl(appConfigurationQueryAppConfigurationFromUrlArgs arguments, auth_AppConfigurationBuilder auth_AppConfigurationBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.appUrl != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "appUrl", ArgumentValue = new GraphQlQueryParameter<string?>("appUrl", arguments.appUrl) } );

            return WithObjectField("appConfigurationQueryAppConfigurationFromUrl", auth_AppConfigurationBuilder, args);
        }
    }

    public class _service_CoverGoMutationsRootBuilder : GraphQlQueryBuilder<_service_CoverGoMutationsRootBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "updatePasswordValidatorsMutationUpdatePasswordValidators", IsComplex = true, QueryBuilderType = typeof(auth_ResultBuilder) },
            new FieldMetadata { Name = "updatePasswordValidatorsMutationCreatePasswordValidators", IsComplex = true, QueryBuilderType = typeof(auth_ResultOfCreatedStatusBuilder) },
            new FieldMetadata { Name = "loginMutationDeactivate", IsComplex = true, QueryBuilderType = typeof(auth_ResultBuilder) },
            new FieldMetadata { Name = "loginMutationReactivate", IsComplex = true, QueryBuilderType = typeof(auth_ResultBuilder) },
            new FieldMetadata { Name = "sSOConfigsMutationAddSSOConfig", IsComplex = true, QueryBuilderType = typeof(auth_ResultBuilder) },
            new FieldMetadata { Name = "sSOConfigsMutationRemoveSSOConfig", IsComplex = true, QueryBuilderType = typeof(auth_ResultBuilder) }
        };

        protected override string TypeName { get; } = "_service_CoverGoMutationsRoot";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public _service_CoverGoMutationsRootBuilder(string? operationName = null) : base("mutation", operationName)
        {
        }

        public _service_CoverGoMutationsRootBuilder WithParameter<T>(GraphQlQueryParameter<T> parameter) => WithParameterInternal(parameter);

        public record updatePasswordValidatorsMutationUpdatePasswordValidatorsArgs(auth_UpdatePasswordValidatorsCommandInput? updatePasswordValidatorsCommand = null);
        public _service_CoverGoMutationsRootBuilder updatePasswordValidatorsMutationUpdatePasswordValidators(updatePasswordValidatorsMutationUpdatePasswordValidatorsArgs arguments, auth_ResultBuilder auth_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.updatePasswordValidatorsCommand != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "updatePasswordValidatorsCommand", ArgumentValue = new GraphQlQueryParameter<auth_UpdatePasswordValidatorsCommandInput?>("updatePasswordValidatorsCommand", arguments.updatePasswordValidatorsCommand) } );

            return WithObjectField("updatePasswordValidatorsMutationUpdatePasswordValidators", auth_ResultBuilder, args);
        }

        public record updatePasswordValidatorsMutationCreatePasswordValidatorsArgs(auth_UpdatePasswordValidatorsCommandInput? updatePasswordValidatorsCommand = null);
        public _service_CoverGoMutationsRootBuilder updatePasswordValidatorsMutationCreatePasswordValidators(updatePasswordValidatorsMutationCreatePasswordValidatorsArgs arguments, auth_ResultOfCreatedStatusBuilder auth_ResultOfCreatedStatusBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.updatePasswordValidatorsCommand != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "updatePasswordValidatorsCommand", ArgumentValue = new GraphQlQueryParameter<auth_UpdatePasswordValidatorsCommandInput?>("updatePasswordValidatorsCommand", arguments.updatePasswordValidatorsCommand) } );

            return WithObjectField("updatePasswordValidatorsMutationCreatePasswordValidators", auth_ResultOfCreatedStatusBuilder, args);
        }

        public record loginMutationDeactivateArgs(string? loginId = null, DateTime? scheduleFor = null);
        public _service_CoverGoMutationsRootBuilder loginMutationDeactivate(loginMutationDeactivateArgs arguments, auth_ResultBuilder auth_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.loginId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "loginId", ArgumentValue = new GraphQlQueryParameter<string?>("loginId", arguments.loginId) } );

            if (arguments.scheduleFor != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "scheduleFor", ArgumentValue = new GraphQlQueryParameter<DateTime?>("scheduleFor", arguments.scheduleFor) } );

            return WithObjectField("loginMutationDeactivate", auth_ResultBuilder, args);
        }

        public record loginMutationReactivateArgs(string? loginId = null, DateTime? scheduleFor = null, auth_SendReactivateNotificationCommandInput? reactivateNotificationCommand = null);
        public _service_CoverGoMutationsRootBuilder loginMutationReactivate(loginMutationReactivateArgs arguments, auth_ResultBuilder auth_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.loginId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "loginId", ArgumentValue = new GraphQlQueryParameter<string?>("loginId", arguments.loginId) } );

            if (arguments.scheduleFor != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "scheduleFor", ArgumentValue = new GraphQlQueryParameter<DateTime?>("scheduleFor", arguments.scheduleFor) } );

            if (arguments.reactivateNotificationCommand != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "reactivateNotificationCommand", ArgumentValue = new GraphQlQueryParameter<auth_SendReactivateNotificationCommandInput?>("reactivateNotificationCommand", arguments.reactivateNotificationCommand) } );

            return WithObjectField("loginMutationReactivate", auth_ResultBuilder, args);
        }

        public record sSOConfigsMutationAddSSOConfigArgs(auth_SSOConfigInput? input = null);
        public _service_CoverGoMutationsRootBuilder sSOConfigsMutationAddSSOConfig(sSOConfigsMutationAddSSOConfigArgs arguments, auth_ResultBuilder auth_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<auth_SSOConfigInput?>("input", arguments.input) } );

            return WithObjectField("sSOConfigsMutationAddSSOConfig", auth_ResultBuilder, args);
        }

        public record sSOConfigsMutationRemoveSSOConfigArgs(string? id = null);
        public _service_CoverGoMutationsRootBuilder sSOConfigsMutationRemoveSSOConfig(sSOConfigsMutationRemoveSSOConfigArgs arguments, auth_ResultBuilder auth_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.id != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "id", ArgumentValue = new GraphQlQueryParameter<string?>("id", arguments.id) } );

            return WithObjectField("sSOConfigsMutationRemoveSSOConfig", auth_ResultBuilder, args);
        }
    }

    public class AppUrlBuilder : GraphQlQueryBuilder<AppUrlBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "appId" },
            new FieldMetadata { Name = "urlRouting", IsComplex = true, QueryBuilderType = typeof(auth_UrlRoutingBuilder) }
        };

        protected override string TypeName { get; } = "AppUrl";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public AppUrlBuilder appId() => WithScalarField("appId");

        public AppUrlBuilder urlRouting(auth_UrlRoutingBuilder auth_UrlRoutingBuilder) => WithObjectField("urlRouting", auth_UrlRoutingBuilder);
    }

    public class auth_UrlRoutingBuilder : GraphQlQueryBuilder<auth_UrlRoutingBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "url" },
            new FieldMetadata { Name = "regexPattern" },
            new FieldMetadata { Name = "order" }
        };

        protected override string TypeName { get; } = "auth_UrlRouting";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_UrlRoutingBuilder url() => WithScalarField("url");

        public auth_UrlRoutingBuilder regexPattern() => WithScalarField("regexPattern");

        public auth_UrlRoutingBuilder order() => WithScalarField("order");
    }

    public class auth_SSOTokenReponseBuilder : GraphQlQueryBuilder<auth_SSOTokenReponseBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "accessToken" },
            new FieldMetadata { Name = "refreshToken" },
            new FieldMetadata { Name = "expiresIn" }
        };

        protected override string TypeName { get; } = "auth_SSOTokenReponse";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_SSOTokenReponseBuilder accessToken() => WithScalarField("accessToken");

        public auth_SSOTokenReponseBuilder refreshToken() => WithScalarField("refreshToken");

        public auth_SSOTokenReponseBuilder expiresIn() => WithScalarField("expiresIn");
    }

    public class auth_ResultOfCreatedStatusBuilder : GraphQlQueryBuilder<auth_ResultOfCreatedStatusBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "status" },
            new FieldMetadata { Name = "errors", IsComplex = true },
            new FieldMetadata { Name = "errors_2", IsComplex = true, QueryBuilderType = typeof(auth_ErrorBuilder) },
            new FieldMetadata { Name = "value", IsComplex = true, QueryBuilderType = typeof(auth_CreatedStatusBuilder) },
            new FieldMetadata { Name = "isSuccess" }
        };

        protected override string TypeName { get; } = "auth_ResultOfCreatedStatus";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_ResultOfCreatedStatusBuilder status() => WithScalarField("status");

        public auth_ResultOfCreatedStatusBuilder errors() => WithScalarField("errors");

        public auth_ResultOfCreatedStatusBuilder errors_2(auth_ErrorBuilder auth_ErrorBuilder) => WithObjectField("errors_2", auth_ErrorBuilder);

        public auth_ResultOfCreatedStatusBuilder value(auth_CreatedStatusBuilder auth_CreatedStatusBuilder) => WithObjectField("value", auth_CreatedStatusBuilder);

        public auth_ResultOfCreatedStatusBuilder isSuccess() => WithScalarField("isSuccess");
    }

    public class auth_ResultBuilder : GraphQlQueryBuilder<auth_ResultBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "status" },
            new FieldMetadata { Name = "errors", IsComplex = true },
            new FieldMetadata { Name = "errors_2", IsComplex = true, QueryBuilderType = typeof(auth_ErrorBuilder) },
            new FieldMetadata { Name = "isSuccess" }
        };

        protected override string TypeName { get; } = "auth_Result";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_ResultBuilder status() => WithScalarField("status");

        public auth_ResultBuilder errors() => WithScalarField("errors");

        public auth_ResultBuilder errors_2(auth_ErrorBuilder auth_ErrorBuilder) => WithObjectField("errors_2", auth_ErrorBuilder);

        public auth_ResultBuilder isSuccess() => WithScalarField("isSuccess");
    }

    public class auth_SSOConfigBuilder : GraphQlQueryBuilder<auth_SSOConfigBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "idClaim" },
            new FieldMetadata { Name = "keyUrlClaim" },
            new FieldMetadata { Name = "keyUrl" },
            new FieldMetadata { Name = "claimsMap", IsComplex = true, QueryBuilderType = typeof(auth_KeyValuePairOfStringAndStringBuilder) },
            new FieldMetadata { Name = "additionalClaims", IsComplex = true, QueryBuilderType = typeof(auth_KeyValuePairOfStringAndListOfStringBuilder) },
            new FieldMetadata { Name = "clientId" },
            new FieldMetadata { Name = "clientSecret" },
            new FieldMetadata { Name = "validateExistingLoginByEmail" },
            new FieldMetadata { Name = "tenantId" },
            new FieldMetadata { Name = "useIdentityToken" },
            new FieldMetadata { Name = "clientIdClaim" },
            new FieldMetadata { Name = "autoProvisionUser" },
            new FieldMetadata { Name = "userNameClaims", IsComplex = true },
            new FieldMetadata { Name = "autoAssignRoles" },
            new FieldMetadata { Name = "rolesClaim" },
            new FieldMetadata { Name = "externalRolesMatching", IsComplex = true, QueryBuilderType = typeof(auth_KeyValuePairOfStringAndStringBuilder) },
            new FieldMetadata { Name = "defaultRoles", IsComplex = true },
            new FieldMetadata { Name = "providerId" },
            new FieldMetadata { Name = "identityProviderHint" },
            new FieldMetadata { Name = "errorPath" },
            new FieldMetadata { Name = "disableClientIdValidation" }
        };

        protected override string TypeName { get; } = "auth_SSOConfig";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_SSOConfigBuilder id() => WithScalarField("id");

        public auth_SSOConfigBuilder idClaim() => WithScalarField("idClaim");

        public auth_SSOConfigBuilder keyUrlClaim() => WithScalarField("keyUrlClaim");

        public auth_SSOConfigBuilder keyUrl() => WithScalarField("keyUrl");

        public auth_SSOConfigBuilder claimsMap(auth_KeyValuePairOfStringAndStringBuilder auth_KeyValuePairOfStringAndStringBuilder) => WithObjectField("claimsMap", auth_KeyValuePairOfStringAndStringBuilder);

        public auth_SSOConfigBuilder additionalClaims(auth_KeyValuePairOfStringAndListOfStringBuilder auth_KeyValuePairOfStringAndListOfStringBuilder) => WithObjectField("additionalClaims", auth_KeyValuePairOfStringAndListOfStringBuilder);

        public auth_SSOConfigBuilder clientId() => WithScalarField("clientId");

        public auth_SSOConfigBuilder clientSecret() => WithScalarField("clientSecret");

        public auth_SSOConfigBuilder validateExistingLoginByEmail() => WithScalarField("validateExistingLoginByEmail");

        public auth_SSOConfigBuilder tenantId() => WithScalarField("tenantId");

        public auth_SSOConfigBuilder useIdentityToken() => WithScalarField("useIdentityToken");

        public auth_SSOConfigBuilder clientIdClaim() => WithScalarField("clientIdClaim");

        public auth_SSOConfigBuilder autoProvisionUser() => WithScalarField("autoProvisionUser");

        public auth_SSOConfigBuilder userNameClaims() => WithScalarField("userNameClaims");

        public auth_SSOConfigBuilder autoAssignRoles() => WithScalarField("autoAssignRoles");

        public auth_SSOConfigBuilder rolesClaim() => WithScalarField("rolesClaim");

        public auth_SSOConfigBuilder externalRolesMatching(auth_KeyValuePairOfStringAndStringBuilder auth_KeyValuePairOfStringAndStringBuilder) => WithObjectField("externalRolesMatching", auth_KeyValuePairOfStringAndStringBuilder);

        public auth_SSOConfigBuilder defaultRoles() => WithScalarField("defaultRoles");

        public auth_SSOConfigBuilder providerId() => WithScalarField("providerId");

        public auth_SSOConfigBuilder identityProviderHint() => WithScalarField("identityProviderHint");

        public auth_SSOConfigBuilder errorPath() => WithScalarField("errorPath");

        public auth_SSOConfigBuilder disableClientIdValidation() => WithScalarField("disableClientIdValidation");
    }

    public class auth_AppConfigurationBuilder : GraphQlQueryBuilder<auth_AppConfigurationBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "tenantId" },
            new FieldMetadata { Name = "appId" },
            new FieldMetadata { Name = "appConfig" },
            new FieldMetadata { Name = "otherApps", IsComplex = true, QueryBuilderType = typeof(AppUrlBuilder) }
        };

        protected override string TypeName { get; } = "auth_AppConfiguration";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_AppConfigurationBuilder tenantId() => WithScalarField("tenantId");

        public auth_AppConfigurationBuilder appId() => WithScalarField("appId");

        public auth_AppConfigurationBuilder appConfig() => WithScalarField("appConfig");

        public auth_AppConfigurationBuilder otherApps(AppUrlBuilder appUrlBuilder) => WithObjectField("otherApps", appUrlBuilder);
    }

    public class auth_KeyValuePairOfStringAndListOfStringBuilder : GraphQlQueryBuilder<auth_KeyValuePairOfStringAndListOfStringBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "key" },
            new FieldMetadata { Name = "value", IsComplex = true }
        };

        protected override string TypeName { get; } = "auth_KeyValuePairOfStringAndListOfString";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_KeyValuePairOfStringAndListOfStringBuilder key() => WithScalarField("key");

        public auth_KeyValuePairOfStringAndListOfStringBuilder value() => WithScalarField("value");
    }

    public class auth_KeyValuePairOfStringAndStringBuilder : GraphQlQueryBuilder<auth_KeyValuePairOfStringAndStringBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "key" },
            new FieldMetadata { Name = "value" }
        };

        protected override string TypeName { get; } = "auth_KeyValuePairOfStringAndString";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_KeyValuePairOfStringAndStringBuilder key() => WithScalarField("key");

        public auth_KeyValuePairOfStringAndStringBuilder value() => WithScalarField("value");
    }

    public class auth_CreatedStatusBuilder : GraphQlQueryBuilder<auth_CreatedStatusBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "ids", IsComplex = true }
        };

        protected override string TypeName { get; } = "auth_CreatedStatus";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_CreatedStatusBuilder id() => WithScalarField("id");

        public auth_CreatedStatusBuilder ids() => WithScalarField("ids");
    }

    public class auth_ErrorBuilder : GraphQlQueryBuilder<auth_ErrorBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "code" },
            new FieldMetadata { Name = "message" }
        };

        protected override string TypeName { get; } = "auth_Error";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public auth_ErrorBuilder code() => WithScalarField("code");

        public auth_ErrorBuilder message() => WithScalarField("message");
    }
    #endregion

    #region input classes
    public record auth_SSORefreshTokenInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _tokenEndpointUrl;
        private InputPropertyInfo _refreshToken;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public string? tokenEndpointUrl
        {
            get => (string?)_tokenEndpointUrl.Value;
            init => _tokenEndpointUrl = new InputPropertyInfo { Name = "tokenEndpointUrl", Value = value };
        }

        public string? refreshToken
        {
            get => (string?)_refreshToken.Value;
            init => _refreshToken = new InputPropertyInfo { Name = "refreshToken", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_tokenEndpointUrl.Name != null) yield return _tokenEndpointUrl;
            if (_refreshToken.Name != null) yield return _refreshToken;
        }
    }

    public record auth_SSOAuthCodeInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _tokenEndpointUrl;
        private InputPropertyInfo _redirectUrl;
        private InputPropertyInfo _authorizationCode;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public string? tokenEndpointUrl
        {
            get => (string?)_tokenEndpointUrl.Value;
            init => _tokenEndpointUrl = new InputPropertyInfo { Name = "tokenEndpointUrl", Value = value };
        }

        public string? redirectUrl
        {
            get => (string?)_redirectUrl.Value;
            init => _redirectUrl = new InputPropertyInfo { Name = "redirectUrl", Value = value };
        }

        public string? authorizationCode
        {
            get => (string?)_authorizationCode.Value;
            init => _authorizationCode = new InputPropertyInfo { Name = "authorizationCode", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_tokenEndpointUrl.Name != null) yield return _tokenEndpointUrl;
            if (_redirectUrl.Name != null) yield return _redirectUrl;
            if (_authorizationCode.Name != null) yield return _authorizationCode;
        }
    }

    public record auth_SendReactivateNotificationCommandInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _type;
        private InputPropertyInfo _fromEntityId;
        private InputPropertyInfo _toEntityId;
        private InputPropertyInfo _policyId;
        private InputPropertyInfo _offerId;
        private InputPropertyInfo _emailMessage;
        private InputPropertyInfo _useConfig;
        private InputPropertyInfo _sentById;
        private InputPropertyInfo _appId;
        private InputPropertyInfo _loginId;
        private InputPropertyInfo _callbackUrl;
        private InputPropertyInfo _redirectQueryString;
        private InputPropertyInfo _language;
        private InputPropertyInfo _scheduleToSendAt;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public string? type
        {
            get => (string?)_type.Value;
            init => _type = new InputPropertyInfo { Name = "type", Value = value };
        }

        public string? fromEntityId
        {
            get => (string?)_fromEntityId.Value;
            init => _fromEntityId = new InputPropertyInfo { Name = "fromEntityId", Value = value };
        }

        public string? toEntityId
        {
            get => (string?)_toEntityId.Value;
            init => _toEntityId = new InputPropertyInfo { Name = "toEntityId", Value = value };
        }

        public string? policyId
        {
            get => (string?)_policyId.Value;
            init => _policyId = new InputPropertyInfo { Name = "policyId", Value = value };
        }

        public string? offerId
        {
            get => (string?)_offerId.Value;
            init => _offerId = new InputPropertyInfo { Name = "offerId", Value = value };
        }

        public auth_SendReactivateEmailMessageInput? emailMessage
        {
            get => (auth_SendReactivateEmailMessageInput?)_emailMessage.Value;
            init => _emailMessage = new InputPropertyInfo { Name = "emailMessage", Value = value };
        }

        public bool? useConfig
        {
            get => (bool?)_useConfig.Value;
            init => _useConfig = new InputPropertyInfo { Name = "useConfig", Value = value };
        }

        public string? sentById
        {
            get => (string?)_sentById.Value;
            init => _sentById = new InputPropertyInfo { Name = "sentById", Value = value };
        }

        public string? appId
        {
            get => (string?)_appId.Value;
            init => _appId = new InputPropertyInfo { Name = "appId", Value = value };
        }

        public string? loginId
        {
            get => (string?)_loginId.Value;
            init => _loginId = new InputPropertyInfo { Name = "loginId", Value = value };
        }

        public string? callbackUrl
        {
            get => (string?)_callbackUrl.Value;
            init => _callbackUrl = new InputPropertyInfo { Name = "callbackUrl", Value = value };
        }

        public string? redirectQueryString
        {
            get => (string?)_redirectQueryString.Value;
            init => _redirectQueryString = new InputPropertyInfo { Name = "redirectQueryString", Value = value };
        }

        public string? language
        {
            get => (string?)_language.Value;
            init => _language = new InputPropertyInfo { Name = "language", Value = value };
        }

        public DateTime? scheduleToSendAt
        {
            get => (DateTime?)_scheduleToSendAt.Value;
            init => _scheduleToSendAt = new InputPropertyInfo { Name = "scheduleToSendAt", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_type.Name != null) yield return _type;
            if (_fromEntityId.Name != null) yield return _fromEntityId;
            if (_toEntityId.Name != null) yield return _toEntityId;
            if (_policyId.Name != null) yield return _policyId;
            if (_offerId.Name != null) yield return _offerId;
            if (_emailMessage.Name != null) yield return _emailMessage;
            if (_useConfig.Name != null) yield return _useConfig;
            if (_sentById.Name != null) yield return _sentById;
            if (_appId.Name != null) yield return _appId;
            if (_loginId.Name != null) yield return _loginId;
            if (_callbackUrl.Name != null) yield return _callbackUrl;
            if (_redirectQueryString.Name != null) yield return _redirectQueryString;
            if (_language.Name != null) yield return _language;
            if (_scheduleToSendAt.Name != null) yield return _scheduleToSendAt;
        }
    }

    public record auth_UpdatePasswordValidatorsCommandInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _clientId;
        private InputPropertyInfo _enableLockout;
        private InputPropertyInfo _lockoutTimespanInSeconds;
        private InputPropertyInfo _lockoutMaxFailedAccessAttempts;
        private InputPropertyInfo _lockoutEmailTemplateId;
        private InputPropertyInfo _requireConfirmedEmail;
        private InputPropertyInfo _requireConfirmPhoneNumber;
        private InputPropertyInfo _requireDigit;
        private InputPropertyInfo _requireLength;
        private InputPropertyInfo _requireUniqueChars;
        private InputPropertyInfo _requireLowercase;
        private InputPropertyInfo _requireUppercase;
        private InputPropertyInfo _requireNonAlphanumeric;
        private InputPropertyInfo _requireMaxConsecutiveRepeatingCharacters;
        private InputPropertyInfo _requireMaxIncrementalSequenceCharacters;
        private InputPropertyInfo _requireLetter;
        private InputPropertyInfo _exposeErrorMessage;
        private InputPropertyInfo _passwordLifespanInSeconds;
        private InputPropertyInfo _tempPasswordLifespanInSeonds;
        private InputPropertyInfo _savePasswordHistoryCount;
        private InputPropertyInfo _allowExpiredPasswordEasyReset;
        private InputPropertyInfo _passwordResetRequireDobVerification;
        private InputPropertyInfo _maxDobVerificationAttempts;
        private InputPropertyInfo _notContainUsername;
        private InputPropertyInfo _email2faLifespanInSeconds;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public string? clientId
        {
            get => (string?)_clientId.Value;
            init => _clientId = new InputPropertyInfo { Name = "clientId", Value = value };
        }

        public bool? enableLockout
        {
            get => (bool?)_enableLockout.Value;
            init => _enableLockout = new InputPropertyInfo { Name = "enableLockout", Value = value };
        }

        public int? lockoutTimespanInSeconds
        {
            get => (int?)_lockoutTimespanInSeconds.Value;
            init => _lockoutTimespanInSeconds = new InputPropertyInfo { Name = "lockoutTimespanInSeconds", Value = value };
        }

        public int? lockoutMaxFailedAccessAttempts
        {
            get => (int?)_lockoutMaxFailedAccessAttempts.Value;
            init => _lockoutMaxFailedAccessAttempts = new InputPropertyInfo { Name = "lockoutMaxFailedAccessAttempts", Value = value };
        }

        public string? lockoutEmailTemplateId
        {
            get => (string?)_lockoutEmailTemplateId.Value;
            init => _lockoutEmailTemplateId = new InputPropertyInfo { Name = "lockoutEmailTemplateId", Value = value };
        }

        public bool? requireConfirmedEmail
        {
            get => (bool?)_requireConfirmedEmail.Value;
            init => _requireConfirmedEmail = new InputPropertyInfo { Name = "requireConfirmedEmail", Value = value };
        }

        public bool? requireConfirmPhoneNumber
        {
            get => (bool?)_requireConfirmPhoneNumber.Value;
            init => _requireConfirmPhoneNumber = new InputPropertyInfo { Name = "requireConfirmPhoneNumber", Value = value };
        }

        public bool? requireDigit
        {
            get => (bool?)_requireDigit.Value;
            init => _requireDigit = new InputPropertyInfo { Name = "requireDigit", Value = value };
        }

        public int? requireLength
        {
            get => (int?)_requireLength.Value;
            init => _requireLength = new InputPropertyInfo { Name = "requireLength", Value = value };
        }

        public int? requireUniqueChars
        {
            get => (int?)_requireUniqueChars.Value;
            init => _requireUniqueChars = new InputPropertyInfo { Name = "requireUniqueChars", Value = value };
        }

        public bool? requireLowercase
        {
            get => (bool?)_requireLowercase.Value;
            init => _requireLowercase = new InputPropertyInfo { Name = "requireLowercase", Value = value };
        }

        public bool? requireUppercase
        {
            get => (bool?)_requireUppercase.Value;
            init => _requireUppercase = new InputPropertyInfo { Name = "requireUppercase", Value = value };
        }

        public bool? requireNonAlphanumeric
        {
            get => (bool?)_requireNonAlphanumeric.Value;
            init => _requireNonAlphanumeric = new InputPropertyInfo { Name = "requireNonAlphanumeric", Value = value };
        }

        public bool? requireMaxConsecutiveRepeatingCharacters
        {
            get => (bool?)_requireMaxConsecutiveRepeatingCharacters.Value;
            init => _requireMaxConsecutiveRepeatingCharacters = new InputPropertyInfo { Name = "requireMaxConsecutiveRepeatingCharacters", Value = value };
        }

        public bool? requireMaxIncrementalSequenceCharacters
        {
            get => (bool?)_requireMaxIncrementalSequenceCharacters.Value;
            init => _requireMaxIncrementalSequenceCharacters = new InputPropertyInfo { Name = "requireMaxIncrementalSequenceCharacters", Value = value };
        }

        public bool? requireLetter
        {
            get => (bool?)_requireLetter.Value;
            init => _requireLetter = new InputPropertyInfo { Name = "requireLetter", Value = value };
        }

        public bool? exposeErrorMessage
        {
            get => (bool?)_exposeErrorMessage.Value;
            init => _exposeErrorMessage = new InputPropertyInfo { Name = "exposeErrorMessage", Value = value };
        }

        public int? passwordLifespanInSeconds
        {
            get => (int?)_passwordLifespanInSeconds.Value;
            init => _passwordLifespanInSeconds = new InputPropertyInfo { Name = "passwordLifespanInSeconds", Value = value };
        }

        public int? tempPasswordLifespanInSeonds
        {
            get => (int?)_tempPasswordLifespanInSeonds.Value;
            init => _tempPasswordLifespanInSeonds = new InputPropertyInfo { Name = "tempPasswordLifespanInSeonds", Value = value };
        }

        public int? savePasswordHistoryCount
        {
            get => (int?)_savePasswordHistoryCount.Value;
            init => _savePasswordHistoryCount = new InputPropertyInfo { Name = "savePasswordHistoryCount", Value = value };
        }

        public bool? allowExpiredPasswordEasyReset
        {
            get => (bool?)_allowExpiredPasswordEasyReset.Value;
            init => _allowExpiredPasswordEasyReset = new InputPropertyInfo { Name = "allowExpiredPasswordEasyReset", Value = value };
        }

        public bool? passwordResetRequireDobVerification
        {
            get => (bool?)_passwordResetRequireDobVerification.Value;
            init => _passwordResetRequireDobVerification = new InputPropertyInfo { Name = "passwordResetRequireDobVerification", Value = value };
        }

        public int? maxDobVerificationAttempts
        {
            get => (int?)_maxDobVerificationAttempts.Value;
            init => _maxDobVerificationAttempts = new InputPropertyInfo { Name = "maxDobVerificationAttempts", Value = value };
        }

        public bool? notContainUsername
        {
            get => (bool?)_notContainUsername.Value;
            init => _notContainUsername = new InputPropertyInfo { Name = "notContainUsername", Value = value };
        }

        public int? email2faLifespanInSeconds
        {
            get => (int?)_email2faLifespanInSeconds.Value;
            init => _email2faLifespanInSeconds = new InputPropertyInfo { Name = "email2faLifespanInSeconds", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_clientId.Name != null) yield return _clientId;
            if (_enableLockout.Name != null) yield return _enableLockout;
            if (_lockoutTimespanInSeconds.Name != null) yield return _lockoutTimespanInSeconds;
            if (_lockoutMaxFailedAccessAttempts.Name != null) yield return _lockoutMaxFailedAccessAttempts;
            if (_lockoutEmailTemplateId.Name != null) yield return _lockoutEmailTemplateId;
            if (_requireConfirmedEmail.Name != null) yield return _requireConfirmedEmail;
            if (_requireConfirmPhoneNumber.Name != null) yield return _requireConfirmPhoneNumber;
            if (_requireDigit.Name != null) yield return _requireDigit;
            if (_requireLength.Name != null) yield return _requireLength;
            if (_requireUniqueChars.Name != null) yield return _requireUniqueChars;
            if (_requireLowercase.Name != null) yield return _requireLowercase;
            if (_requireUppercase.Name != null) yield return _requireUppercase;
            if (_requireNonAlphanumeric.Name != null) yield return _requireNonAlphanumeric;
            if (_requireMaxConsecutiveRepeatingCharacters.Name != null) yield return _requireMaxConsecutiveRepeatingCharacters;
            if (_requireMaxIncrementalSequenceCharacters.Name != null) yield return _requireMaxIncrementalSequenceCharacters;
            if (_requireLetter.Name != null) yield return _requireLetter;
            if (_exposeErrorMessage.Name != null) yield return _exposeErrorMessage;
            if (_passwordLifespanInSeconds.Name != null) yield return _passwordLifespanInSeconds;
            if (_tempPasswordLifespanInSeonds.Name != null) yield return _tempPasswordLifespanInSeonds;
            if (_savePasswordHistoryCount.Name != null) yield return _savePasswordHistoryCount;
            if (_allowExpiredPasswordEasyReset.Name != null) yield return _allowExpiredPasswordEasyReset;
            if (_passwordResetRequireDobVerification.Name != null) yield return _passwordResetRequireDobVerification;
            if (_maxDobVerificationAttempts.Name != null) yield return _maxDobVerificationAttempts;
            if (_notContainUsername.Name != null) yield return _notContainUsername;
            if (_email2faLifespanInSeconds.Name != null) yield return _email2faLifespanInSeconds;
        }
    }

    public record auth_SSOConfigInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _idClaim;
        private InputPropertyInfo _keyUrlClaim;
        private InputPropertyInfo _keyUrl;
        private InputPropertyInfo _claimsMap;
        private InputPropertyInfo _additionalClaims;
        private InputPropertyInfo _clientId;
        private InputPropertyInfo _clientSecret;
        private InputPropertyInfo _validateExistingLoginByEmail;
        private InputPropertyInfo _tenantId;
        private InputPropertyInfo _useIdentityToken;
        private InputPropertyInfo _clientIdClaim;
        private InputPropertyInfo _autoProvisionUser;
        private InputPropertyInfo _userNameClaims;
        private InputPropertyInfo _autoAssignRoles;
        private InputPropertyInfo _rolesClaim;
        private InputPropertyInfo _externalRolesMatching;
        private InputPropertyInfo _defaultRoles;
        private InputPropertyInfo _providerId;
        private InputPropertyInfo _identityProviderHint;
        private InputPropertyInfo _errorPath;
        private InputPropertyInfo _disableClientIdValidation;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public string? idClaim
        {
            get => (string?)_idClaim.Value;
            init => _idClaim = new InputPropertyInfo { Name = "idClaim", Value = value };
        }

        public string? keyUrlClaim
        {
            get => (string?)_keyUrlClaim.Value;
            init => _keyUrlClaim = new InputPropertyInfo { Name = "keyUrlClaim", Value = value };
        }

        public string? keyUrl
        {
            get => (string?)_keyUrl.Value;
            init => _keyUrl = new InputPropertyInfo { Name = "keyUrl", Value = value };
        }

        public ICollection<auth_KeyValuePairOfStringAndStringInput>? claimsMap
        {
            get => (ICollection<auth_KeyValuePairOfStringAndStringInput>?)_claimsMap.Value;
            init => _claimsMap = new InputPropertyInfo { Name = "claimsMap", Value = value };
        }

        public ICollection<auth_KeyValuePairOfStringAndListOfStringInput>? additionalClaims
        {
            get => (ICollection<auth_KeyValuePairOfStringAndListOfStringInput>?)_additionalClaims.Value;
            init => _additionalClaims = new InputPropertyInfo { Name = "additionalClaims", Value = value };
        }

        public string? clientId
        {
            get => (string?)_clientId.Value;
            init => _clientId = new InputPropertyInfo { Name = "clientId", Value = value };
        }

        public string? clientSecret
        {
            get => (string?)_clientSecret.Value;
            init => _clientSecret = new InputPropertyInfo { Name = "clientSecret", Value = value };
        }

        public bool? validateExistingLoginByEmail
        {
            get => (bool?)_validateExistingLoginByEmail.Value;
            init => _validateExistingLoginByEmail = new InputPropertyInfo { Name = "validateExistingLoginByEmail", Value = value };
        }

        public string? tenantId
        {
            get => (string?)_tenantId.Value;
            init => _tenantId = new InputPropertyInfo { Name = "tenantId", Value = value };
        }

        public bool? useIdentityToken
        {
            get => (bool?)_useIdentityToken.Value;
            init => _useIdentityToken = new InputPropertyInfo { Name = "useIdentityToken", Value = value };
        }

        public string? clientIdClaim
        {
            get => (string?)_clientIdClaim.Value;
            init => _clientIdClaim = new InputPropertyInfo { Name = "clientIdClaim", Value = value };
        }

        public bool? autoProvisionUser
        {
            get => (bool?)_autoProvisionUser.Value;
            init => _autoProvisionUser = new InputPropertyInfo { Name = "autoProvisionUser", Value = value };
        }

        public ICollection<string?>? userNameClaims
        {
            get => (ICollection<string?>?)_userNameClaims.Value;
            init => _userNameClaims = new InputPropertyInfo { Name = "userNameClaims", Value = value };
        }

        public bool? autoAssignRoles
        {
            get => (bool?)_autoAssignRoles.Value;
            init => _autoAssignRoles = new InputPropertyInfo { Name = "autoAssignRoles", Value = value };
        }

        public string? rolesClaim
        {
            get => (string?)_rolesClaim.Value;
            init => _rolesClaim = new InputPropertyInfo { Name = "rolesClaim", Value = value };
        }

        public ICollection<auth_KeyValuePairOfStringAndStringInput>? externalRolesMatching
        {
            get => (ICollection<auth_KeyValuePairOfStringAndStringInput>?)_externalRolesMatching.Value;
            init => _externalRolesMatching = new InputPropertyInfo { Name = "externalRolesMatching", Value = value };
        }

        public ICollection<string?>? defaultRoles
        {
            get => (ICollection<string?>?)_defaultRoles.Value;
            init => _defaultRoles = new InputPropertyInfo { Name = "defaultRoles", Value = value };
        }

        public string? providerId
        {
            get => (string?)_providerId.Value;
            init => _providerId = new InputPropertyInfo { Name = "providerId", Value = value };
        }

        public string? identityProviderHint
        {
            get => (string?)_identityProviderHint.Value;
            init => _identityProviderHint = new InputPropertyInfo { Name = "identityProviderHint", Value = value };
        }

        public string? errorPath
        {
            get => (string?)_errorPath.Value;
            init => _errorPath = new InputPropertyInfo { Name = "errorPath", Value = value };
        }

        public bool? disableClientIdValidation
        {
            get => (bool?)_disableClientIdValidation.Value;
            init => _disableClientIdValidation = new InputPropertyInfo { Name = "disableClientIdValidation", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_idClaim.Name != null) yield return _idClaim;
            if (_keyUrlClaim.Name != null) yield return _keyUrlClaim;
            if (_keyUrl.Name != null) yield return _keyUrl;
            if (_claimsMap.Name != null) yield return _claimsMap;
            if (_additionalClaims.Name != null) yield return _additionalClaims;
            if (_clientId.Name != null) yield return _clientId;
            if (_clientSecret.Name != null) yield return _clientSecret;
            if (_validateExistingLoginByEmail.Name != null) yield return _validateExistingLoginByEmail;
            if (_tenantId.Name != null) yield return _tenantId;
            if (_useIdentityToken.Name != null) yield return _useIdentityToken;
            if (_clientIdClaim.Name != null) yield return _clientIdClaim;
            if (_autoProvisionUser.Name != null) yield return _autoProvisionUser;
            if (_userNameClaims.Name != null) yield return _userNameClaims;
            if (_autoAssignRoles.Name != null) yield return _autoAssignRoles;
            if (_rolesClaim.Name != null) yield return _rolesClaim;
            if (_externalRolesMatching.Name != null) yield return _externalRolesMatching;
            if (_defaultRoles.Name != null) yield return _defaultRoles;
            if (_providerId.Name != null) yield return _providerId;
            if (_identityProviderHint.Name != null) yield return _identityProviderHint;
            if (_errorPath.Name != null) yield return _errorPath;
            if (_disableClientIdValidation.Name != null) yield return _disableClientIdValidation;
        }
    }

    public record auth_KeyValuePairOfStringAndListOfStringInput : IGraphQlInputObject
    {
        private InputPropertyInfo _key;
        private InputPropertyInfo _value;

        public string? key
        {
            get => (string?)_key.Value;
            init => _key = new InputPropertyInfo { Name = "key", Value = value };
        }

        public ICollection<string>? value
        {
            get => (ICollection<string>?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_key.Name != null) yield return _key;
            if (_value.Name != null) yield return _value;
        }
    }

    public record auth_KeyValuePairOfStringAndStringInput : IGraphQlInputObject
    {
        private InputPropertyInfo _key;
        private InputPropertyInfo _value;

        public string? key
        {
            get => (string?)_key.Value;
            init => _key = new InputPropertyInfo { Name = "key", Value = value };
        }

        public string? value
        {
            get => (string?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_key.Name != null) yield return _key;
            if (_value.Name != null) yield return _value;
        }
    }

    public record auth_SendReactivateEmailMessageInput : IGraphQlInputObject
    {
        private InputPropertyInfo _from;
        private InputPropertyInfo _fromName;
        private InputPropertyInfo _to;
        private InputPropertyInfo _tos;
        private InputPropertyInfo _ccs;
        private InputPropertyInfo _bccs;
        private InputPropertyInfo _subject;
        private InputPropertyInfo _htmlContent;
        private InputPropertyInfo _templateRendering;

        public string? from
        {
            get => (string?)_from.Value;
            init => _from = new InputPropertyInfo { Name = "from", Value = value };
        }

        public string? fromName
        {
            get => (string?)_fromName.Value;
            init => _fromName = new InputPropertyInfo { Name = "fromName", Value = value };
        }

        public string? to
        {
            get => (string?)_to.Value;
            init => _to = new InputPropertyInfo { Name = "to", Value = value };
        }

        public ICollection<string?>? tos
        {
            get => (ICollection<string?>?)_tos.Value;
            init => _tos = new InputPropertyInfo { Name = "tos", Value = value };
        }

        public ICollection<string?>? ccs
        {
            get => (ICollection<string?>?)_ccs.Value;
            init => _ccs = new InputPropertyInfo { Name = "ccs", Value = value };
        }

        public ICollection<string?>? bccs
        {
            get => (ICollection<string?>?)_bccs.Value;
            init => _bccs = new InputPropertyInfo { Name = "bccs", Value = value };
        }

        public string? subject
        {
            get => (string?)_subject.Value;
            init => _subject = new InputPropertyInfo { Name = "subject", Value = value };
        }

        public string? htmlContent
        {
            get => (string?)_htmlContent.Value;
            init => _htmlContent = new InputPropertyInfo { Name = "htmlContent", Value = value };
        }

        public auth_SendReactivateTemplateRenderingInput? templateRendering
        {
            get => (auth_SendReactivateTemplateRenderingInput?)_templateRendering.Value;
            init => _templateRendering = new InputPropertyInfo { Name = "templateRendering", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_from.Name != null) yield return _from;
            if (_fromName.Name != null) yield return _fromName;
            if (_to.Name != null) yield return _to;
            if (_tos.Name != null) yield return _tos;
            if (_ccs.Name != null) yield return _ccs;
            if (_bccs.Name != null) yield return _bccs;
            if (_subject.Name != null) yield return _subject;
            if (_htmlContent.Name != null) yield return _htmlContent;
            if (_templateRendering.Name != null) yield return _templateRendering;
        }
    }

    public record auth_SendReactivateTemplateRenderingInput : IGraphQlInputObject
    {
        private InputPropertyInfo _templateId;
        private InputPropertyInfo _input;

        public string? templateId
        {
            get => (string?)_templateId.Value;
            init => _templateId = new InputPropertyInfo { Name = "templateId", Value = value };
        }

        public auth_SendReactivateRenderParametersInput? input
        {
            get => (auth_SendReactivateRenderParametersInput?)_input.Value;
            init => _input = new InputPropertyInfo { Name = "input", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_templateId.Name != null) yield return _templateId;
            if (_input.Name != null) yield return _input;
        }
    }

    public record auth_SendReactivateRenderParametersInput : IGraphQlInputObject
    {
        private InputPropertyInfo _name;
        private InputPropertyInfo _contentJsonString;
        private InputPropertyInfo _url;
        private InputPropertyInfo _accessToken;
        private InputPropertyInfo _variablesJsonString;

        public string? name
        {
            get => (string?)_name.Value;
            init => _name = new InputPropertyInfo { Name = "name", Value = value };
        }

        public string? contentJsonString
        {
            get => (string?)_contentJsonString.Value;
            init => _contentJsonString = new InputPropertyInfo { Name = "contentJsonString", Value = value };
        }

        public string? url
        {
            get => (string?)_url.Value;
            init => _url = new InputPropertyInfo { Name = "url", Value = value };
        }

        public string? accessToken
        {
            get => (string?)_accessToken.Value;
            init => _accessToken = new InputPropertyInfo { Name = "accessToken", Value = value };
        }

        public string? variablesJsonString
        {
            get => (string?)_variablesJsonString.Value;
            init => _variablesJsonString = new InputPropertyInfo { Name = "variablesJsonString", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_name.Name != null) yield return _name;
            if (_contentJsonString.Name != null) yield return _contentJsonString;
            if (_url.Name != null) yield return _url;
            if (_accessToken.Name != null) yield return _accessToken;
            if (_variablesJsonString.Name != null) yield return _variablesJsonString;
        }
    }
    #endregion

    #region data classes
    public record _SchemaDefinition
    {
        public string? name { get; init; }
        public string? document { get; init; }
        public ICollection<string>? extensionDocuments { get; init; }
    }

    public record _service_CoverGoQueriesRoot
    {
        public _SchemaDefinition? _schemaDefinition { get; init; }
        public auth_SSOTokenReponse? sSOAccessTokenQuerySSOAccessToken { get; init; }
        public auth_SSOTokenReponse? sSOAccessTokenQuerySSORefreshToken { get; init; }
        public auth_SSOConfig? sSOConfigsQuerySSOConfigById { get; init; }
        public auth_SSOConfig? sSOConfigsQueryConfigByIdAndClientId { get; init; }
        public bool? loginInternalQueryDoesActiveLoginExistByEmail { get; init; }
        public auth_AppConfiguration? appConfigurationQueryAppConfigurationFromUrl { get; init; }
    }

    public record _service_CoverGoMutationsRoot
    {
        public auth_Result? updatePasswordValidatorsMutationUpdatePasswordValidators { get; init; }
        public auth_ResultOfCreatedStatus? updatePasswordValidatorsMutationCreatePasswordValidators { get; init; }
        public auth_Result? loginMutationDeactivate { get; init; }
        public auth_Result? loginMutationReactivate { get; init; }
        public auth_Result? sSOConfigsMutationAddSSOConfig { get; init; }
        public auth_Result? sSOConfigsMutationRemoveSSOConfig { get; init; }
    }

    public record AppUrl
    {
        public string? appId { get; init; }
        public auth_UrlRouting? urlRouting { get; init; }
    }

    public record auth_UrlRouting
    {
        public string? url { get; init; }
        public string? regexPattern { get; init; }
        public int? order { get; init; }
    }

    public record auth_SSOTokenReponse
    {
        public string? accessToken { get; init; }
        public string? refreshToken { get; init; }
        public int? expiresIn { get; init; }
    }

    public record auth_ResultOfCreatedStatus
    {
        public string? status { get; init; }
        public ICollection<string?>? errors { get; init; }
        public ICollection<auth_Error?>? errors_2 { get; init; }
        public auth_CreatedStatus? value { get; init; }
        public bool? isSuccess { get; init; }
    }

    public record auth_Result
    {
        public string? status { get; init; }
        public ICollection<string?>? errors { get; init; }
        public ICollection<auth_Error?>? errors_2 { get; init; }
        public bool? isSuccess { get; init; }
    }

    public record auth_SSOConfig
    {
        public string? id { get; init; }
        public string? idClaim { get; init; }
        public string? keyUrlClaim { get; init; }
        public string? keyUrl { get; init; }
        public ICollection<auth_KeyValuePairOfStringAndString>? claimsMap { get; init; }
        public ICollection<auth_KeyValuePairOfStringAndListOfString>? additionalClaims { get; init; }
        public string? clientId { get; init; }
        public string? clientSecret { get; init; }
        public bool? validateExistingLoginByEmail { get; init; }
        public string? tenantId { get; init; }
        public bool? useIdentityToken { get; init; }
        public string? clientIdClaim { get; init; }
        public bool? autoProvisionUser { get; init; }
        public ICollection<string?>? userNameClaims { get; init; }
        public bool? autoAssignRoles { get; init; }
        public string? rolesClaim { get; init; }
        public ICollection<auth_KeyValuePairOfStringAndString>? externalRolesMatching { get; init; }
        public ICollection<string?>? defaultRoles { get; init; }
        public string? providerId { get; init; }
        public string? identityProviderHint { get; init; }
        public string? errorPath { get; init; }
        public bool? disableClientIdValidation { get; init; }
    }

    public record auth_AppConfiguration
    {
        public string? tenantId { get; init; }
        public string? appId { get; init; }
        public string? appConfig { get; init; }
        public ICollection<AppUrl?>? otherApps { get; init; }
    }

    public record auth_KeyValuePairOfStringAndListOfString
    {
        public string? key { get; init; }
        public ICollection<string>? value { get; init; }
    }

    public record auth_KeyValuePairOfStringAndString
    {
        public string? key { get; init; }
        public string? value { get; init; }
    }

    public record auth_CreatedStatus
    {
        public string? id { get; init; }
        public ICollection<string?>? ids { get; init; }
    }

    public record auth_Error
    {
        public string? code { get; init; }
        public string? message { get; init; }
    }
    #endregion
#nullable restore
}
