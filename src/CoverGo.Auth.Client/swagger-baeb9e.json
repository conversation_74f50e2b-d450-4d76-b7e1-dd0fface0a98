{"openapi": "3.0.1", "info": {"title": "<PERSON><PERSON><PERSON>", "version": "v1"}, "paths": {"/api/v1/auth/apps/query": {"post": {"tags": ["App"], "operationId": "queryAll", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AppQueryArguments"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AppQueryArguments"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AppQueryArguments"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AppQueryArguments"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}}}}}}}, "/api/v1/auth/apps/events": {"post": {"tags": ["App"], "operationId": "events", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventLog"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventLog"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventLog"}}}}}}}}, "/api/v1/auth/apps/totalCount": {"post": {"tags": ["App"], "operationId": "totalCount", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AppWhere"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AppWhere"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AppWhere"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AppWhere"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/auth/apps/create": {"post": {"tags": ["App"], "operationId": "create", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateAppCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateAppCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAppCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAppCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/apps/{appId}/update": {"post": {"tags": ["App"], "operationId": "update", "parameters": [{"name": "appId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateAppCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateAppCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAppCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAppCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/apps/{appId}/delete": {"post": {"tags": ["App"], "operationId": "delete", "parameters": [{"name": "appId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/tokenasloginid/{loginId}/{appId}": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "tokenasloginidGET", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "appId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Token"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Token"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}}}, "post": {"tags": ["<PERSON><PERSON>"], "operationId": "tokenasloginidPOST", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "appId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "object", "nullable": true, "additionalProperties": {"type": "string"}}}, "application/json": {"schema": {"type": "object", "nullable": true, "additionalProperties": {"type": "string"}}}, "text/json": {"schema": {"type": "object", "nullable": true, "additionalProperties": {"type": "string"}}}, "application/*+json": {"schema": {"type": "object", "nullable": true, "additionalProperties": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Token"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Token"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}}}}, "/api/v1/auth/tenantSettings": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "tenantSettings", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantSettings"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantSettings"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantSettings"}}}}}}}, "/api/v1/auth/tenantSettings/hosts/add": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "add", "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "string", "nullable": true}}, "application/json": {"schema": {"type": "string", "nullable": true}}, "text/json": {"schema": {"type": "string", "nullable": true}}, "application/*+json": {"schema": {"type": "string", "nullable": true}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/tenantSettings/hosts/remove": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "remove", "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "string", "nullable": true}}, "application/json": {"schema": {"type": "string", "nullable": true}}, "text/json": {"schema": {"type": "string", "nullable": true}}, "application/*+json": {"schema": {"type": "string", "nullable": true}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/createTenant": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "createTenant", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateTenantCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateTenantCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTenantCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTenantCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/passwordvalidators": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "passwordvalidators", "parameters": [{"name": "clientId", "in": "query", "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PasswordValidators"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PasswordValidators"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PasswordValidators"}}}}}}}, "/api/v1/auth/passwordValidators/validate": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "validate", "parameters": [{"name": "clientId", "in": "query", "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ValidatePasswordCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ValidatePasswordCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidatePasswordCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ValidatePasswordCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/queryids": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "queryids", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LoginQueryArguments"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginQueryArguments"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginQueryArguments"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginQueryArguments"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/v1/auth/logins/fromLoginId/{loginId}": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "fromLoginId", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "appId", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "version", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Login"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Login"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Login"}}}}}}}, "/api/v1/auth/logins/filter": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "filter", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LoginQueryArguments"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginQueryArguments"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginQueryArguments"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginQueryArguments"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Login"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Login"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Login"}}}}}}}}, "/api/v1/auth/logins/totalCount": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "totalCount2", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LoginWhere"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginWhere"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginWhere"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginWhere"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/auth/logins/events": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "events2", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventLog"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventLog"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventLog"}}}}}}}}, "/api/v1/auth/logins/event": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "event", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LoginEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginEvent"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginEvent"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginEvent"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/fromUsername/{username}": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "fromUsername", "parameters": [{"name": "username", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "appId", "in": "query", "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Login"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Login"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Login"}}}}}}}, "/api/v1/auth/logins": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "logins", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateLoginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateLoginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateLoginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateLoginCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreatedStatusResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreatedStatusResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatedStatusResult"}}}}}}}, "/api/v1/auth/logins/{loginId}/sendCode": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "sendCode", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SendCodeCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendCodeCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendCodeCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendCodeCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/{loginId}/verifycode": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "verifycode", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VerifyCodeCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerifyCodeCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyCodeCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyCodeCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/{loginId}/verifyResetPasswordCode": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "verifyResetPasswordCode", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VerifyCodeCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerifyCodeCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyCodeCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyCodeCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/{loginId}/resendConfirmationEmail": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "resendConfirmationEmail", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ResendEmailCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResendEmailCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResendEmailCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResendEmailCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/{loginId}/update": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "update2", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateLoginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateLoginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateLoginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateLoginCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/{loginId}/delete": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "delete2", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/{loginId}/confirmEmail": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "confirmEmail", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ConfirmEmailCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ConfirmEmailCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConfirmEmailCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConfirmEmailCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResult"}}}}}}}, "/api/v1/auth/logins/{loginId}/resetPassword": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "resetPassword", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/forgotPassword": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "forgotPassword", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/{loginId}/changepassword": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "changepassword", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/changeExpiredPassword": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "changeExpiredPassword", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ChangeExpiredPasswordCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChangeExpiredPasswordCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeExpiredPasswordCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeExpiredPasswordCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/{loginId}/updateLockoutEndDate": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "updateLockoutEndDate", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ChangeUserLockoutDateCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChangeUserLockoutDateCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeUserLockoutDateCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeUserLockoutDateCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/targetedPermissionSchemas/add": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "add2", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddTargetedPermissionSchemaToLoginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddTargetedPermissionSchemaToLoginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddTargetedPermissionSchemaToLoginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddTargetedPermissionSchemaToLoginCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/targetedPermissionSchemas/remove": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "remove2", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RemoveTargetedPermissionSchemaFromLoginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RemoveTargetedPermissionSchemaFromLoginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RemoveTargetedPermissionSchemaFromLoginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RemoveTargetedPermissionSchemaFromLoginCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/targetedPermissionSchemas": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "targetedPermissionSchemas", "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "nullable": true, "items": {"type": "string"}}}, "application/json": {"schema": {"type": "array", "nullable": true, "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "nullable": true, "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "nullable": true, "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TargetedPermissionSchema"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TargetedPermissionSchema"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TargetedPermissionSchema"}}}}}}}}, "/api/v1/auth/logins/scheduledNotifications": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "scheduledNotifications", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/logins/scheduledNotifications/reschedule": {"post": {"tags": ["<PERSON><PERSON>"], "operationId": "reschedule", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RescheduleNotificationCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RescheduleNotificationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RescheduleNotificationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RescheduleNotificationCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v2/auth/logins/events": {"post": {"tags": ["AuthV2"], "operationId": "events3", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LoginEventWhereQueryArguments"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginEventWhereQueryArguments"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginEventWhereQueryArguments"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginEventWhereQueryArguments"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DetailedEventLog"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DetailedEventLog"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DetailedEventLog"}}}}}}}}, "/tenants/initialize": {"post": {"tags": ["Obsole<PERSON><PERSON><PERSON><PERSON>"], "operationId": "initialize", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/InitializeTenantCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InitializeTenantCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InitializeTenantCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InitializeTenantCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}, "deprecated": true}}, "/tenants/identify/{url}": {"get": {"tags": ["Obsole<PERSON><PERSON><PERSON><PERSON>"], "operationId": "identify", "parameters": [{"name": "url", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantIdAndAppIdResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantIdAndAppIdResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantIdAndAppIdResult"}}}}}, "deprecated": true}}, "/api/v1/otpLogin/preOtpLogin": {"post": {"tags": ["Otp"], "operationId": "preOtpLogin", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreatePreOtpLoginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreatePreOtpLoginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePreOtpLoginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePreOtpLoginCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PreOtpLogin"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PreOtpLogin"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PreOtpLogin"}}}}}}}, "/api/v1/otpLogin/otpLogin": {"post": {"tags": ["Otp"], "operationId": "otpLogin", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateOtpLoginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateOtpLoginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOtpLoginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOtpLoginCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OtpLogin"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OtpLogin"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OtpLogin"}}}}}}}, "/api/v1/otpLogin/accessTokenFromOtp": {"post": {"tags": ["Otp"], "operationId": "accessTokenFromOtp", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateAccessTokenFromOtpLoginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateAccessTokenFromOtpLoginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAccessTokenFromOtpLoginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAccessTokenFromOtpLoginCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}}}, "/api/v1/otpLogin/sendRemarksOTP": {"get": {"tags": ["Otp"], "operationId": "sendRemarksOTP", "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "string", "nullable": true}}, "application/json": {"schema": {"type": "string", "nullable": true}}, "text/json": {"schema": {"type": "string", "nullable": true}}, "application/*+json": {"schema": {"type": "string", "nullable": true}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/otpLogin/validateRemarksOTP": {"post": {"tags": ["Otp"], "operationId": "validateRemarksOTP", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/OTPRemarks"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OTPRemarks"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OTPRemarks"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OTPRemarks"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/auth/permissions/{loginId}": {"post": {"tags": ["Permission"], "operationId": "permissionsPOST", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/AddTargettedPermissionCommand"}}}, "application/json": {"schema": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/AddTargettedPermissionCommand"}}}, "text/json": {"schema": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/AddTargettedPermissionCommand"}}}, "application/*+json": {"schema": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/AddTargettedPermissionCommand"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissions/{loginId}/{type}/{value}": {"delete": {"tags": ["Permission"], "operationId": "permissionsDELETE", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "type", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "value", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "removedById", "in": "query", "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/{loginId}/permissions/remove": {"post": {"tags": ["Permission"], "operationId": "remove3", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/RemoveTargettedPermissionCommand"}}}, "application/json": {"schema": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/RemoveTargettedPermissionCommand"}}}, "text/json": {"schema": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/RemoveTargettedPermissionCommand"}}}, "application/*+json": {"schema": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/RemoveTargettedPermissionCommand"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissions": {"get": {"tags": ["Permission"], "operationId": "permissionsAll", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/v1/logins/{loginId}/permissions": {"get": {"tags": ["Permission"], "operationId": "permissionsGET", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "names", "in": "query", "schema": {"type": "array", "nullable": true, "items": {"type": "string"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}}, "/api/v1/logins/permissions": {"get": {"tags": ["Permission"], "operationId": "permissionsGET2", "parameters": [{"name": "ids", "in": "query", "schema": {"type": "array", "nullable": true, "items": {"type": "string"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}}, "/api/v1/logins/permissions/verifyFullAccessForClientId": {"post": {"tags": ["Permission"], "operationId": "verifyFullAccessForClientId", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/FullAccessVerificationCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FullAccessVerificationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FullAccessVerificationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FullAccessVerificationCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/auth/permissiongroups": {"post": {"tags": ["PermissionGroups"], "operationId": "permissiongroupsPOST", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreatePermissionGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePermissionGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}, "get": {"tags": ["PermissionGroups"], "operationId": "permissiongroupsAll", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionGroup"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionGroup"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionGroup"}}}}}}}}, "/api/v1/auth/permissiongroups/{id}": {"put": {"tags": ["PermissionGroups"], "operationId": "permissiongroupsPUT", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}, "get": {"tags": ["PermissionGroups"], "operationId": "permissiongroupsGET", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PermissionGroup"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionGroup"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionGroup"}}}}}}}, "/api/v1/auth/permissiongroups/delete/{id}": {"post": {"tags": ["PermissionGroups"], "operationId": "delete3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissiongroups/query": {"post": {"tags": ["PermissionGroups"], "operationId": "queryAll2", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PermissionGroupWhere"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionGroupWhere"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionGroupWhere"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PermissionGroupWhere"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionGroup"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionGroup"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionGroup"}}}}}}}}, "/api/v1/auth/permissiongroups/events": {"post": {"tags": ["PermissionGroups"], "operationId": "events4", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EventQuery"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventLog"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventLog"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EventLog"}}}}}}}}, "/api/v1/auth/permissiongroups/{id}/permissions/add": {"post": {"tags": ["PermissionGroups"], "operationId": "add3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddPermissionToPermissionGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddPermissionToPermissionGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddPermissionToPermissionGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddPermissionToPermissionGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissiongroups/{id}/permissionGroups/add": {"post": {"tags": ["PermissionGroups"], "operationId": "add4", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddPermissionGroupToPermissionGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddPermissionGroupToPermissionGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddPermissionGroupToPermissionGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddPermissionGroupToPermissionGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissiongroups/{id}/permissions/remove": {"post": {"tags": ["PermissionGroups"], "operationId": "remove4", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RemovePermissionFromPermissionGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RemovePermissionFromPermissionGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RemovePermissionFromPermissionGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RemovePermissionFromPermissionGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissiongroups/{id}/permissionGroups/remove": {"post": {"tags": ["PermissionGroups"], "operationId": "remove5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RemovePermissionGroupFromPermissionGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RemovePermissionGroupFromPermissionGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RemovePermissionGroupFromPermissionGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RemovePermissionGroupFromPermissionGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissiongroups/{id}/loginPermissions/add": {"post": {"tags": ["PermissionGroups"], "operationId": "add5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddLoginPermissionsToPermissionGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddLoginPermissionsToPermissionGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddLoginPermissionsToPermissionGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddLoginPermissionsToPermissionGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissiongroups/{id}/loginPermissions/remove": {"post": {"tags": ["PermissionGroups"], "operationId": "remove6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RemoveLoginPermissionsFromPermissionGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RemoveLoginPermissionsFromPermissionGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RemoveLoginPermissionsFromPermissionGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RemoveLoginPermissionsFromPermissionGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissionSchemas/query": {"post": {"tags": ["PermissionSchemas"], "operationId": "queryAll3", "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PermissionSchemaWhereQueryArguments"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionSchemaWhereQueryArguments"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionSchemaWhereQueryArguments"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PermissionSchemaWhereQueryArguments"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionSchema"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionSchema"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionSchema"}}}}}}}}, "/api/v1/auth/permissionSchemas/totalCount": {"post": {"tags": ["PermissionSchemas"], "operationId": "totalCount3", "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PermissionSchemaWhere"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionSchemaWhere"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionSchemaWhere"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PermissionSchemaWhere"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/auth/permissionSchemas/create": {"post": {"tags": ["PermissionSchemas"], "operationId": "create2", "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreatePermissionSchemaCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionSchemaCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionSchemaCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePermissionSchemaCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreatedStatusResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreatedStatusResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatedStatusResult"}}}}}}}, "/api/v1/auth/permissionSchemas/update": {"post": {"tags": ["PermissionSchemas"], "operationId": "update3", "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionSchemaCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionSchemaCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionSchemaCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionSchemaCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/permissionSchemas/{permissionSchemaId}/delete": {"post": {"tags": ["PermissionSchemas"], "operationId": "delete4", "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "permissionSchemaId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/targetgroups": {"post": {"tags": ["TargetGroup"], "operationId": "targetgroupsPOST", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateTargetGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateTargetGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTargetGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTargetGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}, "get": {"tags": ["TargetGroup"], "operationId": "targetgroupsAll", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TargetGroup"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TargetGroup"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TargetGroup"}}}}}}}}, "/api/v1/auth/targetgroups/{id}": {"put": {"tags": ["TargetGroup"], "operationId": "targetgroupsPUT", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateTargetGroupCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateTargetGroupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTargetGroupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateTargetGroupCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}, "post": {"tags": ["TargetGroup"], "operationId": "targetgroupsPOST2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}, "get": {"tags": ["TargetGroup"], "operationId": "targetgroupsGET", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TargetGroup"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TargetGroup"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TargetGroup"}}}}}}}, "/api/v1/auth/targetgroups/{id}/user/{targetId}": {"post": {"tags": ["TargetGroup"], "operationId": "userPOST", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "targetId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}, "delete": {"tags": ["TargetGroup"], "operationId": "userDELETE", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "targetId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/targetgroups/{id}/targetgroup/{targetGroupId}": {"post": {"tags": ["TargetGroup"], "operationId": "targetgroupPOST", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "targetGroupId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}, "delete": {"tags": ["TargetGroup"], "operationId": "targetgroupDELETE", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "targetGroupId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v2/tenants/initialize": {"post": {"tags": ["Tenant"], "operationId": "initialize2", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/InitializeTenantCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InitializeTenantCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InitializeTenantCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InitializeTenantCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v2/tenants/identify/{url}": {"get": {"tags": ["Tenant"], "operationId": "identify2", "parameters": [{"name": "url", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantIdAndAppIdResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantIdAndAppIdResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantIdAndAppIdResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/auth/storage/{loginId}/count": {"post": {"tags": ["UserStorage"], "operationId": "count", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UserStorageItemWhereQueryArguments"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserStorageItemWhereQueryArguments"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserStorageItemWhereQueryArguments"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserStorageItemWhereQueryArguments"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int64Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int64Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int64Result"}}}}}}}, "/api/v1/auth/storage/{loginId}": {"post": {"tags": ["UserStorage"], "operationId": "storagePOST", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateUserStorageItemCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateUserStorageItemCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserStorageItemCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserStorageItemCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}, "put": {"tags": ["UserStorage"], "operationId": "storagePUT", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateUserStorageItemCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStorageItemCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStorageItemCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserStorageItemCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/storage/{loginId}/{storageItemKey}": {"delete": {"tags": ["UserStorage"], "operationId": "storageDELETE", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "storageItemKey", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/v1/auth/storage/{loginId}/query": {"post": {"tags": ["UserStorage"], "operationId": "query", "parameters": [{"name": "loginId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UserStorageItemWhereQueryArguments"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserStorageItemWhereQueryArguments"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserStorageItemWhereQueryArguments"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserStorageItemWhereQueryArguments"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserStorageItemIReadOnlyCollectionResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserStorageItemIReadOnlyCollectionResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserStorageItemIReadOnlyCollectionResult"}}}}}}}, "/api/auth/utils/indexstats": {"get": {"tags": ["Utils"], "operationId": "indexstats", "parameters": [{"name": "colName", "in": "query", "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}}, "components": {"schemas": {"AppWhere": {"type": "object", "additionalProperties": false, "properties": {"or": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/AppWhere"}}, "and": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/AppWhere"}}, "appId": {"type": "string", "nullable": true}, "appId_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "lastModifiedAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}, "lastModifiedById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "lastModifiedById_contains": {"type": "string", "nullable": true}, "createdById": {"type": "string", "nullable": true}, "createdById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "createdById_contains": {"type": "string", "nullable": true}}}, "OrderByType": {"type": "string", "enum": ["ASC", "DSC"]}, "OrderBy": {"type": "object", "additionalProperties": false, "properties": {"fieldName": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/OrderByType"}}}, "AppQueryArguments": {"type": "object", "additionalProperties": false, "properties": {"where": {"$ref": "#/components/schemas/AppWhere"}, "orderBy": {"$ref": "#/components/schemas/OrderBy"}, "first": {"type": "integer", "format": "int32", "nullable": true}, "skip": {"type": "integer", "format": "int32", "nullable": true}}}, "UrlRouting": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "regexPattern": {"type": "string", "nullable": true}, "order": {"type": "integer", "format": "int32"}}}, "AccessTokenType": {"type": "string", "enum": ["Jwt", "Reference"]}, "ClaimsIdentity": {"type": "object", "additionalProperties": false, "properties": {"authenticationType": {"type": "string", "nullable": true}, "isAuthenticated": {"type": "boolean", "readOnly": true}, "actor": {"$ref": "#/components/schemas/ClaimsIdentity"}, "bootstrapContext": {"nullable": true}, "claims": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Claim"}}, "label": {"type": "string", "nullable": true}, "name": {"type": "string", "readOnly": true, "nullable": true}, "nameClaimType": {"type": "string", "readOnly": true, "nullable": true}, "roleClaimType": {"type": "string", "readOnly": true, "nullable": true}}}, "Claim": {"type": "object", "additionalProperties": false, "properties": {"issuer": {"type": "string", "nullable": true}, "originalIssuer": {"type": "string", "nullable": true}, "properties": {"type": "object", "readOnly": true, "nullable": true, "additionalProperties": {"type": "string"}}, "subject": {"$ref": "#/components/schemas/ClaimsIdentity"}, "type": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "valueType": {"type": "string", "nullable": true}}}, "Secret": {"type": "object", "additionalProperties": false, "properties": {"description": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "expiration": {"type": "string", "format": "date-time", "nullable": true}, "type": {"type": "string", "nullable": true}}}, "TokenExpiration": {"type": "string", "enum": ["Sliding", "Absolute"]}, "TokenUsage": {"type": "string", "enum": ["ReUse", "OneTimeOnly"]}, "ForgotPasswordEmailSettings": {"type": "object", "additionalProperties": false, "properties": {"from": {"type": "string", "nullable": true}, "fromName": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "templateId": {"type": "string", "nullable": true}, "link": {"type": "string", "nullable": true}}}, "App": {"type": "object", "additionalProperties": false, "properties": {"appId": {"type": "string", "nullable": true}, "appName": {"type": "string", "nullable": true}, "redirectUris": {"type": "array", "nullable": true, "items": {"type": "string"}}, "email": {"type": "string", "nullable": true}, "emailSenderName": {"type": "string", "nullable": true}, "oneTimePasswordEmailSubject": {"type": "string", "nullable": true}, "useNotificationConfig": {"type": "boolean"}, "accessTokenLifetime": {"type": "integer", "format": "int32"}, "requires2FA": {"type": "boolean"}, "urlRouting": {"$ref": "#/components/schemas/UrlRouting"}, "absoluteRefreshTokenLifetime": {"type": "integer", "format": "int32"}, "accessTokenType": {"$ref": "#/components/schemas/AccessTokenType"}, "allowAccessTokensViaBrowser": {"type": "boolean"}, "allowedCorsOrigins": {"type": "array", "nullable": true, "items": {"type": "string"}}, "allowedGrantTypes": {"type": "array", "nullable": true, "items": {"type": "string"}}, "allowPlainTextPkce": {"type": "boolean"}, "allowedScopes": {"type": "array", "nullable": true, "items": {"type": "string"}}, "allowOfflineAccess": {"type": "boolean"}, "allowRememberConsent": {"type": "boolean"}, "alwaysIncludeUserClaimsInIdToken": {"type": "boolean"}, "alwaysSendClientClaims": {"type": "boolean"}, "authorizationCodeLifetime": {"type": "integer", "format": "int32"}, "updateAccessTokenClaimsOnRefresh": {"type": "boolean"}, "backChannelLogoutSessionRequired": {"type": "boolean"}, "backChannelLogoutUri": {"type": "string", "nullable": true}, "claims": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Claim"}}, "clientClaimsPrefix": {"type": "string", "nullable": true}, "clientSecrets": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Secret"}}, "clientUri": {"type": "string", "nullable": true}, "consentLifetime": {"type": "integer", "format": "int32", "nullable": true}, "description": {"type": "string", "nullable": true}, "deviceCodeLifetime": {"type": "integer", "format": "int32"}, "enabled": {"type": "boolean"}, "enableLocalLogin": {"type": "boolean"}, "frontChannelLogoutSessionRequired": {"type": "boolean"}, "frontChannelLogoutUri": {"type": "string", "nullable": true}, "identityProviderRestrictions": {"type": "array", "nullable": true, "items": {"type": "string"}}, "identityTokenLifetime": {"type": "integer", "format": "int32"}, "includeJwtId": {"type": "boolean"}, "logoUri": {"type": "string", "nullable": true}, "pairWiseSubjectSalt": {"type": "string", "nullable": true}, "postLogoutRedirectUris": {"type": "array", "nullable": true, "items": {"type": "string"}}, "properties": {"type": "object", "nullable": true, "additionalProperties": {"type": "string"}}, "protocolType": {"type": "string", "nullable": true}, "refreshTokenExpiration": {"$ref": "#/components/schemas/TokenExpiration"}, "refreshTokenUsage": {"$ref": "#/components/schemas/TokenUsage"}, "requireClientSecret": {"type": "boolean"}, "requireConsent": {"type": "boolean"}, "requirePkce": {"type": "boolean"}, "slidingRefreshTokenLifetime": {"type": "integer", "format": "int32"}, "userCodeType": {"type": "string", "nullable": true}, "userSsoLifetime": {"type": "integer", "format": "int32", "nullable": true}, "emailConfirmationTokenLifespan": {"type": "string", "format": "date-span", "nullable": true}, "dataProtectionTokenLifespan": {"type": "string", "format": "date-span", "nullable": true}, "passwordExpiryLifespan": {"type": "string", "format": "date-span", "nullable": true}, "defaultTimeZone": {"type": "string", "nullable": true}, "activationTokenExpiryDisabled": {"type": "boolean"}, "requiresEmail2FA": {"type": "boolean"}, "appConfig": {"type": "string", "nullable": true}, "forgotPasswordEmailSettings": {"$ref": "#/components/schemas/ForgotPasswordEmailSettings"}, "createdAt": {"type": "string", "format": "date-time"}, "lastModifiedAt": {"type": "string", "format": "date-time"}, "createdById": {"type": "string", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}}}, "EventQuery": {"type": "object", "additionalProperties": false, "properties": {"ids": {"type": "array", "nullable": true, "items": {"type": "string"}}, "types": {"type": "array", "nullable": true, "items": {"type": "string"}}, "fromDate": {"type": "string", "format": "date-time", "nullable": true}, "toDate": {"type": "string", "format": "date-time", "nullable": true}}}, "EventLog": {"type": "object", "additionalProperties": false, "properties": {"relatedId": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "byId": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}}}, "CreateAppCommand": {"type": "object", "additionalProperties": false, "properties": {"appId": {"type": "string", "nullable": true}, "appName": {"type": "string", "nullable": true}, "redirectUris": {"type": "array", "nullable": true, "items": {"type": "string"}}, "email": {"type": "string", "nullable": true}, "emailSenderName": {"type": "string", "nullable": true}, "useNotificationConfig": {"type": "boolean"}, "accessTokenLifetime": {"type": "integer", "format": "int32", "nullable": true}, "absoluteRefreshTokenLifetime": {"type": "integer", "format": "int32", "nullable": true}, "slidingRefreshTokenLifetime": {"type": "integer", "format": "int32", "nullable": true}, "requires2FA": {"type": "boolean"}, "urlRouting": {"$ref": "#/components/schemas/UrlRouting"}, "createdById": {"type": "string", "nullable": true}, "emailConfirmationTokenLifespan": {"type": "string", "format": "date-span", "nullable": true}, "dataProtectionTokenLifespan": {"type": "string", "format": "date-span", "nullable": true}, "defaultTimeZone": {"type": "string", "nullable": true}, "activationTokenExpiryDisabled": {"type": "boolean"}, "requiresEmail2FA": {"type": "boolean"}, "appConfig": {"type": "string", "nullable": true}, "forgotPasswordEmailSettings": {"$ref": "#/components/schemas/ForgotPasswordEmailSettings"}}}, "Error": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}}}, "Result": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "nullable": true}, "errors": {"type": "array", "nullable": true, "items": {"type": "string"}}, "errors_2": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Error"}}, "isSuccess": {"type": "boolean", "readOnly": true}}}, "UrlRoutingToUpdate": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "isUrlChanged": {"type": "boolean"}, "regexPattern": {"type": "string", "nullable": true}, "isRegexPatternChanged": {"type": "boolean"}, "order": {"type": "integer", "format": "int32"}, "isOrderChanged": {"type": "boolean"}}}, "ForgotPasswordEmailSettingsToUpdate": {"type": "object", "additionalProperties": false, "properties": {"from": {"type": "string", "nullable": true}, "isFromChanged": {"type": "boolean"}, "fromName": {"type": "string", "nullable": true}, "isFromNameChanged": {"type": "boolean"}, "subject": {"type": "string", "nullable": true}, "isSubjectChanged": {"type": "boolean"}, "templateId": {"type": "string", "nullable": true}, "isTemplateIdChanged": {"type": "boolean"}, "link": {"type": "string", "nullable": true}, "isLinkChanged": {"type": "boolean"}}}, "UpdateAppCommand": {"type": "object", "additionalProperties": false, "properties": {"appName": {"type": "string", "nullable": true}, "isAppNameChanged": {"type": "boolean"}, "redirectUris": {"type": "array", "nullable": true, "items": {"type": "string"}}, "isRedirectUrisChanged": {"type": "boolean"}, "email": {"type": "string", "nullable": true}, "isEmailChanged": {"type": "boolean"}, "emailSenderName": {"type": "string", "nullable": true}, "isEmailSenderNameChanged": {"type": "boolean"}, "accessTokenLifetime": {"type": "integer", "format": "int32", "nullable": true}, "isAccessTokenLifetimeChanged": {"type": "boolean"}, "isEmailConfirmationTokenLifespanChanged": {"type": "boolean"}, "isDataProtectionTokenLifespanChanged": {"type": "boolean"}, "requires2FA": {"type": "boolean", "nullable": true}, "isRequires2FAChanged": {"type": "boolean"}, "urlRouting": {"$ref": "#/components/schemas/UrlRoutingToUpdate"}, "isUrlRoutingChanged": {"type": "boolean"}, "useNotificationConfig": {"type": "boolean", "nullable": true}, "isUseNotificationConfigChanged": {"type": "boolean"}, "emailConfirmationTokenLifespan": {"type": "string", "format": "date-span", "nullable": true}, "dataProtectionTokenLifespan": {"type": "string", "format": "date-span", "nullable": true}, "absoluteRefreshTokenLifetime": {"type": "integer", "format": "int32", "nullable": true}, "isAbsoluteRefreshTokenLifetimeChanged": {"type": "boolean"}, "slidingRefreshTokenLifetime": {"type": "integer", "format": "int32", "nullable": true}, "isSlidingRefreshTokenLifetimeChanged": {"type": "boolean"}, "modifiedById": {"type": "string", "nullable": true}, "defaultTimeZone": {"type": "string", "nullable": true}, "isDefaultTimeZoneChanged": {"type": "boolean"}, "activationTokenExpiryDisabled": {"type": "boolean"}, "isActivationTokenExpiryDisabledChanged": {"type": "boolean"}, "requiresEmail2FA": {"type": "boolean"}, "isRequiresEmail2FAChanged": {"type": "boolean"}, "appConfig": {"type": "string", "nullable": true}, "isAppConfigChanged": {"type": "boolean"}, "forgotPasswordEmailSettings": {"$ref": "#/components/schemas/ForgotPasswordEmailSettingsToUpdate"}, "isForgotPasswordEmailSettingsChanged": {"type": "boolean"}}}, "DeleteCommand": {"type": "object", "additionalProperties": false, "properties": {"deletedById": {"type": "string", "nullable": true}}}, "Token": {"type": "object", "additionalProperties": false, "properties": {"accessToken": {"type": "string", "nullable": true}}}, "SSOConfig": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "idClaim": {"type": "string", "nullable": true}, "keyUrlClaim": {"type": "string", "nullable": true}, "keyUrl": {"type": "string", "nullable": true}, "claimsMap": {"type": "object", "nullable": true, "additionalProperties": {"type": "string"}}, "additionalClaims": {"type": "object", "nullable": true, "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "clientId": {"type": "string", "nullable": true}, "clientSecret": {"type": "string", "nullable": true}, "validateExistingLoginByEmail": {"type": "boolean", "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "useIdentityToken": {"type": "boolean", "nullable": true}, "clientIdClaim": {"type": "string", "nullable": true}, "autoProvisionUser": {"type": "boolean", "nullable": true}, "userNameClaims": {"type": "array", "nullable": true, "items": {"type": "string"}}, "autoAssignRoles": {"type": "boolean", "nullable": true}, "rolesClaim": {"type": "string", "nullable": true}, "externalRolesMatching": {"type": "object", "nullable": true, "additionalProperties": {"type": "string"}}, "defaultRoles": {"type": "array", "nullable": true, "items": {"type": "string"}}, "providerId": {"type": "string", "nullable": true}, "identityProviderHint": {"type": "string", "nullable": true}, "errorPath": {"type": "string", "nullable": true}, "disableClientIdValidation": {"type": "boolean", "nullable": true}}}, "TenantSettings": {"type": "object", "additionalProperties": false, "properties": {"tenantId": {"type": "string", "nullable": true}, "hosts": {"type": "array", "nullable": true, "items": {"type": "string"}}, "ssoConfigs": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/SSOConfig"}}}}, "CreateAdminCommand": {"type": "object", "additionalProperties": false, "properties": {"username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}}, "CreateTenantCommand": {"type": "object", "additionalProperties": false, "properties": {"adminSettings": {"$ref": "#/components/schemas/CreateAdminCommand"}}}, "PasswordValidators": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "clientId": {"type": "string", "nullable": true}, "enableLockout": {"type": "boolean"}, "lockoutTimespanInSeconds": {"type": "integer", "format": "int32"}, "lockoutMaxFailedAccessAttempts": {"type": "integer", "format": "int32"}, "lockoutEmailTemplateId": {"type": "string", "nullable": true}, "requireConfirmedEmail": {"type": "boolean"}, "requireConfirmPhoneNumber": {"type": "boolean"}, "requireDigit": {"type": "boolean"}, "requireLength": {"type": "integer", "format": "int32"}, "requireUniqueChars": {"type": "integer", "format": "int32"}, "requireLowercase": {"type": "boolean"}, "requireUppercase": {"type": "boolean"}, "requireNonAlphanumeric": {"type": "boolean"}, "requireMaxConsecutiveRepeatingCharacters": {"type": "boolean"}, "requireMaxIncrementalSequenceCharacters": {"type": "boolean"}, "requireLetter": {"type": "boolean"}, "exposeErrorMessage": {"type": "boolean"}, "passwordLifespanInSeconds": {"type": "integer", "format": "int32", "nullable": true}, "tempPasswordLifespanInSeonds": {"type": "integer", "format": "int32", "nullable": true}, "savePasswordHistoryCount": {"type": "integer", "format": "int32", "nullable": true}, "allowExpiredPasswordEasyReset": {"type": "boolean"}, "passwordResetRequireDobVerification": {"type": "boolean"}, "maxDobVerificationAttempts": {"type": "integer", "format": "int32", "nullable": true}, "email2faLifespanInSeconds": {"type": "integer", "format": "int32", "nullable": true}, "email2faTimeStepInSeconds": {"type": "integer", "format": "int32", "nullable": true}, "notContainUsername": {"type": "boolean"}}}, "ValidatePasswordCommand": {"type": "object", "additionalProperties": false, "properties": {"password": {"type": "string", "nullable": true}}}, "LoginWhere": {"type": "object", "additionalProperties": false, "properties": {"or": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/LoginWhere"}}, "and": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/LoginWhere"}}, "ids": {"type": "array", "nullable": true, "items": {"type": "string"}}, "entityIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "entityTypes": {"type": "array", "nullable": true, "items": {"type": "string"}}, "usernames": {"type": "array", "nullable": true, "items": {"type": "string"}}, "username_contains": {"type": "string", "nullable": true}, "passwordLastUpdated_lt": {"type": "string", "format": "date-time", "nullable": true}, "passwordLastUpdated_gt": {"type": "string", "format": "date-time", "nullable": true}, "email_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "isEmailConfirmed": {"type": "boolean", "nullable": true}, "isTelephoneNumberConfirmed": {"type": "boolean", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "permissionGroupIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "excludePermissions": {"type": "boolean"}, "lastModifiedAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}, "lastModifiedById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "lastModifiedById_contains": {"type": "string", "nullable": true}, "createdById": {"type": "string", "nullable": true}, "createdById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "createdById_contains": {"type": "string", "nullable": true}}}, "LoginQueryArguments": {"type": "object", "additionalProperties": false, "properties": {"where": {"$ref": "#/components/schemas/LoginWhere"}, "orderBy": {"$ref": "#/components/schemas/OrderBy"}, "first": {"type": "integer", "format": "int32", "nullable": true}, "skip": {"type": "integer", "format": "int32", "nullable": true}}}, "PermissionGroup": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "targettedPermissions": {"type": "object", "nullable": true, "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "permissionGroupIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "inheritedLoginIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "productTypes": {"type": "array", "nullable": true, "items": {"type": "string"}}, "createdAt": {"type": "string", "format": "date-time"}, "lastModifiedAt": {"type": "string", "format": "date-time"}, "createdById": {"type": "string", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}}}, "Login": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isEmailConfirmed": {"type": "boolean"}, "telephoneNumber": {"type": "string", "nullable": true}, "isTelephoneNumberConfirmed": {"type": "boolean"}, "isPasswordValidationDobRequire": {"type": "boolean"}, "lockoutEndDateUtc": {"type": "string", "format": "date-time", "nullable": true}, "accessFailedCount": {"type": "integer", "format": "int32"}, "ignorePasswordLifespan": {"type": "boolean"}, "ignoreAccountLockout": {"type": "boolean"}, "targettedPermissions": {"type": "object", "nullable": true, "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "permissionGroups": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/PermissionGroup"}}, "inheritedLoginIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "entityId": {"type": "string", "nullable": true}, "targetedPermissionSchemaIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "permissionLazyLoadingRequired": {"type": "boolean"}, "isActive": {"type": "boolean"}, "latestOtp": {"type": "string", "nullable": true}, "passwordLastUpdated": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "lastModifiedAt": {"type": "string", "format": "date-time"}, "createdById": {"type": "string", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}}}, "LoginEventType": {"type": "string", "enum": ["createLogin", "verifyCode", "verifyResetPasswordCode", "sendCode", "resendEmail", "updateLogin", "deleteLogin", "confirmEmail", "resetPassword", "forgotPassword", "changePassword", "changeExpiredPassword", "addPermission", "removePermission", "updateLockoutEndDate", "requestAccessTokenSuccess", "addTargetedPermissionSchema", "removeTargetedPermissionSchema", "deactivate", "reactivate"]}, "LoginEvent": {"type": "object", "additionalProperties": false, "properties": {"loginId": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/LoginEventType"}, "id": {"type": "string", "nullable": true}, "values": {"nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}}, "PushMessage": {"type": "object", "additionalProperties": false, "properties": {"token": {"type": "string", "nullable": true}, "topic": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "data": {"type": "object", "nullable": true, "additionalProperties": {"type": "string"}}}}, "PdfAttachment": {"type": "object", "additionalProperties": false, "properties": {"fileName": {"type": "string", "nullable": true}, "htmlContent": {"type": "string", "nullable": true}, "bytes": {"type": "string", "format": "byte", "nullable": true}, "password": {"type": "string", "nullable": true}}}, "AttachmentTemplate": {"type": "object", "additionalProperties": false, "properties": {"fileName": {"type": "string", "nullable": true}, "templateId": {"type": "string", "nullable": true}}}, "AttachmentReference": {"type": "object", "additionalProperties": false, "properties": {"fileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}}}, "RenderParameters": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "nullable": true}, "contentJsonString": {"type": "string", "nullable": true}, "content": {"nullable": true}, "url": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}, "variables": {"nullable": true}, "variablesJsonString": {"type": "string", "nullable": true}, "overrideAttachmentTemplates": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/AttachmentTemplate"}}, "overrideAttachmentReferences": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/AttachmentReference"}}}}, "TemplateRendering": {"type": "object", "additionalProperties": false, "properties": {"templateId": {"type": "string", "nullable": true}, "input": {"$ref": "#/components/schemas/RenderParameters"}}}, "EmailMessage": {"type": "object", "additionalProperties": false, "properties": {"from": {"type": "string", "nullable": true}, "fromName": {"type": "string", "nullable": true}, "to": {"type": "string", "nullable": true}, "tos": {"type": "array", "nullable": true, "items": {"type": "string"}}, "ccs": {"type": "array", "nullable": true, "items": {"type": "string"}}, "bccs": {"type": "array", "nullable": true, "items": {"type": "string"}}, "subject": {"type": "string", "nullable": true}, "htmlContent": {"type": "string", "nullable": true}, "pdfAttachments": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/PdfAttachment"}}, "templateRendering": {"$ref": "#/components/schemas/TemplateRendering"}}}, "SmsMessage": {"type": "object", "additionalProperties": false, "properties": {"from": {"type": "string", "nullable": true}, "to": {"type": "string", "nullable": true}, "body": {"type": "string", "nullable": true}, "templateRendering": {"$ref": "#/components/schemas/TemplateRendering"}}}, "SendNotificationCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "fromEntityId": {"type": "string", "nullable": true}, "toEntityId": {"type": "string", "nullable": true}, "policyId": {"type": "string", "nullable": true}, "offerId": {"type": "string", "nullable": true}, "pushMessage": {"$ref": "#/components/schemas/PushMessage"}, "emailMessage": {"$ref": "#/components/schemas/EmailMessage"}, "smsMessage": {"$ref": "#/components/schemas/SmsMessage"}, "useConfig": {"type": "boolean", "nullable": true}, "sentById": {"type": "string", "nullable": true}, "appId": {"type": "string", "nullable": true}, "loginId": {"type": "string", "nullable": true}, "callbackUrl": {"type": "string", "nullable": true}, "redirectQueryString": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "scheduleToSendAt": {"type": "string", "format": "date-time", "nullable": true}}}, "CreateLoginCommand": {"type": "object", "additionalProperties": false, "properties": {"clientId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "telephoneNumber": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "createdById": {"type": "string", "nullable": true}, "entityId": {"type": "string", "nullable": true}, "entityType": {"type": "string", "nullable": true}, "isEmailConfirmed": {"type": "boolean"}, "ignorePasswordValidation": {"type": "boolean"}, "ignorePasswordLifespan": {"type": "boolean"}, "ignoreAccountLockout": {"type": "boolean"}, "appIdsToBeGrantedAccessTo": {"type": "array", "nullable": true, "items": {"type": "string"}}, "sendNotificationCommand": {"$ref": "#/components/schemas/SendNotificationCommand"}, "redirectQueryString": {"type": "string", "nullable": true}, "useDefaultPermissions": {"type": "boolean", "nullable": true}}}, "CreatedStatus": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "ids": {"type": "array", "nullable": true, "items": {"type": "string"}}}}, "CreatedStatusResult": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "nullable": true}, "errors": {"type": "array", "nullable": true, "items": {"type": "string"}}, "errors_2": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Error"}}, "value": {"$ref": "#/components/schemas/CreatedStatus"}, "isSuccess": {"type": "boolean", "readOnly": true}}}, "SendCodeCommand": {"type": "object", "additionalProperties": false, "properties": {"clientId": {"type": "string", "nullable": true}, "sentById": {"type": "string", "nullable": true}, "templateId": {"type": "string", "nullable": true}, "purpose": {"type": "string", "nullable": true}}}, "VerifyCodeCommand": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "string", "nullable": true}, "purpose": {"type": "string", "nullable": true}, "verifiedById": {"type": "string", "nullable": true}}}, "ResendEmailCommand": {"type": "object", "additionalProperties": false, "properties": {"clientId": {"type": "string", "nullable": true}, "resentById": {"type": "string", "nullable": true}, "sendNotificationCommand": {"$ref": "#/components/schemas/SendNotificationCommand"}, "callbackUrl": {"type": "string", "nullable": true}}}, "UpdateLoginCommand": {"type": "object", "additionalProperties": false, "properties": {"userName": {"type": "string", "nullable": true}, "isUserNameChanged": {"type": "boolean"}, "email": {"type": "string", "nullable": true}, "isEmailChanged": {"type": "boolean"}, "telephoneNumber": {"type": "string", "nullable": true}, "isTelephoneNumberChanged": {"type": "boolean"}, "modifiedById": {"type": "string", "nullable": true}}}, "ConfirmEmailCommand": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "string", "nullable": true}, "confirmedById": {"type": "string", "nullable": true}, "appId": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "sendNotificationCommand": {"$ref": "#/components/schemas/SendNotificationCommand"}}}, "TokenResult": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "nullable": true}, "errors": {"type": "array", "nullable": true, "items": {"type": "string"}}, "errors_2": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Error"}}, "value": {"$ref": "#/components/schemas/Token"}, "isSuccess": {"type": "boolean", "readOnly": true}}}, "ResetPasswordCommand": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "sendNotificationCommand": {"$ref": "#/components/schemas/SendNotificationCommand"}}}, "ForgotPasswordCommand": {"type": "object", "additionalProperties": false, "properties": {"clientId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "sendNotificationCommand": {"$ref": "#/components/schemas/SendNotificationCommand"}, "forgottenById": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}}}, "ChangePasswordCommand": {"type": "object", "additionalProperties": false, "properties": {"currentPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "ignorePasswordValidation": {"type": "boolean"}, "sendNotificationCommand": {"$ref": "#/components/schemas/SendNotificationCommand"}, "changedById": {"type": "string", "nullable": true}}}, "ChangeExpiredPasswordCommand": {"type": "object", "additionalProperties": false, "properties": {"userName": {"type": "string", "nullable": true}, "currentPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}}, "ChangeUserLockoutDateCommand": {"type": "object", "additionalProperties": false, "properties": {"endDateTime": {"type": "string", "format": "date-time", "nullable": true}, "modifiedById": {"type": "string", "nullable": true}}}, "AddTargetedPermissionSchemaToLoginCommand": {"type": "object", "additionalProperties": false, "properties": {"loginId": {"type": "string", "nullable": true}, "permissionSchemaId": {"type": "string", "nullable": true}, "targetIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "addedById": {"type": "string", "nullable": true}}}, "RemoveTargetedPermissionSchemaFromLoginCommand": {"type": "object", "additionalProperties": false, "properties": {"loginId": {"type": "string", "nullable": true}, "permissionSchemaId": {"type": "string", "nullable": true}, "targetIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "removedById": {"type": "string", "nullable": true}}}, "TargetedPermissionSchema": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "permissionSchemaId": {"type": "string", "nullable": true}, "targetIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "createdAt": {"type": "string", "format": "date-time"}, "lastModifiedAt": {"type": "string", "format": "date-time"}, "createdById": {"type": "string", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}}}, "RescheduleNotificationCommand": {"type": "object", "additionalProperties": false, "properties": {"loginId": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "scheduleToSendAt": {"type": "string", "format": "date-time", "nullable": true}}}, "LoginEventWhere": {"type": "object", "additionalProperties": false, "properties": {"loginIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "types": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/LoginEventType"}}, "fromDate": {"type": "string", "format": "date-time", "nullable": true}, "toDate": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}, "lastModifiedById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "lastModifiedById_contains": {"type": "string", "nullable": true}, "createdById": {"type": "string", "nullable": true}, "createdById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "createdById_contains": {"type": "string", "nullable": true}}}, "GroupBy": {"type": "object", "additionalProperties": false, "properties": {"fieldName": {"type": "string", "nullable": true}}}, "LoginEventWhereQueryArguments": {"type": "object", "additionalProperties": false, "properties": {"where": {"$ref": "#/components/schemas/LoginEventWhere"}, "asOf": {"type": "string", "format": "date-time", "nullable": true}, "orderBy": {"$ref": "#/components/schemas/OrderBy"}, "orderBy2": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/OrderBy"}}, "first": {"type": "integer", "format": "int32", "nullable": true}, "skip": {"type": "integer", "format": "int32", "nullable": true}, "includeEvents": {"type": "boolean", "nullable": true}, "groupBy": {"$ref": "#/components/schemas/GroupBy"}}}, "DetailedEventLog": {"type": "object", "additionalProperties": false, "properties": {"value": {"nullable": true}, "relatedId": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "byId": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}}}, "InitializeTenantCommand": {"type": "object", "additionalProperties": false, "properties": {"tenantId": {"type": "string", "nullable": true}}}, "TenantIdAndAppId": {"type": "object", "additionalProperties": false, "properties": {"tenantId": {"type": "string", "nullable": true}, "appId": {"type": "string", "nullable": true}}}, "TenantIdAndAppIdResult": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "nullable": true}, "errors": {"type": "array", "nullable": true, "items": {"type": "string"}}, "errors_2": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Error"}}, "value": {"$ref": "#/components/schemas/TenantIdAndAppId"}, "isSuccess": {"type": "boolean", "readOnly": true}}}, "CreatePreOtpLoginCommand": {"type": "object", "additionalProperties": false, "properties": {"clientId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}}}, "PreOtpLogin": {"type": "object", "additionalProperties": false, "properties": {"token": {"type": "string", "nullable": true}}}, "CreateOtpLoginCommand": {"type": "object", "additionalProperties": false, "properties": {"token": {"type": "string", "nullable": true}, "emailTemplateId": {"type": "string", "nullable": true}, "smsTemplateId": {"type": "string", "nullable": true}}}, "OtpLogin": {"type": "object", "additionalProperties": false, "properties": {"token": {"type": "string", "nullable": true}}}, "CreateAccessTokenFromOtpLoginCommand": {"type": "object", "additionalProperties": false, "properties": {"token": {"type": "string", "nullable": true}, "oneTimePassword": {"type": "string", "nullable": true}}}, "ResponseErrorType": {"type": "string", "enum": ["None", "Protocol", "Http", "Exception", "PolicyViolation"]}, "HttpStatusCode": {"type": "string", "enum": ["Continue", "SwitchingProtocols", "Processing", "EarlyHints", "OK", "Created", "Accepted", "NonAuthoritativeInformation", "NoContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PartialContent", "MultiStatus", "AlreadyReported", "IMUsed", "MultipleChoices", "Ambiguous", "MovedPermanently", "Moved", "Found", "Redirect", "<PERSON><PERSON><PERSON>", "RedirectMethod", "NotModified", "UseProxy", "Unused", "TemporaryRedirect", "RedirectKeepVerb", "PermanentRedirect", "BadRequest", "Unauthorized", "PaymentRequired", "Forbidden", "NotFound", "MethodNotAllowed", "NotAcceptable", "ProxyAuthenticationRequired", "RequestTimeout", "Conflict", "Gone", "LengthRequired", "PreconditionFailed", "RequestEntityTooLarge", "RequestUriTooLong", "UnsupportedMediaType", "RequestedRangeNotSatisfiable", "ExpectationFailed", "MisdirectedRequest", "UnprocessableEntity", "Locked", "FailedDependency", "UpgradeRequired", "PreconditionRequired", "TooManyRequests", "RequestHeaderFields<PERSON>ooLarge", "UnavailableForLegalReasons", "InternalServerError", "NotImplemented", "BadGateway", "ServiceUnavailable", "GatewayTimeout", "HttpVersionNotSupported", "VariantAlsoNegotiates", "InsufficientStorage", "LoopDetected", "NotExtended", "NetworkAuthenticationRequired"]}, "TokenResponse": {"type": "object", "additionalProperties": false, "properties": {"accessToken": {"type": "string", "readOnly": true, "nullable": true}, "identityToken": {"type": "string", "readOnly": true, "nullable": true}, "tokenType": {"type": "string", "readOnly": true, "nullable": true}, "refreshToken": {"type": "string", "readOnly": true, "nullable": true}, "errorDescription": {"type": "string", "readOnly": true, "nullable": true}, "expiresIn": {"type": "integer", "readOnly": true, "format": "int32"}, "raw": {"type": "string", "readOnly": true, "nullable": true}, "json": {"readOnly": true, "nullable": true}, "exception": {"nullable": true}, "isError": {"type": "boolean", "readOnly": true}, "errorType": {"$ref": "#/components/schemas/ResponseErrorType"}, "httpStatusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "httpErrorReason": {"type": "string", "readOnly": true, "nullable": true}, "error": {"type": "string", "readOnly": true, "nullable": true}}}, "OTPRemarks": {"type": "object", "additionalProperties": false, "properties": {"username": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}}}, "AddTargettedPermissionCommand": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "addedById": {"type": "string", "nullable": true}}}, "RemoveTargettedPermissionCommand": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "removedById": {"type": "string", "nullable": true}}}, "FullAccessVerificationCommand": {"type": "object", "additionalProperties": false, "properties": {"tenantId": {"type": "string", "nullable": true}, "clientId": {"type": "string", "nullable": true}, "loginId": {"type": "string", "nullable": true}}}, "CreatePermissionGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "createdById": {"type": "string", "nullable": true}, "productTypes": {"type": "array", "nullable": true, "items": {"type": "string"}}}}, "UpdatePermissionGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "modifiedById": {"type": "string", "nullable": true}, "productTypes": {"type": "array", "nullable": true, "items": {"type": "string"}}}}, "PermissionGroupWhere": {"type": "object", "additionalProperties": false, "properties": {"and": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/PermissionGroupWhere"}}, "or": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/PermissionGroupWhere"}}, "id": {"type": "string", "nullable": true}, "id_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "name": {"type": "string", "nullable": true}, "name_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "lastModifiedAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}, "lastModifiedById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "lastModifiedById_contains": {"type": "string", "nullable": true}, "createdById": {"type": "string", "nullable": true}, "createdById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "createdById_contains": {"type": "string", "nullable": true}}}, "AddPermissionToPermissionGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"permissionId": {"type": "string", "nullable": true}, "targetId": {"type": "string", "nullable": true}, "addedById": {"type": "string", "nullable": true}}}, "AddPermissionGroupToPermissionGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"permissionGroupId": {"type": "string", "nullable": true}, "addedById": {"type": "string", "nullable": true}}}, "RemovePermissionFromPermissionGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"permissionId": {"type": "string", "nullable": true}, "targetId": {"type": "string", "nullable": true}, "removedById": {"type": "string", "nullable": true}}}, "RemovePermissionGroupFromPermissionGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"permissionGroupId": {"type": "string", "nullable": true}, "removedById": {"type": "string", "nullable": true}}}, "AddLoginPermissionsToPermissionGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"loginId": {"type": "string", "nullable": true}, "addedById": {"type": "string", "nullable": true}}}, "RemoveLoginPermissionsFromPermissionGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"loginId": {"type": "string", "nullable": true}, "removedById": {"type": "string", "nullable": true}}}, "PermissionSchemaActionType": {"type": "string", "enum": ["Read", "Write"]}, "KeyScalarValue": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string", "nullable": true}, "value": {"$ref": "#/components/schemas/ScalarValue"}}}, "ScalarValue": {"type": "object", "additionalProperties": false, "properties": {"stringValue": {"type": "string", "nullable": true}, "numberValue": {"type": "number", "format": "double", "nullable": true}, "doubleValue": {"type": "number", "format": "double", "nullable": true}, "booleanValue": {"type": "boolean", "nullable": true}, "dateValue": {"type": "string", "format": "date-time", "nullable": true}, "objectValue": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/KeyScalarValue"}}, "arrayValue": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ScalarValue"}}}}, "FieldsWhereCondition": {"type": "string", "enum": ["EQUALS", "IN", "STRING_CONTAINS", "ARRAY_CONTAINS", "LESS_THAN", "GREATER_THAN", "EXISTS"]}, "FieldsWhere": {"type": "object", "additionalProperties": false, "properties": {"path": {"type": "string", "nullable": true}, "value": {"$ref": "#/components/schemas/ScalarValue"}, "condition": {"$ref": "#/components/schemas/FieldsWhereCondition"}, "and": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/FieldsWhere"}}, "or": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/FieldsWhere"}}}}, "PermissionSchemaWhere": {"type": "object", "additionalProperties": false, "properties": {"and": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/PermissionSchemaWhere"}}, "or": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/PermissionSchemaWhere"}}, "id": {"type": "string", "nullable": true}, "id_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "objectType": {"type": "string", "nullable": true}, "actionType": {"$ref": "#/components/schemas/PermissionSchemaActionType"}, "name": {"type": "string", "nullable": true}, "schema": {"$ref": "#/components/schemas/FieldsWhere"}, "lastModifiedAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}, "lastModifiedById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "lastModifiedById_contains": {"type": "string", "nullable": true}, "createdById": {"type": "string", "nullable": true}, "createdById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "createdById_contains": {"type": "string", "nullable": true}}}, "PermissionSchemaWhereQueryArguments": {"type": "object", "additionalProperties": false, "properties": {"where": {"$ref": "#/components/schemas/PermissionSchemaWhere"}, "asOf": {"type": "string", "format": "date-time", "nullable": true}, "orderBy": {"$ref": "#/components/schemas/OrderBy"}, "orderBy2": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/OrderBy"}}, "first": {"type": "integer", "format": "int32", "nullable": true}, "skip": {"type": "integer", "format": "int32", "nullable": true}, "includeEvents": {"type": "boolean", "nullable": true}, "groupBy": {"$ref": "#/components/schemas/GroupBy"}}}, "PermissionSchema": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "objectType": {"type": "string", "nullable": true}, "actionType": {"$ref": "#/components/schemas/PermissionSchemaActionType"}, "schema": {"nullable": true}, "stateCondition": {"$ref": "#/components/schemas/FieldsWhere"}, "updateCondition": {"$ref": "#/components/schemas/FieldsWhere"}, "createdAt": {"type": "string", "format": "date-time"}, "lastModifiedAt": {"type": "string", "format": "date-time"}, "createdById": {"type": "string", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}}}, "CreatePermissionSchemaCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "objectType": {"type": "string", "nullable": true}, "actionType": {"$ref": "#/components/schemas/PermissionSchemaActionType"}, "schema": {"type": "string", "nullable": true}, "stateCondition": {"$ref": "#/components/schemas/FieldsWhere"}, "updateCondition": {"$ref": "#/components/schemas/FieldsWhere"}, "createdById": {"type": "string", "nullable": true}}}, "UpdatePermissionSchemaCommand": {"type": "object", "additionalProperties": false, "properties": {"permissionSchemaId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "objectType": {"type": "string", "nullable": true}, "actionType": {"$ref": "#/components/schemas/PermissionSchemaActionType"}, "schema": {"type": "string", "nullable": true}, "schemaPatch": {"type": "string", "nullable": true}, "stateCondition": {"$ref": "#/components/schemas/FieldsWhere"}, "updateCondition": {"$ref": "#/components/schemas/FieldsWhere"}, "modifiedById": {"type": "string", "nullable": true}}}, "CreateTargetGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}}, "TargetGroup": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "targetIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "targetGroupIds": {"type": "array", "nullable": true, "items": {"type": "string"}}}}, "UpdateTargetGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}}, "ProblemDetails": {"type": "object", "additionalProperties": {}, "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}}, "UserStorageItemWhere": {"type": "object", "additionalProperties": false, "properties": {"and": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/UserStorageItemWhere"}}, "or": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/UserStorageItemWhere"}}, "key_contains": {"type": "string", "nullable": true}, "key_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "fieldsWhere": {"$ref": "#/components/schemas/FieldsWhere"}, "lastModifiedAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_lt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt_gt": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}, "lastModifiedById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "lastModifiedById_contains": {"type": "string", "nullable": true}, "createdById": {"type": "string", "nullable": true}, "createdById_in": {"type": "array", "nullable": true, "items": {"type": "string"}}, "createdById_contains": {"type": "string", "nullable": true}}}, "UserStorageItemWhereQueryArguments": {"type": "object", "additionalProperties": false, "properties": {"where": {"$ref": "#/components/schemas/UserStorageItemWhere"}, "asOf": {"type": "string", "format": "date-time", "nullable": true}, "orderBy": {"$ref": "#/components/schemas/OrderBy"}, "orderBy2": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/OrderBy"}}, "first": {"type": "integer", "format": "int32", "nullable": true}, "skip": {"type": "integer", "format": "int32", "nullable": true}, "includeEvents": {"type": "boolean", "nullable": true}, "groupBy": {"$ref": "#/components/schemas/GroupBy"}}}, "Int64Result": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "nullable": true}, "errors": {"type": "array", "nullable": true, "items": {"type": "string"}}, "errors_2": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Error"}}, "value": {"type": "integer", "format": "int64"}, "isSuccess": {"type": "boolean", "readOnly": true}}}, "CreateUserStorageItemCommand": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string", "nullable": true}, "fields": {"type": "string", "nullable": true}}}, "UpdateUserStorageItemCommand": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string", "nullable": true}, "fields": {"type": "string", "nullable": true}}}, "UserStorageItem": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string", "nullable": true}, "fields": {"nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "lastModifiedAt": {"type": "string", "format": "date-time"}, "createdById": {"type": "string", "nullable": true}, "lastModifiedById": {"type": "string", "nullable": true}}}, "UserStorageItemIReadOnlyCollectionResult": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "nullable": true}, "errors": {"type": "array", "nullable": true, "items": {"type": "string"}}, "errors_2": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Error"}}, "value": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/UserStorageItem"}}, "isSuccess": {"type": "boolean", "readOnly": true}}}}}}