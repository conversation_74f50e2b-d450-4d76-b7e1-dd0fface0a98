﻿using CoverGo.Auth.GraphQL_Client;
using FluentAssertions;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Auth.Tests.Integration;

public class SSOConfigTests
{
    [Fact]
    public async Task GIVEN_SSOConfig_WHEN_adding_SSOConfig_THEN_config_is_added()
    {
        GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());
        await client.Authorize();

        string id = Guid.NewGuid().ToString();
        auth_SSOConfigInput input = new()
        {
            id = id
        };

        string mutation = new _service_CoverGoMutationsRootBuilder().sSOConfigsMutationAddSSOConfig(
            new(input),
            new auth_ResultBuilder().WithAllFields()).Build();

        auth_Result result = await client.SendMutationAsync<auth_Result>(mutation);
        result.isSuccess.Should().BeTrue();
    }

    [Fact]
    public async Task GIVEN_SSOConfig_WHEN_adding_SSOConfig_without_Permissions_THEN_Exception_is_returned()
    {
        GraphQLHttpClient notAuthorizedClient =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

        string id = Guid.NewGuid().ToString();
        auth_SSOConfigInput input = new()
        {
            id = id
        };

        string mutation = new _service_CoverGoMutationsRootBuilder().sSOConfigsMutationAddSSOConfig(
            new(input),
            new auth_ResultBuilder().WithAllFields()).Build();

        Func<Task> act = async () => await notAuthorizedClient.SendMutationAsync<auth_Result>(mutation);

        await act.Should().ThrowAsync<Exception>()
            .WithMessage(
                "Error occured during the GraphQL request: The current user is not authorized to access this resource.");
    }

    [Fact]
    public async Task GIVEN_SSOConfig_WHEN_querying_SSOConfig_by_id_without_Permissions_THEN_No_Exception_is_returned()
    {
        GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

        await client.Authorize();

        string id = Guid.NewGuid().ToString();
        string idClaim = Guid.NewGuid().ToString();
        string keyUrl = Guid.NewGuid().ToString();
        string keyUrlClaim = Guid.NewGuid().ToString();
        string claimMapKey = Guid.NewGuid().ToString();
        string claimMapValue = Guid.NewGuid().ToString();
        string additionalClaimKey = Guid.NewGuid().ToString();
        string additionalClaimValue1 = Guid.NewGuid().ToString();
        string additionalClaimValue2 = Guid.NewGuid().ToString();
        string clientIdClaim = "aud";

        List<auth_KeyValuePairOfStringAndStringInput> claimsMap = new()
        {
            new()
            {
                key = claimMapKey,
                value = claimMapValue
            }
        };
        List<auth_KeyValuePairOfStringAndListOfStringInput> additionalClaims = new()
        {
            new()
            {
                key = additionalClaimKey,
                value = new List<string>
                {
                    additionalClaimValue1,
                    additionalClaimValue2
                }
            }
        };
        auth_SSOConfigInput input = new()
        {
            id = id,
            idClaim = idClaim,
            keyUrl = keyUrl,
            keyUrlClaim = keyUrlClaim,
            validateExistingLoginByEmail = true,
            claimsMap = claimsMap,
            additionalClaims = additionalClaims,
            useIdentityToken = true,
            clientIdClaim = clientIdClaim
        };

        string mutation = new _service_CoverGoMutationsRootBuilder().sSOConfigsMutationAddSSOConfig(
            new(input),
            new auth_ResultBuilder().WithAllFields()).Build();

        await client.SendMutationAsync<auth_Result>(mutation);

        string query = new _service_CoverGoQueriesRootBuilder().sSOConfigsQuerySSOConfigById(
            new(id),
            new auth_SSOConfigBuilder().WithAllFields()).Build();

        GraphQLHttpClient notAuthorizedClient =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());
        auth_SSOConfig authSsoConfig = await notAuthorizedClient.SendQueryAsync<auth_SSOConfig>(query);

        authSsoConfig.Should().NotBeNull();
    }

    [Fact]
    public async Task GIVEN_SSOConfig_WHEN_querying_SSOConfig_by_id_THEN_SSOConfig_is_returned()
    {
        GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

        await client.Authorize();

        string id = Guid.NewGuid().ToString();
        string idClaim = Guid.NewGuid().ToString();
        string keyUrl = Guid.NewGuid().ToString();
        string keyUrlClaim = Guid.NewGuid().ToString();
        string claimMapKey = Guid.NewGuid().ToString();
        string claimMapValue = Guid.NewGuid().ToString();
        string additionalClaimKey = Guid.NewGuid().ToString();
        string additionalClaimValue1 = Guid.NewGuid().ToString();
        string additionalClaimValue2 = Guid.NewGuid().ToString();
        string clientIdClaim = "aud";

        List<auth_KeyValuePairOfStringAndStringInput> claimsMap = new()
        {
            new()
            {
                key = claimMapKey,
                value = claimMapValue
            }
        };
        List<auth_KeyValuePairOfStringAndListOfStringInput> additionalClaims = new()
        {
            new()
            {
                key = additionalClaimKey,
                value = new List<string>
                {
                    additionalClaimValue1,
                    additionalClaimValue2
                }
            }
        };
        auth_SSOConfigInput input = new()
        {
            id = id,
            idClaim = idClaim,
            keyUrl = keyUrl,
            keyUrlClaim = keyUrlClaim,
            validateExistingLoginByEmail = true,
            claimsMap = claimsMap,
            additionalClaims = additionalClaims,
            useIdentityToken = true,
            clientIdClaim = clientIdClaim
        };

        string mutation = new _service_CoverGoMutationsRootBuilder().sSOConfigsMutationAddSSOConfig(
            new(input),
            new auth_ResultBuilder().WithAllFields()).Build();

        await client.SendMutationAsync<auth_Result>(mutation);

        string query = new _service_CoverGoQueriesRootBuilder().sSOConfigsQuerySSOConfigById(
            new(id),
            new auth_SSOConfigBuilder().WithAllFields()).Build();

        auth_SSOConfig config = await client.SendQueryAsync<auth_SSOConfig>(query);

        config.Should().NotBeNull();
        config.id.Should().Be(id);
        config.idClaim.Should().Be(idClaim);
        config.keyUrl.Should().Be(keyUrl);
        config.keyUrlClaim.Should().Be(keyUrlClaim);
        config.validateExistingLoginByEmail.Should().BeTrue();
        config.tenantId.Should().Be("covergo");
        config.useIdentityToken.Should().BeTrue();
        config.clientIdClaim.Should().Be(clientIdClaim);
        auth_KeyValuePairOfStringAndString claimsMapResult = config!.claimsMap!.First();
        claimsMapResult.key.Should().Be(claimMapKey);
        claimsMapResult.value.Should().Be(claimMapValue);
        auth_KeyValuePairOfStringAndListOfString additionalClaimsResult = config!.additionalClaims!.First();
        additionalClaimsResult.key.Should().Be(additionalClaimKey);
        additionalClaimsResult.value.Should().Contain(additionalClaimValue1);
        additionalClaimsResult.value.Should().Contain(additionalClaimValue2);
    }

    [Fact]
    public async Task GIVEN_SSOConfig_WHEN_removing_SSOConfig_THEN_SSOConfig_is_removed()
    {
        GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());
        await client.Authorize();

        string id = Guid.NewGuid().ToString();
        auth_SSOConfigInput input = new()
        {
            id = id
        };

        string addMutation = new _service_CoverGoMutationsRootBuilder().sSOConfigsMutationAddSSOConfig(
            new(input),
            new auth_ResultBuilder().WithAllFields()).Build();

        await client.SendMutationAsync<auth_Result>(addMutation);

        string removeMutation = new _service_CoverGoMutationsRootBuilder().sSOConfigsMutationRemoveSSOConfig(
            new(id),
            new auth_ResultBuilder().WithAllFields()).Build();

        auth_Result removeResult = await client.SendMutationAsync<auth_Result>(removeMutation);
        removeResult.isSuccess.Should().BeTrue();

        string query = new _service_CoverGoQueriesRootBuilder().sSOConfigsQuerySSOConfigById(
            new(id),
            new auth_SSOConfigBuilder().WithAllFields()).Build();

        auth_SSOConfig config = await client.SendQueryAsync<auth_SSOConfig>(query);

        config.Should().BeNull();
    }

    [Fact]
    public async Task GIVEN_SSOConfig_WHEN_removing_SSOConfig_without_Permissions_Exception_is_returned()
    {
        GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());
        await client.Authorize();

        string id = Guid.NewGuid().ToString();
        auth_SSOConfigInput input = new()
        {
            id = id
        };

        string addMutation = new _service_CoverGoMutationsRootBuilder().sSOConfigsMutationAddSSOConfig(
            new(input),
            new auth_ResultBuilder().WithAllFields()).Build();

        await client.SendMutationAsync<auth_Result>(addMutation);

        string removeMutation = new _service_CoverGoMutationsRootBuilder().sSOConfigsMutationRemoveSSOConfig(
            new(id),
            new auth_ResultBuilder().WithAllFields()).Build();


        GraphQLHttpClient notAuthorizedClient =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

        Func<Task> act = async () => await notAuthorizedClient.SendMutationAsync<auth_Result>(removeMutation);

        await act.Should().ThrowAsync<Exception>()
            .WithMessage(
                "Error occured during the GraphQL request: The current user is not authorized to access this resource.");
    }
}
