﻿using AutoFixture.Xunit2;
using CoverGo.Auth.Client;
using CoverGo.Auth.GraphQL_Client;
using FluentAssertions;
using GraphQL;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using IdentityModel.Client;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using TokenResponse = IdentityModel.Client.TokenResponse;

namespace CoverGo.Auth.Tests.Integration
{
    public class LoginTests
    {
        [Fact]
        public async Task GIVEN_create_login_command_WHEN_create_login_and_query_token_THEN_receive_token()
        {
            HttpClient httpClient = Setup.BuildHttpClient(UserCredentials.Admin.TenantId);
            var loginClient = new AuthClient(httpClient);
            string username = Guid.NewGuid().ToString();
            string password = "password";

            await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username, password, new List<string> { UserCredentials.Admin.ClientId });

            string accessToken = await GetAccessTokenAsync(UserCredentials.Admin.TenantId, UserCredentials.Admin.ClientId, username, password);
            accessToken.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_login_WHEN_updatePassword_and_query_token_THEN_receive_token()
        {
            var loginClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";

            string createdLoginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username, password, new List<string> { UserCredentials.Admin.ClientId }); ;

            string newPassword = "newPassword";

            Response<Result> changePasswordResult = await loginClient.ChangepasswordAsync(createdLoginId, new ChangePasswordCommand
            {
                CurrentPassword = password,
                NewPassword = newPassword
            });
            changePasswordResult.StatusCode.Should().Be(200);
            changePasswordResult.Result.Status.Should().Be("success");

            string accessToken = await GetAccessTokenAsync(UserCredentials.Admin.TenantId, UserCredentials.Admin.ClientId, username, newPassword);
            accessToken.Should().NotBeNullOrWhiteSpace();
        }

        [Theory]
        [InlineData(null, true)]
        [InlineData(true, true)]
        [InlineData(false, false)]
        public async Task GIVEN_WHEN_create_login_with_useDefaultPermissions_param_THEN_expected_set_of_permissions_set(bool? useDefaultPermissions, bool isClientIdPermissionExists)
        {
            var loginClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";

            Response<ICollection<Login>> logins = await loginClient.FilterAsync(new LoginQueryArguments
            {
                Where = new LoginWhere
                {
                    Ids = new List<string>
                    {
                        await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username, password, new List<string> { UserCredentials.Admin.ClientId }, useDefaultPermissions: useDefaultPermissions)
                    }
                }
            });

            var login = logins.Result.Single();

            login.TargettedPermissions.ContainsKey("clientId").Should().Be(isClientIdPermissionExists);
        }

        [Fact(Skip = "Can be use only for debug and providing the code during debugging since the code is sent by email.")]
        public async Task GIVEN_login_WHEN_forgotPassword_THEN_resetPassword_code_works_only_one_time()
        {
            var appClient = new AppClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> appResult = await appClient.UpdateAsync(UserCredentials.Admin.ClientId, new UpdateAppCommand()
            {
                RedirectUris = new List<string> { "https://test.covergo.com", "https://test.covergo.com" },
                IsRedirectUrisChanged = true
            });
            appResult.StatusCode.Should().Be(200);
            appResult.Result.Status.Should().Be("success");

            var loginClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";
            string email = "<EMAIL>";

            string createdLoginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username, password, new List<string> { UserCredentials.Admin.ClientId }, email);

            string newPassword = "newPassword";

            Response<Result> forgotPasswordResult = await loginClient.ForgotPasswordAsync(new ForgotPasswordCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Email = email
            });
            forgotPasswordResult.StatusCode.Should().Be(200);
            forgotPasswordResult.Result.Status.Should().Be("success");

            string resetCode = "";

            Response<Result> resetPasswordResult = await loginClient.ResetPasswordAsync(createdLoginId, new ResetPasswordCommand
            {
                Code = resetCode,
                Password = newPassword
            });
            resetPasswordResult.StatusCode.Should().Be(200);
            resetPasswordResult.Result.Status.Should().Be("success");

            resetPasswordResult = await loginClient.ResetPasswordAsync(createdLoginId, new ResetPasswordCommand
            {
                Code = resetCode,
                Password = newPassword
            });
            resetPasswordResult.StatusCode.Should().Be(200);
            resetPasswordResult.Result.Status.Should().Be("failure");
        }

        [Fact(Skip = "Can be used only for debug since the code is sent by email.")]
        public async Task GIVEN_login_in_permission_group_with_clientId_all_WHEN_request_forgotPassword_to_admin_clientId_THEN_login_receives_the_letter()
        {
            var appClient = new AppClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            await appClient.UpdateAsync(UserCredentials.Admin.ClientId, new UpdateAppCommand()
            {
                RedirectUris = new List<string> { "https://test.covergo.com", "https://test.covergo.com" },
                IsRedirectUrisChanged = true
            });

            var loginClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string yourEmail = "<EMAIL>";

            string createdLoginId = await CreateLoginAndReturnLoginIdAsync(
                UserCredentials.Admin.TenantId,
                username,
                "password",
                new List<string> { },
                yourEmail,
                false
            );

            var permissionGroupsClient = new PermissionGroupsClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            string permissionGroupName = Guid.NewGuid().ToString();

            Response<Result> createPermissionGroupResponse =
                await permissionGroupsClient.PermissiongroupsPostAsync(
                    new CreatePermissionGroupCommand
                    {
                        Name = permissionGroupName
                    }
                );

            Response<ICollection<PermissionGroup>> createdPermissionGroupIdResult =
                await permissionGroupsClient.QueryAsync(new PermissionGroupWhere { Name = permissionGroupName });

            string createdPermissionGroupId = createdPermissionGroupIdResult.Result.FirstOrDefault().Id;

            Response<Result> addPermissionToPermissionGroupResult = await permissionGroupsClient.AddPostAsync(
                createdPermissionGroupId,
                new AddPermissionToPermissionGroupCommand
                {
                    PermissionId = "clientId",
                    TargetId = "all",
                    AddedById = Guid.NewGuid().ToString()
                }
            );

            var permissionClient = new PermissionClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            Response<Result> addToPermissionGroupResult =
                await permissionClient.PermissionsPostAsync(
                    createdLoginId,
                    new List<AddTargettedPermissionCommand>{
                        new()
                        {
                            Type = "groups",
                            Value = createdPermissionGroupId,
                            AddedById = Guid.NewGuid().ToString()
                        }
                    }
                );

            Response<Result> forgotPasswordResult = await loginClient.ForgotPasswordAsync(new ForgotPasswordCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Username = username
            });

            forgotPasswordResult.StatusCode.Should().Be(200);
            forgotPasswordResult.Result.Status.Should().Be("success");

            // At this point you should receive `forgot password` letter to yourEmail.
        }

        [Fact(Skip = "Can be used only for debug since the code is sent by email.")]
        public async Task GIVEN_login_with_permission_clientId_all_WHEN_request_forgotPassword_to_admin_clientId_THEN_he_receives_the_letter()
        {
            var appClient = new AppClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            await appClient.UpdateAsync(UserCredentials.Admin.ClientId, new UpdateAppCommand()
            {
                RedirectUris = new List<string> { "https://test.covergo.com", "https://test.covergo.com" },
                IsRedirectUrisChanged = true
            });

            var loginClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string yourEmail = "<EMAIL>";

            string createdLoginId = await CreateLoginAndReturnLoginIdAsync(
                UserCredentials.Admin.TenantId,
                username,
                "password",
                new List<string> { },
                yourEmail,
                false
            );

            var permissionClient = new PermissionClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            Response<Result> addToPermissionGroupResult =
                await permissionClient.PermissionsPostAsync(
                    createdLoginId,
                    new List<AddTargettedPermissionCommand>{
                        new()
                        {
                            Type = "clientId",
                            Value = "all",
                            AddedById = Guid.NewGuid().ToString()
                        }
                    }
                );

            Response<Result> forgotPasswordResult = await loginClient.ForgotPasswordAsync(new ForgotPasswordCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Username = username
            });

            forgotPasswordResult.StatusCode.Should().Be(200);
            forgotPasswordResult.Result.Status.Should().Be("success");

            // At this point you should receive `forgot password` letter to yourEmail.
        }

        [Theory]
        [AutoData]
        public async Task GIVEN_dlvn_tenant_WHEN_updating_user_name_THEN_password_is_reset_automaticaly_to_be_same_as_user_name(string client)
        {
            string tenantId = "covergo_dlvn";
            string clientId = $"dlvnApp{client}".Replace("-", "");

            var appClient = new AppClient(Setup.BuildHttpClient(tenantId));
            Response<Result> appResult = await appClient.CreateAsync(new CreateAppCommand()
            {
                AppId = clientId,
            });
            appResult.StatusCode.Should().Be(200);
            appResult.Result.Status.Should().Be("success");

            var loginClient = new AuthClient(Setup.BuildHttpClient(tenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";
            string email = "<EMAIL>";


            string createdLoginId = await CreateLoginAndReturnLoginIdAsync(tenantId, username, password, new List<string> { clientId }, email);

            string newUserName = Guid.NewGuid().ToString();

            Response<Result> updateResult = await loginClient.UpdateAsync(createdLoginId, new UpdateLoginCommand()
            {
                UserName = newUserName,
                IsUserNameChanged = true,
            });
            updateResult.StatusCode.Should().Be(200);
            updateResult.Result.Status.Should().Be("success");

            string accessToken = await GetAccessTokenAsync(tenantId, clientId, newUserName, newUserName);
            accessToken.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_login_WHEN_deactivate_THEN_doesActiveLoginExistByEmail_return_false()
        {
            string email = Guid.NewGuid().ToString();
            string loginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, email);

            GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

            await client.Authorize();

            string mutation = new _service_CoverGoMutationsRootBuilder().loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();

            auth_Result result = await client.SendMutationAsync<auth_Result>(mutation);
            result.isSuccess.Should().BeTrue();

            string query = new _service_CoverGoQueriesRootBuilder().loginInternalQueryDoesActiveLoginExistByEmail(
            KnownTenants.CoverGo, email).Build();

            GraphQLResponse<JToken> response = await client.SendQueryAsync<JToken>(new GraphQLHttpRequest(query));
            bool isActive = response.Data["loginInternalQueryDoesActiveLoginExistByEmail"].Value<bool>();
            isActive.Should().BeFalse();
        }

        [Fact]
        public async Task GIVEN_login_WHEN_deactivate_THEN_login_is_active_is_false()
        {
            string email = Guid.NewGuid().ToString();
            string loginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, email);

            GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

            await client.Authorize();

            string mutation = new _service_CoverGoMutationsRootBuilder().loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();

            auth_Result result = await client.SendMutationAsync<auth_Result>(mutation);
            result.isSuccess.Should().BeTrue();

            AuthClient loginClient = new AuthClient(Setup.BuildHttpClient(KnownTenants.CoverGo));
            Response<ICollection<Login>> logins = await loginClient.FilterAsync(new LoginQueryArguments
            {
                Where = new LoginWhere
                {
                    Ids = new List<string>
                    {
                        loginId
                    }
                }
            });

            logins.Result.First().IsActive.Should().BeFalse();
        }

        [Fact]
        public async Task GIVEN_login_WHEN_email_already_exists_THEN_login_is_not_created()
        {
            string email = Guid.NewGuid().ToString();
            await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, email);

            Response<CreatedStatusResult> createLoginResult = await CreateLoginAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, email);

            createLoginResult.Result.IsSuccess.Should().Be(false);
            createLoginResult.Result.Errors.Count().Should().Be(1);
            createLoginResult.Result.Errors.Contains("User with same email address already exists.");

        }

        [Fact]
        public async Task GIVEN_login_WHEN_email_not_exists_THEN_login_is_created()
        {
            string email = Guid.NewGuid().ToString();
            await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, email);

            string newEmail = Guid.NewGuid().ToString();
            Response<CreatedStatusResult> createLoginResult = await CreateLoginAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, newEmail);

            createLoginResult.Result.IsSuccess.Should().Be(true);
            createLoginResult.Result.Value.Id.Should().NotBe(null);

        }

        [Fact]
        public async Task GIVEN_login_WHEN_username_already_exists_THEN_login_is_not_created()
        {
            string username = Guid.NewGuid().ToString();
            await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username, null, new List<string> { UserCredentials.Admin.ClientId }, Guid.NewGuid().ToString());

            Response<CreatedStatusResult> createLoginResult = await CreateLoginAsync(UserCredentials.Admin.TenantId, username, null, new List<string> { UserCredentials.Admin.ClientId }, Guid.NewGuid().ToString());

            createLoginResult.Result.IsSuccess.Should().Be(false);
            createLoginResult.Result.Errors.Count().Should().Be(1);
            createLoginResult.Result.Errors.Contains("User with same username already exists.");

        }

        [Fact]
        public async Task GIVEN_login_WHEN_username_not_exists_THEN_login_is_not_created()
        {
            string username = Guid.NewGuid().ToString();
            await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username, null, new List<string> { UserCredentials.Admin.ClientId }, Guid.NewGuid().ToString());

            string newUsername = Guid.NewGuid().ToString();
            Response<CreatedStatusResult> createLoginResult = await CreateLoginAsync(UserCredentials.Admin.TenantId, newUsername, null, new List<string> { UserCredentials.Admin.ClientId }, Guid.NewGuid().ToString());

            createLoginResult.Result.IsSuccess.Should().Be(true);
            createLoginResult.Result.Value.Id.Should().NotBe(null);

        }

        [Fact]
        public async Task GIVEN_deactivatedLogin_WHEN_query_deactivatedLogins_THEN_deactivatedLogin_is_returned()
        {
            string email = Guid.NewGuid().ToString();
            string loginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, email);

            GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

            await client.Authorize();

            string mutation = new _service_CoverGoMutationsRootBuilder().loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();

            auth_Result result = await client.SendMutationAsync<auth_Result>(mutation);
            result.isSuccess.Should().BeTrue();

            AuthClient loginClient = new AuthClient(Setup.BuildHttpClient(KnownTenants.CoverGo));
            Response<ICollection<Login>> logins = await loginClient.FilterAsync(new LoginQueryArguments
            {
                Where = new LoginWhere
                {
                    IsActive = false,
                    ExcludePermissions = true
                }
            });

            logins.Result.Select(l => l.Id).Should().Contain(loginId);
        }

        [Fact]
        public async Task GIVEN_deactivated_login_WHEN_reactivate_THEN_doesActiveLoginExistByEmail_return_true()
        {
            string email = Guid.NewGuid().ToString();
            string loginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, email);

            GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

            await client.Authorize();

            string deactivateMutation = new _service_CoverGoMutationsRootBuilder().loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();
            await client.SendMutationAsync<auth_Result>(deactivateMutation);

            string reactivateMutation = new _service_CoverGoMutationsRootBuilder().loginMutationReactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();

            auth_Result result = await client.SendMutationAsync<auth_Result>(reactivateMutation);
            result.isSuccess.Should().BeTrue();

            string query = new _service_CoverGoQueriesRootBuilder().loginInternalQueryDoesActiveLoginExistByEmail(
            KnownTenants.CoverGo, email).Build();

            GraphQLResponse<JToken> response = await client.SendQueryAsync<JToken>(new GraphQLHttpRequest(query));
            bool isActive = response.Data["loginInternalQueryDoesActiveLoginExistByEmail"].Value<bool>();
            isActive.Should().BeTrue();
        }

        [Fact]
        public async Task GIVEN_deactivated_login_WHEN_reactivate_THEN_login_is_active_is_true()
        {
            string email = Guid.NewGuid().ToString();
            string loginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, email);

            GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

            await client.Authorize();

            string deactivateMutation = new _service_CoverGoMutationsRootBuilder().loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();
            await client.SendMutationAsync<auth_Result>(deactivateMutation);

            JToken noti = new JObject();
            noti["emailMessage"] = new JObject();
            noti["emailMessage"]["to"] = email;
            var notiObj = noti.ToObject<auth_SendReactivateNotificationCommandInput>();

            string reactivateMutation = new _service_CoverGoMutationsRootBuilder().loginMutationReactivate(
                new(
                    loginId,
                    reactivateNotificationCommand: notiObj),
                new auth_ResultBuilder().WithAllFields()).Build();

            auth_Result result = await client.SendMutationAsync<auth_Result>(reactivateMutation);
            result.isSuccess.Should().BeTrue();

            AuthClient loginClient = new AuthClient(Setup.BuildHttpClient(KnownTenants.CoverGo));
            Response<ICollection<Login>> logins = await loginClient.FilterAsync(new LoginQueryArguments
            {
                Where = new LoginWhere
                {
                    Ids = new List<string>
                    {
                        loginId
                    }
                }
            });

            logins.Result.First().IsActive.Should().BeTrue();
        }

        [Fact]
        public async Task GIVEN_reactivate_login_WHEN_query_active_logins_THEN_reactivatedLogin_is_returned()
        {
            string email = Guid.NewGuid().ToString();
            string loginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId }, email);

            GraphQLHttpClient client =
            new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
                new NewtonsoftJsonSerializer());

            await client.Authorize();

            string deactivateMutation = new _service_CoverGoMutationsRootBuilder().loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();
            await client.SendMutationAsync<auth_Result>(deactivateMutation);

            string reactivateMutation = new _service_CoverGoMutationsRootBuilder().loginMutationReactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();

            auth_Result result = await client.SendMutationAsync<auth_Result>(reactivateMutation);
            result.isSuccess.Should().BeTrue();

            AuthClient loginClient = new AuthClient(Setup.BuildHttpClient(KnownTenants.CoverGo));
            Response<ICollection<Login>> logins = await loginClient.FilterAsync(new LoginQueryArguments
            {
                Where = new LoginWhere
                {
                    IsActive = true,
                    ExcludePermissions = true
                }
            });

            logins.Result.Select(l => l.Id).Should().Contain(loginId);
        }

        [Fact]
        public async Task GIVEN_multiple_login_actions_WHEN_last_login_times_are_queried_THEN_last_login_times_are_received()
        {
            string username1 = Guid.NewGuid().ToString();
            string password1 = "password1";
            string username2 = Guid.NewGuid().ToString();
            string password2 = "password2";

            string userId1 = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username1, password1, new List<string> { UserCredentials.Admin.ClientId });
            string userId2 = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username2, password2, new List<string> { UserCredentials.Admin.ClientId });

            await GetAccessTokenAsync(UserCredentials.Admin.TenantId, UserCredentials.Admin.ClientId, username1, password1);
            await GetAccessTokenAsync(UserCredentials.Admin.TenantId, UserCredentials.Admin.ClientId, username2, password2);

            var now = DateTime.UtcNow;
            await GetAccessTokenAsync(UserCredentials.Admin.TenantId, UserCredentials.Admin.ClientId, username1, password1);
            await GetAccessTokenAsync(UserCredentials.Admin.TenantId, UserCredentials.Admin.ClientId, username2, password2);

            var loginClient = new AuthV2Client(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            var loginEventType = LoginEventType.RequestAccessTokenSuccess;
            Response<ICollection<DetailedEventLog>> lastEvents = await loginClient.EventsAsync(new()
            {
                Where = new()
                {
                    LoginIds = new string[] { userId1, userId2 },
                    Types = new LoginEventType[] { loginEventType }
                },
                GroupBy = new() { FieldName = "loginId" },
                OrderBy = new() { FieldName = "timestamp", Type = OrderByType.DSC }
            });

            lastEvents.Result.Should().NotBeNull();
            lastEvents.Result.Count.Should().Be(2);
            lastEvents.Result.Should().Satisfy(eventLog => eventLog.RelatedId == userId1 && eventLog.Timestamp.UtcDateTime >= now,
                eventLog => eventLog.RelatedId == userId2 && eventLog.Timestamp.UtcDateTime >= now);
        }

        [Fact]
        public async Task GIVEN_deactivated_login_WHEN_get_login_by_id_THEN_loading_permissions_is_skipped()
        {
            string loginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, Guid.NewGuid().ToString(), null, new List<string> { UserCredentials.Admin.ClientId });

            GraphQLHttpClient client = new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
               new NewtonsoftJsonSerializer());

            await client.Authorize();

            string deactivateMutation = new _service_CoverGoMutationsRootBuilder().loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();
            await client.SendMutationAsync<auth_Result>(deactivateMutation);

            AuthClient loginClient = new(Setup.BuildHttpClient(KnownTenants.CoverGo));
            Response<Login> login = await loginClient.FromLoginIdAsync(loginId, null, null);
            login.Result.TargettedPermissions.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task GIVEN_deactivated_login_WHEN_get_access_token_THEN_unable_to_get_access_token()
        {
            string username = Guid.NewGuid().ToString();
            string password = "password1";
            string loginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username, password, new List<string> { UserCredentials.Admin.ClientId });

            GraphQLHttpClient client = new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
               new NewtonsoftJsonSerializer());

            await client.Authorize();

            string deactivateMutation = new _service_CoverGoMutationsRootBuilder().loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();
            await client.SendMutationAsync<auth_Result>(deactivateMutation);

            string accessToken = await GetAccessTokenAsync(UserCredentials.Admin.TenantId, UserCredentials.Admin.ClientId, username, password);
            accessToken.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_deactivated_login_WHEN_get_refresh_token_THEN_unable_to_get_access_token()
        {
            string username = Guid.NewGuid().ToString();
            string password = "password1";
            string loginId = await CreateLoginAndReturnLoginIdAsync(UserCredentials.Admin.TenantId, username, password, new List<string> { UserCredentials.Admin.ClientId });

            GraphQLHttpClient client = new(Environment.GetEnvironmentVariable("GATEWAY_URL") ?? "http://localhost:60060/graphql",
               new NewtonsoftJsonSerializer());

            await client.Authorize();

            HttpClient tokenClient = Setup.BuildHttpClient(UserCredentials.Admin.TenantId);
            TokenResponse tokenResponse = await tokenClient.RequestPasswordTokenAsync(
                new PasswordTokenRequest
                {
                    Address = "connect/token",
                    ClientId = UserCredentials.Admin.ClientId,
                    UserName = username,
                    Password = password,
                    Scope = "custom_profile offline_access"
                }
            );

            string deactivateMutation = new _service_CoverGoMutationsRootBuilder().loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()).Build();
            await client.SendMutationAsync<auth_Result>(deactivateMutation);

            TokenResponse refreshTokenResponse = await tokenClient.RequestRefreshTokenAsync(
             new RefreshTokenRequest
             {
                 Address = "connect/token",
                 ClientId = UserCredentials.Admin.ClientId,
                 RefreshToken = tokenResponse.RefreshToken,
                 Scope = "custom_profile offline_access"
             }
            );
            refreshTokenResponse.AccessToken.Should().BeNullOrEmpty();
        }

        private async Task<string> CreateLoginAndReturnLoginIdAsync(string tenantId, string userName, string password, ICollection<string> appIds, string email = null, bool? useDefaultPermissions = null)
        {
            HttpClient httpClient = Setup.BuildHttpClient(tenantId);
            AuthClient loginClient = new AuthClient(httpClient);
            Response<CreatedStatusResult> createLoginResponse = await CreateLoginAsync(tenantId,userName,password,appIds,email,useDefaultPermissions);

            createLoginResponse.Result.Status.Should().Be("success");
            return createLoginResponse.Result.Value.Id;
        }

        private async Task<Response<CreatedStatusResult>> CreateLoginAsync(string tenantId, string userName, string password, ICollection<string> appIds, string email = null, bool? useDefaultPermissions = null)
        {
            HttpClient httpClient = Setup.BuildHttpClient(tenantId);
            AuthClient loginClient = new AuthClient(httpClient);
            Response<CreatedStatusResult> createLoginResponse = await loginClient.LoginsAsync(new CreateLoginCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Username = userName,
                Password = password,
                Email = email ?? Guid.NewGuid().ToString(),
                IgnorePasswordValidation = true,
                IsEmailConfirmed = true,
                AppIdsToBeGrantedAccessTo = appIds,
                UseDefaultPermissions = useDefaultPermissions
            });

            return createLoginResponse;
        }

        private static async Task<string> GetAccessTokenAsync(string tenantId, string clientId, string username, string password)
        {
            HttpClient tokenClient = Setup.BuildHttpClient(tenantId);
            TokenResponse tokenResponse = await tokenClient.RequestPasswordTokenAsync(
                new PasswordTokenRequest
                {
                    Address = "connect/token",
                    ClientId = clientId,
                    UserName = username,
                    Password = password,
                    Scope = "custom_profile offline_access"
                }
            );
            return tokenResponse.AccessToken;
        }
    }
}
