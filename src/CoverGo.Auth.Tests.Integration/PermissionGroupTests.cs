﻿using CoverGo.Auth.Client;
using CoverGo.Auth.Infrastructure.Services;
using FluentAssertions;
using IdentityModel.Client;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using TokenResponse = IdentityModel.Client.TokenResponse;

namespace CoverGo.Auth.Tests.Integration
{
    public class PermissionGroupTests
    {
        [Fact]
        public async Task GIVEN_login_AND_permissiongroup_with_no_permissions_WHEN_request_token_THEN_it_is_successful()
        {
            var loginClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";

            Response<CreatedStatusResult> createLoginResponse = await loginClient.LoginsAsync(new CreateLoginCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Username = username,
                Password = password,
                IgnorePasswordValidation = true,
                IsEmailConfirmed = true,
                AppIdsToBeGrantedAccessTo = new List<string> { UserCredentials.Admin.ClientId }
            });

            createLoginResponse.StatusCode.Should().Be(200);
            createLoginResponse.Result.Errors.Should().BeNullOrEmpty();

            string createdLoginId = createLoginResponse.Result.Value.Id;

            var permissionGroupsClient = new PermissionGroupsClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            string permissionGroupName = Guid.NewGuid().ToString();
            Response<Result> createPermissionGroupResponse =
                await permissionGroupsClient.PermissiongroupsPostAsync(new CreatePermissionGroupCommand
                {
                    Name = permissionGroupName
                });
            createPermissionGroupResponse.StatusCode.Should().Be(200);
            createPermissionGroupResponse.Result.Errors.Should().BeNullOrEmpty();

            Response<ICollection<PermissionGroup>> createdPermissionGroupIdResult = await permissionGroupsClient.QueryAsync(new PermissionGroupWhere { Name = permissionGroupName });

            createdPermissionGroupIdResult.StatusCode.Should().Be(200);
            string createdPermissionGroupId = createdPermissionGroupIdResult.Result.FirstOrDefault().Id;

            var permissionClient = new PermissionClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> addToPermissionGroupResult =
                await permissionClient.PermissionsPostAsync(
                    createdLoginId,
                    new List<AddTargettedPermissionCommand>{new()
                    {
                        Type = "groups",
                        Value = createdPermissionGroupId,
                        AddedById = Guid.NewGuid().ToString()
                    }});

            addToPermissionGroupResult.StatusCode.Should().Be(200);
            addToPermissionGroupResult.Result.Errors.Should().BeNullOrEmpty();

            HttpClient client = Setup.BuildHttpClient(UserCredentials.Admin.TenantId);

            TokenResponse tokenResponse = await client.RequestPasswordTokenAsync(
                new PasswordTokenRequest
                {
                    Address = "connect/token",
                    ClientId = UserCredentials.Admin.ClientId,
                    UserName = username,
                    Password = password,
                    Scope = "custom_profile offline_access"
                }
            );

            tokenResponse.AccessToken.Should().NotBeNull();

            ValidatePermissionGroupsClaim(tokenResponse);
        }

        [Fact]
        public async Task GIVEN_login_AND_permissiongroup_WHEN_get_login_permissions_THEN_it_contains_role_type_with_group_name_value()
        {
            AuthClient loginClient = new(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";

            Response<CreatedStatusResult> createLoginResponse = await loginClient.LoginsAsync(new CreateLoginCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Username = username,
                Password = password,
                IgnorePasswordValidation = true,
                IsEmailConfirmed = true,
                AppIdsToBeGrantedAccessTo = new List<string> { UserCredentials.Admin.ClientId }
            });

            createLoginResponse.StatusCode.Should().Be(200);
            createLoginResponse.Result.Errors.Should().BeNullOrEmpty();

            string createdLoginId = createLoginResponse.Result.Value.Id;

            PermissionGroupsClient permissionGroupsClient = new(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            string permissionGroupName = Guid.NewGuid().ToString();
            Response<Result> createPermissionGroupResponse =
                await permissionGroupsClient.PermissiongroupsPostAsync(new CreatePermissionGroupCommand
                {
                    Name = permissionGroupName
                });
            createPermissionGroupResponse.StatusCode.Should().Be(200);
            createPermissionGroupResponse.Result.Errors.Should().BeNullOrEmpty();

            Response<ICollection<PermissionGroup>> createdPermissionGroupIdResult = await permissionGroupsClient.QueryAsync(new PermissionGroupWhere { Name = permissionGroupName });

            createdPermissionGroupIdResult.StatusCode.Should().Be(200);
            string createdPermissionGroupId = createdPermissionGroupIdResult.Result.FirstOrDefault().Id;

            PermissionClient permissionClient = new(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            await permissionClient.PermissionsPostAsync(
                    createdLoginId,
                    new List<AddTargettedPermissionCommand>{new()
                    {
                        Type = "groups",
                        Value = createdPermissionGroupId,
                        AddedById = Guid.NewGuid().ToString()
                    }});

            Response<ICollection<Login>> logins = await loginClient.FilterAsync(new LoginQueryArguments
            {
                Where = new LoginWhere
                {
                    Ids = new List<string>
                    {
                        createdLoginId
                    }
                },
            });

            Login login = logins.Result.Single();

            login.TargettedPermissions.FirstOrDefault(r => r.Key == "role").Value.Should().Contain(permissionGroupName);
        }


        [Fact]
        public async Task GIVEN_login_AND_permissiongroup_WHEN_get_login_permissions_V2_THEN_it_contains_role_type_with_group_name_value()
        {
            AuthClient loginClient = new(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";

            Response<CreatedStatusResult> createLoginResponse = await loginClient.LoginsAsync(new CreateLoginCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Username = username,
                Password = password,
                IgnorePasswordValidation = true,
                IsEmailConfirmed = true,
                AppIdsToBeGrantedAccessTo = new List<string> { UserCredentials.Admin.ClientId }
            });

            createLoginResponse.StatusCode.Should().Be(200);
            createLoginResponse.Result.Errors.Should().BeNullOrEmpty();

            string createdLoginId = createLoginResponse.Result.Value.Id;

            PermissionGroupsClient permissionGroupsClient = new(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            string permissionGroupName = Guid.NewGuid().ToString();
            Response<Result> createPermissionGroupResponse =
                await permissionGroupsClient.PermissiongroupsPostAsync(new CreatePermissionGroupCommand
                {
                    Name = permissionGroupName
                });
            createPermissionGroupResponse.StatusCode.Should().Be(200);
            createPermissionGroupResponse.Result.Errors.Should().BeNullOrEmpty();

            Response<ICollection<PermissionGroup>> createdPermissionGroupIdResult = await permissionGroupsClient.QueryAsync(new PermissionGroupWhere { Name = permissionGroupName });

            createdPermissionGroupIdResult.StatusCode.Should().Be(200);
            string createdPermissionGroupId = createdPermissionGroupIdResult.Result.FirstOrDefault().Id;

            PermissionClient permissionClient = new(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            await permissionClient.PermissionsPostAsync(
                    createdLoginId,
                    new List<AddTargettedPermissionCommand>{new()
                    {
                        Type = "groups",
                        Value = createdPermissionGroupId,
                        AddedById = Guid.NewGuid().ToString()
                    }});

            Login login = (await loginClient.FromLoginIdAsync(createdLoginId, UserCredentials.Admin.ClientId, 2)).Result;

            login.TargettedPermissions.FirstOrDefault(r => r.Key == "role").Value.Should().Contain(permissionGroupName);
        }

        static void ValidatePermissionGroupsClaim(TokenResponse tokenResponse)
        {
            JwtSecurityTokenHandler tokenHandler = new ();
            JwtSecurityToken securityToken = (JwtSecurityToken)tokenHandler.ReadToken(tokenResponse.AccessToken);
            securityToken.Should().NotBeNull();
            securityToken.Claims.Should().NotBeEmpty();
            securityToken.Claims.Should().Contain(c => c.Type == CustomProfileService.PermissionGroupsClaimType);
        }
    }
}
