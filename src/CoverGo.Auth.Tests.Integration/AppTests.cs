﻿using AutoFixture.Xunit2;
using CoverGo.Auth.Client;
using FluentAssertions;
using FluentAssertions.Json;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Auth.Tests.Integration
{
    public class AppTests
    {
        private const string TestAppConfig = @"{
    emails: {
        emailFrom: ""<EMAIL>"",
        emailSubject: ""Set your password""
    },
    dateFormat: ""DD/MM/YYYY"",
    dateTimeFormat: ""DD/MM/YYYY HH:mm:ss"",
    name: ""CoverGo Test"",
    logoUrl: ""/logo.svg"",
    fileSystem: {
        bucketName: ""covergo-test""
    },
    loginMethods: {
        email: {
            enabled: true
        },
        openIdConnectAuthCodeFlow: {
            enabled: true
        }
    }
}";

        [Fact]
        public async Task GIVEN_ClientId_WHEN_updating_App_THEN_appConfig_is_updated()
        {
            var appClient = new AppClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> appResult = await appClient.UpdateAsync(UserCredentials.Admin.ClientId, new UpdateAppCommand()
            {
                AppConfig = TestAppConfig,
                IsAppConfigChanged = true,
            });
            appResult.StatusCode.Should().Be(200);
            appResult.Result.Status.Should().Be("success");

            Response<ICollection<App>> appResponse = await appClient.QueryAsync(new AppQueryArguments()
            {
                Where = new AppWhere()
                {
                    AppId = UserCredentials.Admin.ClientId
                }
            });
            appResponse.StatusCode.Should().Be(200);
            App app = appResponse.Result.FirstOrDefault();
            app.Should().NotBeNull();
            JToken.Parse(app.AppConfig).Should().BeEquivalentTo(JToken.Parse(TestAppConfig));
        }

        [Theory]
        [AutoData]
        public async Task GIVEN_clientId_appConfig_WHEN_creatingApp_THEN_appConfig_is_persisted(string clientId)
        {
            var appClient = new AppClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> appResult = await appClient.CreateAsync(new CreateAppCommand()
            {
                AppId = clientId,
                AppConfig = TestAppConfig,
            });
            appResult.StatusCode.Should().Be(200);
            appResult.Result.Status.Should().Be("success");

            Response<ICollection<App>> appResponse = await appClient.QueryAsync(new AppQueryArguments()
            {
                Where = new AppWhere()
                {
                    AppId = clientId
                }
            });
            appResponse.StatusCode.Should().Be(200);
            App app = appResponse.Result.FirstOrDefault();
            app.Should().NotBeNull();
            JToken.Parse(app.AppConfig).Should().BeEquivalentTo(JToken.Parse(TestAppConfig));
        }

        [Theory]
        [AutoData]
        public async Task GIVEN_ClientId_WHEN_updating_App_with_host_in_tenant_THEN_url_routing_url_is_updated(Uri hostUri)
        {
            var authClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> authResult = await authClient.AddPostAsync(hostUri.Host);
            authResult.StatusCode.Should().Be(200);
            authResult.Result.Status.Should().Be("success");
            var appClient = new AppClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> appResult = await appClient.UpdateAsync(UserCredentials.Admin.ClientId, new UpdateAppCommand()
            {
                UrlRouting = new UrlRoutingToUpdate()
                {
                    Url = hostUri.ToString(),
                    IsUrlChanged = true,
                },
                IsUrlRoutingChanged = true,
            });
            appResult.StatusCode.Should().Be(200);
            appResult.Result.Status.Should().Be("success");

            Response<ICollection<App>> appResponse = await appClient.QueryAsync(new AppQueryArguments()
            {
                Where = new AppWhere()
                {
                    AppId = UserCredentials.Admin.ClientId
                }
            });
            appResponse.StatusCode.Should().Be(200);
            App app = appResponse.Result.FirstOrDefault();
            app.Should().NotBeNull();
            app.UrlRouting.Url.Should().BeEquivalentTo(hostUri.ToString());
        }


        [Theory]
        [AutoData]
        public async Task GIVEN_ClientId_WHEN_updating_host_with_host_not_in_tenant_THEN_error(Uri hostUri)
        {
            var appClient = new AppClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> appResult = await appClient.UpdateAsync(UserCredentials.Admin.ClientId, new UpdateAppCommand()
            {
                UrlRouting = new UrlRoutingToUpdate()
                {
                    Url = hostUri.ToString(),
                    IsUrlChanged = true,
                },
                IsUrlRoutingChanged = true,
            });
            appResult.StatusCode.Should().Be(200);
            appResult.Result.Status.Should().Be("failure");
            appResult.Result.Errors.Should().Contain($"URL routing host for URL {hostUri} is not defined in the tenant {UserCredentials.Admin.TenantId}. Applications can use only hosts that are also defined in the tenant.");
        }

        [Theory]
        [AutoData]
        public async Task GIVEN_clientId_and_host_url_in_tenant_WHEN_creatingApp_THEN_app_is_created(string clientId, Uri hostUri)
        {
            var authClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> authResult = await authClient.AddPostAsync(hostUri.Host);
            authResult.StatusCode.Should().Be(200);
            authResult.Result.Status.Should().Be("success");
            var appClient = new AppClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> appResult = await appClient.CreateAsync(new CreateAppCommand()
            {
                AppId = clientId,
                UrlRouting = new UrlRouting()
                {
                    Url = hostUri.ToString(),
                },
            });
            appResult.StatusCode.Should().Be(200);
            appResult.Result.Status.Should().Be("success");

            Response<ICollection<App>> appResponse = await appClient.QueryAsync(new AppQueryArguments()
            {
                Where = new AppWhere()
                {
                    AppId = clientId
                }
            });
            appResponse.StatusCode.Should().Be(200);
            App app = appResponse.Result.FirstOrDefault();
            app.Should().NotBeNull();
            app.UrlRouting.Url.Should().BeEquivalentTo(hostUri.ToString());
        }

        [Theory]
        [AutoData]
        public async Task GIVEN_clientId_and_host_url_not_in_tenant_WHEN_creatingApp_THEN_error(string clientId, Uri hostUri)
        {
            var appClient = new AppClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            Response<Result> appResult = await appClient.CreateAsync(new CreateAppCommand()
            {
                AppId = clientId,
                UrlRouting = new UrlRouting()
                {
                    Url = hostUri.ToString(),
                },
            });
            appResult.StatusCode.Should().Be(200);
            appResult.Result.Status.Should().Be("failure");
            appResult.Result.Errors.Should().Contain($"URL routing host for URL {hostUri} is not defined in the tenant {UserCredentials.Admin.TenantId}. Applications can use only hosts that are also defined in the tenant.");
        }
    }
}
