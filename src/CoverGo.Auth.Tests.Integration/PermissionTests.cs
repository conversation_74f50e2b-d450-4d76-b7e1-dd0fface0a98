﻿using CoverGo.Auth.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Auth.Tests.Integration
{
    public class PermissionTests
    {
        [Fact]
        public async Task GIVEN_login_WHEN_add_permission_THEN_it_is_added()
        {
            var loginClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";

            Response<CreatedStatusResult> createLoginResponse = await loginClient.LoginsAsync(new CreateLoginCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Username = username,
                Password = password,
                IgnorePasswordValidation = true,
                IsEmailConfirmed = true,
                AppIdsToBeGrantedAccessTo = new List<string> { UserCredentials.Admin.ClientId }
            });

            string createdLoginId = createLoginResponse.Result.Value.Id;

            var permissionClient = new PermissionClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            string testValue = Guid.NewGuid().ToString();

            Response<Result> addPermissionResult = await permissionClient.PermissionsPostAsync(createdLoginId, new List<AddTargettedPermissionCommand> {
                new AddTargettedPermissionCommand
                {
                    Type = "readPolicies",
                    Value = testValue,
                    AddedById = "addedByLoginId"
                }
            });

            addPermissionResult.StatusCode.Should().Be(200);
            addPermissionResult.Result.Status.Should().Be("success");

            Login login = (await loginClient.FilterAsync(new LoginQueryArguments { Where = new LoginWhere { Ids = new List<string> { createdLoginId } } })).Result.FirstOrDefault();

            login.TargettedPermissions.Should().ContainKey("readPolicies");
            KeyValuePair<string, ICollection<string>> readPoliciesClaim = login.TargettedPermissions.FirstOrDefault(k => k.Key == "readPolicies");
            readPoliciesClaim.Value.Should().Contain(testValue);
        }

        [Fact]
        public async Task GIVEN_login_with_permissions_WHEN_remove_permission_deprecated_THEN_it_is_removed()
        {
            var loginClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";

            Response<CreatedStatusResult> createLoginResponse = await loginClient.LoginsAsync(new CreateLoginCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Username = username,
                Password = password,
                IgnorePasswordValidation = true,
                IsEmailConfirmed = true,
                AppIdsToBeGrantedAccessTo = new List<string> { UserCredentials.Admin.ClientId }
            });

            string createdLoginId = createLoginResponse.Result.Value.Id;

            var permissionClient = new PermissionClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            string testValue = Guid.NewGuid().ToString();

            Response<Result> addPermissionResult = await permissionClient.PermissionsPostAsync(createdLoginId, new List<AddTargettedPermissionCommand> {
                new AddTargettedPermissionCommand
                {
                    Type = "readPolicies",
                    Value = testValue,
                    AddedById = "addedByLoginId"
                }
            });

            Response<Result> removePermissionResult = await permissionClient.PermissionsDeleteAsync(createdLoginId, "readPolicies", testValue, "removedByLoginId");

            removePermissionResult.StatusCode.Should().Be(200);
            removePermissionResult.Result.Status.Should().Be("success");

            Login login = (await loginClient.FilterAsync(new LoginQueryArguments { Where = new LoginWhere { Ids = new List<string> { createdLoginId } } })).Result.FirstOrDefault();

            login.TargettedPermissions.Should().NotContain("readPolicies", new List<string> { testValue });
        }

        [Fact]
        public async Task GIVEN_login_with_permissions_WHEN_remove_permission_THEN_it_is_removed()
        {
            var loginClient = new AuthClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));
            string username = Guid.NewGuid().ToString();
            string password = "password";

            Response<CreatedStatusResult> createLoginResponse = await loginClient.LoginsAsync(new CreateLoginCommand
            {
                ClientId = UserCredentials.Admin.ClientId,
                Username = username,
                Password = password,
                IgnorePasswordValidation = true,
                IsEmailConfirmed = true,
                AppIdsToBeGrantedAccessTo = new List<string> { UserCredentials.Admin.ClientId }
            });

            string createdLoginId = createLoginResponse.Result.Value.Id;

            var permissionClient = new PermissionClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            string testValue = Guid.NewGuid().ToString();

            Response<Result> addPermissionResult = await permissionClient.PermissionsPostAsync(createdLoginId, new List<AddTargettedPermissionCommand> {
                new AddTargettedPermissionCommand
                {
                    Type = "readPolicies",
                    Value = testValue,
                    AddedById = "addedByLoginId"
                }
            });

            var permissionGroupsClient = new PermissionClient(Setup.BuildHttpClient(UserCredentials.Admin.TenantId));

            Response<Result> removePermissionResult = await permissionClient.RemoveAsync(createdLoginId, new List<RemoveTargettedPermissionCommand> { new RemoveTargettedPermissionCommand {
                Type = "readPolicies",
                Value = testValue,
                RemovedById = "removedByLoginId"
            } });

            removePermissionResult.StatusCode.Should().Be(200);
            removePermissionResult.Result.Status.Should().Be("success");

            Login login = (await loginClient.FilterAsync(new LoginQueryArguments { Where = new LoginWhere { Ids = new List<string> { createdLoginId } } })).Result.FirstOrDefault();

            login.TargettedPermissions.Should().NotContain("readPolicies", new List<string> { testValue });
        }
    }
}
