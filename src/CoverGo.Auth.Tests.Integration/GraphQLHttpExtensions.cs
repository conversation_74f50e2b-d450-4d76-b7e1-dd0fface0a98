﻿using GraphQL;
using GraphQL.Client.Http;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Auth.Tests.Integration;

public static class CoverGoGraphQlClientExtensions
{
    public static async Task<TResponse> SendMutationAsync<TResponse>(this GraphQLHttpClient client, string mutation)
        where TResponse : class
    {
        GraphQLResponse<JToken> response = await client.SendMutationAsync<JToken>(new GraphQLHttpRequest(mutation));
        Validate(response);
        return response.Data.First?.First?.ToObject<TResponse>()
            ?? throw new Exception("Cannot deserialize result");
    }

    public static async Task<TResponse> SendQueryAsync<TResponse>(this GraphQLHttpClient client, string query)
        where TResponse : class
    {
        GraphQLResponse<JToken> response = await client.SendQueryAsync<JToken>(new GraphQLHttpRequest(query));
        Validate(response);

        JToken responseObject = response.Data.First?.First;
        if (responseObject == null) {
            throw new Exception("No response");
        }

        TResponse parsedObject = responseObject.ToObject<TResponse>();
        if (responseObject.Type != JTokenType.Null && parsedObject == null) {
            throw new Exception("Cannot deserialize result");
        }

        return parsedObject;
    }

    static void Validate<TResponse>(GraphQLResponse<TResponse> response)
    {
        if (response.Errors?.Any() == true) {
            throw new Exception($"Error occured during the GraphQL request: {string.Join(";", response.Errors.Select(it => it.Message))}");
        }
    }

    public static async Task<GraphQLHttpClient> Authorize(this GraphQLHttpClient client, string tenantId = "covergo", string clientId = "admin", string username = "<EMAIL>", string password = "V9K&KobcZO3")
    {
        string tokenQuery = $@"{{ token_2(tenantId: ""{tenantId}"", clientId: ""{clientId}"", username:""{username}"", password: ""{password}""){{ accessToken }} }}";

        GraphQLResponse<JToken> tokenInfo = await client.SendQueryAsync<JToken>(new GraphQLHttpRequest(tokenQuery));
        string token = tokenInfo.Data["token_2"]?["accessToken"]?.ToString();

        if (string.IsNullOrEmpty(token))
            throw new Exception("Cannot get token");

        client.HttpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", $"Bearer {token}");

        return client;
    }
}