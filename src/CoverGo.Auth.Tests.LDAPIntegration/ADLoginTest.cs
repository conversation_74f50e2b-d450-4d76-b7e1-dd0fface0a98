﻿using FluentAssertions;
using IdentityModel.Client;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Auth.Tests.Integration
{
    public class ADLoginTest //NOTE: Add AD to docker-compose and move these tests back to run in pipeline
    {
        [Fact(Skip = "Add AD to docker-compose and move these tests back to run in pipeline")]
        public async Task GIVEN_AD_user_WHEN_requesting_access_token_with_credentials_THEN_receive_access_token()
        {
            HttpClient client = Setup.BuildHttpClient("dbs_uat");

            TokenResponse tokenResponse = await client.RequestPasswordTokenAsync(
               new PasswordTokenRequest
               {
                   Address = "connect/token",
                   ClientId = "test",
                   UserName = "adamb",
                   Password = "P@ss1W0Rd!",
                   Scope = "custom_profile offline_access"
               }
           );

            tokenResponse.AccessToken.Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_AD_user_WHEN_requesting_access_token_with_invalid_credentials_THEN_receive_null()
        {
            HttpClient client = Setup.BuildHttpClient("dbs_uat");

            TokenResponse tokenResponse = await client.RequestPasswordTokenAsync(
                new PasswordTokenRequest
                {
                    Address = "connect/token",
                    ClientId = "test",
                    UserName = "invalid",
                    Password = "wrongPassword",
                    Scope = "custom_profile offline_access"
                }
            );

            tokenResponse.AccessToken.Should().BeNullOrEmpty();
        }
    }
}
