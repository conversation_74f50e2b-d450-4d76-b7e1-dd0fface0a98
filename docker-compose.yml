version: '2.4'
services:
  covergo-gateway:
    image: ghcr.io/covergo/gateway:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - USE_HOTCHOCOLATE=true
    ports:
      - "60060:8080" # To access localhost:60060/graphql
    depends_on:
      covergo-auth:
        condition: service_started
      covergo-auth-health:
        condition: service_healthy
      covergo-scheduler:
        condition: service_started

  covergo-mongo:
    image: ghcr.io/covergo/auth-mongo:latest
    restart: always
    build:
      dockerfile:  ./Mongo.Dockerfile
      context: .
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=local_dev
      - MONGO_INITDB_DATABASE=auth
    ports:
      - 27017:27017

  covergo-auth:
    image: ghcr.io/covergo/auth:latest
    restart: always
    build:
      dockerfile: ./Dockerfile.bak
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - OTP_LOGIN_CIPHER_KEY=zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=
      - OTP_LOGIN_CIPHER_IV=94jCf53NO1acZ3pO7UE+gA==
      - OTP_LOGIN_HASHER_KEY=key
      - COVERGO_PASSWORD=V9K&KobcZO3
      - serviceUrls__users=http://covergo-auth-tests-integration:60010/
      - serviceUrls__notifications=http://covergo-auth-tests-integration:60070/
      - serviceUrls__policies=http://covergo-auth-tests-integration:60050/
      - serviceUrls__cases=http://covergo-auth-tests-integration:60600/
      - serviceUrls__transactions=http://covergo-auth-tests-integration:60120/
      - serviceUrls__advisor=http://covergo-auth-tests-integration:60110/
      - serviceUrls__claims=http://covergo-auth-tests-integration:60080/
      - serviceUrls__scheduler=http://covergo-scheduler:8080/
      - WAIT_HOSTS=covergo-mongo:27017
      - PROMETHEUS_ENABLED=false
    ports:
      - 8080:8080
    depends_on:
      - covergo-mongo

  covergo-scheduler:
    image: ghcr.io/covergo/scheduler:latest
    restart: always
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    depends_on:
      - covergo-mongo

  covergo-auth-health:
    image: busybox
    entrypoint: ["sleep","100m"]
    healthcheck:
      test: "wget http://covergo-auth:8080/hc -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30

  covergo-auth-tests-integration:
    image: ghcr.io/covergo/auth-test-integration:latest
    restart: always
    build:
      dockerfile: ./Dockerfile.bak
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
      target: run-tests-integration
    environment:
      - AUTH_INTEGRATION_TEST-AuthUrl=http://covergo-auth:8080
      - WAIT_HOSTS=covergo-auth:8080
      - GATEWAY_URL=http://covergo-gateway:8080/graphql
    depends_on:
      - covergo-gateway
